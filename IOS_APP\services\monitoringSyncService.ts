import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  getFirestore, 
  collection, 
  doc, 
  setDoc, 
  getDocs, 
  deleteDoc,
  addDoc,
  serverTimestamp,
  writeBatch 
} from 'firebase/firestore';
import { 
  ref, 
  uploadBytes, 
  getDownloadURL 
} from 'firebase/storage';
import { storage } from '@/config/firebase';
import NetInfo from '@react-native-community/netinfo';

export interface OfflineMonitoringSession {
  sessionId: string;
  protocol: string;
  startTime: string;
  endTime?: string;
  totalDistance: number;
  contactsCount: number;
  pathCoordinates: Array<{latitude: number, longitude: number}>;
  userId: string;
  status: 'active' | 'completed';
  deviceInfo: {
    platform: string;
  };
  lastUpdated: string;
}

export interface OfflineGPSPoint {
  sessionId: string;
  timestamp: string;
  latitude: number;
  longitude: number;
  accuracy?: number | null;
  altitude?: number | null;
  heading?: number | null;
  speed?: number | null;
  distance: number;
}

export interface OfflineContactEvent {
  sessionId: string;
  timestamp: string;
  observerLocation: {
    latitude: number;
    longitude: number;
  };
  contactLocation: {
    latitude: number;
    longitude: number;
  };
  distance: number;
  bearing?: number;
  contactNumber: number;
  circumstances: any;
  contactLocationDetails: any;
  images: string[];
  userId?: string;
  userName?: string;
  userRole?: string;
  protocol?: string;
  needsImageSync?: boolean;
  isReportMode?: boolean; // Flag to indicate if this contact is from a report
}

class MonitoringSyncService {
  private syncInProgress = false;
  private progressCallback: ((progress: number, message: string) => void) | null = null;

  // Set progress callback for UI updates
  setProgressCallback(callback: ((progress: number, message: string) => void) | null) {
    this.progressCallback = callback;
  }

  // Report progress to UI
  private reportProgress(progress: number, message: string) {
    if (this.progressCallback) {
      this.progressCallback(progress, message);
    }
  }

  // Check if there are images that need syncing
  public async hasImagesToSync(): Promise<boolean> {
    try {
      // Check technician contact events
      const offlineContactEvents = await AsyncStorage.getItem('offlineContactEvents');
      if (offlineContactEvents) {
        const events = JSON.parse(offlineContactEvents);
        const hasImages = events.some((event: any) => event.images && event.images.length > 0);
        if (hasImages) return true;
      }

      // Check gestor contact events
      const gestorContactEvents = await AsyncStorage.getItem('gestorOfflineContactEvents');
      if (gestorContactEvents) {
        const events = JSON.parse(gestorContactEvents);
        const hasImages = events.some((event: any) => event.images && event.images.length > 0);
        if (hasImages) return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking for images to sync:', error);
      return false;
    }
  }

  // Helper function to upload local images to Firebase Storage
  private async uploadImageToStorage(imageUri: string, userId: string, sessionId: string, contactNumber: number, imageIndex: number, isGestorMobile: boolean = false): Promise<string | null> {
    try {
      console.log(`📤 Background sync: Uploading image ${imageIndex + 1} for contact ${contactNumber}...`);
      
      // Skip if already a Firebase Storage URL
      if (imageUri.includes('firebasestorage.googleapis.com')) {
        console.log(`⏭️ Image ${imageIndex + 1} already uploaded, skipping...`);
        return imageUri;
      }
      
      // Only upload local file URLs
      if (!imageUri.startsWith('file://') && !imageUri.includes('/data/user/')) {
        console.log(`⏭️ Skipping non-local image: ${imageUri}`);
        return imageUri;
      }
      
      // Add retry logic for network issues
      let retries = 3;
      while (retries > 0) {
        try {
      const response = await fetch(imageUri);
          if (!response.ok) {
            throw new Error(`Failed to fetch image: ${response.status}`);
          }
      const blob = await response.blob();
      
      // Create a unique filename using session ID, contact number, and timestamp
      const timestamp = new Date().getTime();
      
      // Use separate folders for different types of contacts to maintain complete isolation
      const folderName = isGestorMobile ? 'gestorMobileContacts_images' : 'gestoresReports_images';
      const filename = `${folderName}/${userId}/${timestamp}_contact_${contactNumber}_${imageIndex}.jpg`;
      const imageRef = ref(storage, filename);
      
      const snapshot = await uploadBytes(imageRef, blob);
      const downloadURL = await getDownloadURL(snapshot.ref);
      
          console.log(`✅ Background sync: Image ${imageIndex + 1} uploaded successfully`);
      return downloadURL;
        } catch (uploadError) {
          retries--;
          if (retries > 0) {
            console.log(`⚠️ Upload retry ${3 - retries}/3 for image ${imageIndex + 1}...`);
            await new Promise(resolve => setTimeout(resolve, 1000 * (4 - retries))); // Exponential backoff
          } else {
            throw uploadError;
          }
        }
      }
      
      return null;
    } catch (error) {
      console.error(`❌ Background sync: Failed to upload image ${imageIndex + 1} after retries:`, error);
      return null; // Return null to indicate failure, but don't stop the process
    }
  }

  async syncOfflineData(): Promise<void> {
    if (this.syncInProgress) {
      console.log('🔄 Sync already in progress, skipping...');
      return;
    }

    // Check internet connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      console.log('📡 No internet connection, skipping sync');
      return;
    }

    // Check if there are images to sync
    const hasImagesToSync = await this.hasImagesToSync();
    if (hasImagesToSync) {
      this.reportProgress(0, 'A iniciar sincronização de fotos...');
    }

    this.syncInProgress = true;
    console.log('🚀 Starting monitoring data sync...');

    // Emergency timeout to prevent stuck sync
    const syncTimeout = setTimeout(() => {
      console.log('🚨 EMERGENCY: Sync timeout after 10s - force closing progress modal');
      this.reportProgress(0, '');
      this.syncInProgress = false;
    }, 10000); // 10 second timeout - more aggressive

    try {
      this.reportProgress(5, 'A sincronizar sessões...');
      await this.syncMonitoringSessions();
      
      this.reportProgress(10, 'A sincronizar pontos GPS...');
      await this.syncGPSPoints();
      
      await this.syncContactEvents();
      await this.syncGestorContactEvents(); // Add gestor contact sync
      
      if (hasImagesToSync) {
        this.reportProgress(100, 'Sincronização concluída!');
        // Clear progress after a short delay
        setTimeout(() => {
          this.reportProgress(0, '');
        }, 1000);
      }
      
      console.log('✅ Monitoring data sync completed successfully');
      clearTimeout(syncTimeout); // Clear timeout since sync completed
    } catch (error) {
      console.error('❌ Error syncing monitoring data:', error);
      clearTimeout(syncTimeout);
      this.reportProgress(0, 'Erro na sincronização');
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  private async syncMonitoringSessions(): Promise<void> {
    try {
      const offlineSessions = await AsyncStorage.getItem('offlineMonitoringSessions');
      if (!offlineSessions) return;

      const sessions: OfflineMonitoringSession[] = JSON.parse(offlineSessions);
      if (sessions.length === 0) return;

      console.log(`📊 Syncing ${sessions.length} monitoring sessions...`);

      const db = getFirestore();
      const batch = writeBatch(db);
      const syncedSessionIds: string[] = [];

      for (const session of sessions) {
        try {
          const sessionData = {
            sessionId: session.sessionId,
            protocol: session.protocol,
            startTime: new Date(session.startTime),
            endTime: session.endTime ? new Date(session.endTime) : null,
            totalDistance: session.totalDistance,
            contactsCount: session.contactsCount,
            pathCoordinates: session.pathCoordinates,
            userId: session.userId,
            status: session.status,
            deviceInfo: session.deviceInfo,
            syncedAt: serverTimestamp(),
            createdAt: serverTimestamp(),
          };

          const docRef = doc(collection(db, 'monitoringSessions'));
          batch.set(docRef, sessionData);
          syncedSessionIds.push(session.sessionId);

          console.log(`📤 Queued session ${session.sessionId} for sync`);
        } catch (error) {
          console.error(`❌ Error preparing session ${session.sessionId}:`, error);
        }
      }

      // Commit batch
      await batch.commit();
      console.log(`✅ Successfully synced ${syncedSessionIds.length} sessions`);

      // Remove synced sessions from local storage
      const remainingSessions = sessions.filter(s => !syncedSessionIds.includes(s.sessionId));
      await AsyncStorage.setItem('offlineMonitoringSessions', JSON.stringify(remainingSessions));

    } catch (error) {
      console.error('❌ Error syncing monitoring sessions:', error);
      throw error;
    }
  }

  private async syncGPSPoints(): Promise<void> {
    // Add timeout specifically for GPS points sync
    return Promise.race([
      this.doSyncGPSPoints(),
      new Promise<void>((_, reject) => {
        setTimeout(() => {
          console.log('🚨 GPS points sync timeout after 5 seconds');
          reject(new Error('GPS sync timeout'));
        }, 5000);
      })
    ]);
  }

  private async doSyncGPSPoints(): Promise<void> {
    try {
      const offlineGPSPoints = await AsyncStorage.getItem('offlineGPSPoints');
      if (!offlineGPSPoints) return;

      const gpsPoints: OfflineGPSPoint[] = JSON.parse(offlineGPSPoints);
      if (gpsPoints.length === 0) return;

      console.log(`📍 Syncing ${gpsPoints.length} GPS points...`);

      const db = getFirestore();
      const batch = writeBatch(db);
      let batchCount = 0;
      const maxBatchSize = 500; // Firestore batch limit

      for (const point of gpsPoints) {
        try {
          const pointData = {
            sessionId: point.sessionId,
            timestamp: new Date(point.timestamp),
            latitude: point.latitude,
            longitude: point.longitude,
            accuracy: point.accuracy,
            altitude: point.altitude,
            heading: point.heading,
            speed: point.speed,
            distance: point.distance,
            syncedAt: serverTimestamp(),
          };

          const docRef = doc(collection(db, 'gpsPoints'));
          batch.set(docRef, pointData);
          batchCount++;

          // Commit batch if we reach the limit
          if (batchCount >= maxBatchSize) {
            await batch.commit();
            console.log(`📤 Synced batch of ${batchCount} GPS points`);
            batchCount = 0;
          }
        } catch (error) {
          console.error(`❌ Error preparing GPS point:`, error);
        }
      }

      // Commit remaining points
      if (batchCount > 0) {
        await batch.commit();
        console.log(`📤 Synced final batch of ${batchCount} GPS points`);
      }

      console.log(`✅ Successfully synced all GPS points`);

      // Clear synced GPS points
      await AsyncStorage.removeItem('offlineGPSPoints');

    } catch (error) {
      console.error('❌ Error syncing GPS points:', error);
      throw error;
    }
  }

  private async syncContactEvents(): Promise<void> {
    try {
      const offlineContactEvents = await AsyncStorage.getItem('offlineContactEvents');
      if (!offlineContactEvents) return;

      const contactEvents: OfflineContactEvent[] = JSON.parse(offlineContactEvents);
      if (contactEvents.length === 0) return;

      console.log(`📞 Syncing ${contactEvents.length} contact events...`);

      const db = getFirestore();
      const syncedEvents: OfflineContactEvent[] = [];

      // Sync each contact event to Firebase
      for (let i = 0; i < contactEvents.length; i++) {
        const event = contactEvents[i];
        
        try {
          console.log(`📤 Syncing contact event ${i + 1}/${contactEvents.length} (contact ${event.contactNumber})...`);
          
          // Upload local images to Firebase Storage if they exist
          let finalImageUrls: string[] = [];
          
          if (event.images && event.images.length > 0) {
            console.log(`📸 Processing ${event.images.length} images for contact ${event.contactNumber}...`);
            this.reportProgress(10 + (i / contactEvents.length) * 60, `A carregar fotos do contacto ${event.contactNumber}...`);
            
            for (let imageIndex = 0; imageIndex < event.images.length; imageIndex++) {
              const imageUri = event.images[imageIndex];
              
              // Report progress for each image
              const imageProgress = 10 + (i / contactEvents.length) * 60 + (imageIndex / event.images.length) * (60 / contactEvents.length);
              this.reportProgress(imageProgress, `A carregar foto ${imageIndex + 1}/${event.images.length} do contacto ${event.contactNumber}...`);
              
              const uploadedUrl = await this.uploadImageToStorage(
                imageUri, 
                event.userId || 'unknown', 
                event.sessionId, 
                event.contactNumber, 
                imageIndex,
                false // isGestorMobile = false for technician contacts
              );
              
              // Only add successfully uploaded images
              if (uploadedUrl) {
                finalImageUrls.push(uploadedUrl);
              }
            }
            
            console.log(`✅ Successfully processed ${finalImageUrls.length}/${event.images.length} images for contact ${event.contactNumber}`);
          }

          // Create the event data with uploaded image URLs
          const eventDataToSync = {
            ...event,
            images: finalImageUrls, // Use uploaded URLs (empty array if all uploads failed)
            syncedAt: new Date().toISOString(),
            // Remove the needsImageSync flag as we've processed the images
            needsImageSync: undefined,
          };

          // Remove undefined fields
          delete eventDataToSync.needsImageSync;

          await addDoc(collection(db, 'contactEvents'), eventDataToSync);
          syncedEvents.push(event);
          
          console.log(`✅ Contact event ${i + 1} synced successfully (contact ${event.contactNumber})`);
          
        } catch (error) {
          console.error(`❌ Failed to sync contact event ${i + 1} (contact ${event.contactNumber}):`, error);
          // Continue with other events even if one fails - the failed event will remain in storage for retry
        }
      }

      // Also create a summary report in the reports collection for webadmin compatibility
      if (syncedEvents.length > 0) {
        try {
          // Group events by session for summary
          const sessionGroups = syncedEvents.reduce((groups, event) => {
            if (!groups[event.sessionId]) {
              groups[event.sessionId] = [];
            }
            groups[event.sessionId].push(event);
            return groups;
          }, {} as { [key: string]: OfflineContactEvent[] });

          // Create a summary report for each session
          for (const [sessionId, sessionEvents] of Object.entries(sessionGroups)) {
            const firstEvent = sessionEvents[0];
            const lastEvent = sessionEvents[sessionEvents.length - 1];
            
            // Get all successfully uploaded images from all events in this session
            const allSessionImages = sessionEvents.flatMap(e => e.images || []);
            
            // Create summary report document
            const summaryReport = {
              type: 'monitoring_session',
              sessionId: sessionId,
              userName: firstEvent.userName || 'Unknown User',
              userRole: firstEvent.userRole || 'colaborador',
              userId: firstEvent.userId || 'unknown_user',
              protocol: firstEvent.protocol || 'trajeto',
              created_at: firstEvent.timestamp,
              location: firstEvent.observerLocation,
              contactsCount: sessionEvents.length,
              deviceInfo: {
                platform: 'android' // Default, could be improved
              },
              // Include summary of all contacts in this session
              sessionSummary: {
                startTime: firstEvent.timestamp,
                endTime: lastEvent.timestamp,
                totalContacts: sessionEvents.length,
                circumstances: sessionEvents.map(e => e.circumstances).filter(Boolean),
                locations: sessionEvents.map(e => ({
                  observer: e.observerLocation,
                  contact: e.contactLocation,
                  distance: e.distance,
                  bearing: e.bearing
                })),
                images: allSessionImages
              },
              // For webadmin compatibility - use first event's data with all session images
              circumstances: firstEvent.circumstances,
              contactLocationDetails: firstEvent.contactLocationDetails,
              images: allSessionImages,
              distance: firstEvent.distance,
              bearing: firstEvent.bearing,
              syncedAt: new Date().toISOString()
            };

            // Only create summary report if we have a valid userId
            if (firstEvent.userId && firstEvent.userId !== 'unknown_user' && firstEvent.userId !== 'undefined') {
              await addDoc(collection(db, 'reports'), summaryReport);
              console.log(`✅ Summary report created for session: ${sessionId} with ${allSessionImages.length} images`);
            } else {
              console.log(`⚠️ Skipping summary report for session ${sessionId} - no valid userId (${firstEvent.userId})`);
              // If userId is invalid, mark this session's events for cleanup
              if (!firstEvent.userId || firstEvent.userId === 'undefined' || firstEvent.userId === 'unknown_user') {
                console.log(`🗑️ Marking session ${sessionId} events for cleanup due to invalid userId`);
                // Remove these events from the contact events array so they don't get retried
                syncedEvents.push(...sessionEvents);
              }
            }
          }
        } catch (summaryError) {
          console.error('❌ Failed to create summary reports:', summaryError);
          // Don't fail the entire sync process if summary creation fails
        }
      }

      console.log(`✅ Successfully synced ${syncedEvents.length}/${contactEvents.length} contact events`);

      // Only remove successfully synced events from local storage
      if (syncedEvents.length > 0) {
        const remainingEvents = contactEvents.filter(event => 
          !syncedEvents.some(synced => 
            synced.sessionId === event.sessionId && 
            synced.contactNumber === event.contactNumber &&
            synced.timestamp === event.timestamp
          )
        );
        
        await AsyncStorage.setItem('offlineContactEvents', JSON.stringify(remainingEvents));
        console.log(`🗑️ Removed ${syncedEvents.length} synced events, ${remainingEvents.length} remaining for retry`);
      }

    } catch (error) {
      console.error('❌ Error syncing contact events:', error);
      throw error;
    }
  }

  private async syncGestorContactEvents(): Promise<void> {
    try {
      console.log('🔄 SYNC: Starting syncGestorContactEvents...');
      const gestorOfflineContactEvents = await AsyncStorage.getItem('gestorOfflineContactEvents');
      console.log('🔄 SYNC: Raw gestorOfflineContactEvents from storage:', gestorOfflineContactEvents);
      
      if (!gestorOfflineContactEvents) {
        console.log('🔄 SYNC: No gestorOfflineContactEvents found in storage');
        return;
      }

      const contactEvents: OfflineContactEvent[] = JSON.parse(gestorOfflineContactEvents);
      console.log('🔄 SYNC: Parsed contact events:', contactEvents);
      
      if (contactEvents.length === 0) {
        console.log('🔄 SYNC: Contact events array is empty');
        return;
      }

      console.log(`📞 SYNC: Syncing ${contactEvents.length} gestor contact events...`);

      const db = getFirestore();
      const syncedEvents: OfflineContactEvent[] = [];

      // Sync each contact event to Firebase using gestorMobile_contacts collection
      for (let i = 0; i < contactEvents.length; i++) {
        const event = contactEvents[i];
        
        try {
          console.log(`📤 Syncing gestor contact event ${i + 1}/${contactEvents.length} (contact ${event.contactNumber})...`);
          
          // Upload local images to Firebase Storage if they exist
          let finalImageUrls: string[] = [];
          
          if (event.images && event.images.length > 0) {
            console.log(`📸 Processing ${event.images.length} images for gestor contact ${event.contactNumber}...`);
            
            for (let imageIndex = 0; imageIndex < event.images.length; imageIndex++) {
              const imageUri = event.images[imageIndex];
              
              const uploadedUrl = await this.uploadImageToStorage(
                imageUri, 
                event.userId || 'unknown', 
                event.sessionId, 
                event.contactNumber, 
                imageIndex,
                !event.isReportMode // isGestorMobile = true for zone trajectories, false for reports
              );
              
              // Only add successfully uploaded images
              if (uploadedUrl) {
                finalImageUrls.push(uploadedUrl);
              }
            }
            
            console.log(`✅ Successfully processed ${finalImageUrls.length}/${event.images.length} images for gestor contact ${event.contactNumber}`);
          }
          
          // Create contact event document for gestorMobile_contacts collection
          const contactEventData = {
            sessionId: event.sessionId,
            timestamp: new Date(event.timestamp),
            observerLocation: event.observerLocation,
            contactLocation: event.contactLocation,
            distance: event.distance,
            bearing: event.bearing || 0,
            contactNumber: event.contactNumber,
            circumstances: event.circumstances,
            contactLocationDetails: event.contactLocationDetails,
            images: finalImageUrls,
            userId: event.userId || 'unknown_user',
            userName: event.userName || 'Unknown User',
            userRole: event.userRole || 'gestor_caca',
            protocol: event.protocol || 'trajeto',
            syncedAt: serverTimestamp(),
          };

          // Save to appropriate collection based on isReportMode flag
          const collectionName = event.isReportMode ? 'gestoresReports_contacts' : 'gestorMobile_contacts';
          console.log(`🔄 SYNC: Event isReportMode: ${event.isReportMode}, using collection: ${collectionName}`);
          console.log(`🔄 SYNC: Contact sessionId: ${event.sessionId}, contactNumber: ${event.contactNumber}`);
          console.log(`🔄 SYNC: About to save contact event data:`, contactEventData);
          
          await addDoc(collection(db, collectionName), contactEventData);
          
          console.log(`✅ SYNC: Successfully synced gestor contact event ${event.contactNumber} to ${collectionName}`);
          console.log(`✅ SYNC: Contact saved with sessionId: ${event.sessionId}`);
          syncedEvents.push(event);
          
        } catch (error) {
          console.error(`❌ Failed to sync gestor contact event ${event.contactNumber}:`, error);
          // Continue with other events even if one fails
        }
      }

      console.log(`✅ Successfully synced ${syncedEvents.length}/${contactEvents.length} gestor contact events`);

      // Remove successfully synced events from local storage
      if (syncedEvents.length > 0) {
        const remainingEvents = contactEvents.filter(event => 
          !syncedEvents.some(synced => 
            synced.sessionId === event.sessionId && 
            synced.contactNumber === event.contactNumber &&
            synced.timestamp === event.timestamp
          )
        );
        
        await AsyncStorage.setItem('gestorOfflineContactEvents', JSON.stringify(remainingEvents));
        console.log(`🗑️ Removed ${syncedEvents.length} synced gestor contact events from local storage`);
      }

    } catch (error) {
      console.error('❌ Error syncing gestor contact events:', error);
      throw error;
    }
  }

  async getOfflineDataSummary(): Promise<{
    sessions: number;
    gpsPoints: number;
    contactEvents: number;
    gestorContactEvents: number;
  }> {
    try {
      const [sessions, gpsPoints, contactEvents, gestorContactEvents] = await Promise.all([
        AsyncStorage.getItem('offlineMonitoringSessions'),
        AsyncStorage.getItem('offlineGPSPoints'),
        AsyncStorage.getItem('offlineContactEvents'),
        AsyncStorage.getItem('gestorOfflineContactEvents'),
      ]);

      return {
        sessions: sessions ? JSON.parse(sessions).length : 0,
        gpsPoints: gpsPoints ? JSON.parse(gpsPoints).length : 0,
        contactEvents: contactEvents ? JSON.parse(contactEvents).length : 0,
        gestorContactEvents: gestorContactEvents ? JSON.parse(gestorContactEvents).length : 0,
      };
    } catch (error) {
      console.error('Error getting offline data summary:', error);
      return { sessions: 0, gpsPoints: 0, contactEvents: 0, gestorContactEvents: 0 };
    }
  }
}

export const monitoringSyncService = new MonitoringSyncService(); 