import React, { useState } from 'react';
import { View, Image, ActivityIndicator, StyleSheet, ViewStyle, ImageStyle } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';

type FontAwesomeIconName = keyof typeof FontAwesome.glyphMap;

interface LoadingImageProps {
  source: { uri: string };
  style?: ImageStyle;
  containerStyle?: ViewStyle;
  placeholderIcon?: FontAwesomeIconName;
  placeholderIconSize?: number;
  placeholderIconColor?: string;
}

const LoadingImage: React.FC<LoadingImageProps> = ({
  source,
  style,
  containerStyle,
  placeholderIcon = 'image',
  placeholderIconSize = 24,
  placeholderIconColor = '#666',
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  return (
    <View style={[styles.container, containerStyle]}>
      {isLoading && (
        <View style={[styles.loadingContainer, StyleSheet.absoluteFill]}>
          <ActivityIndicator color="#666" />
        </View>
      )}
      {hasError ? (
        <View style={[styles.placeholderContainer, StyleSheet.absoluteFill]}>
          <FontAwesome 
            name={placeholderIcon} 
            size={placeholderIconSize} 
            color={placeholderIconColor} 
          />
        </View>
      ) : (
        <Image
          source={source}
          style={[styles.image, style]}
          onLoadStart={() => setIsLoading(true)}
          onLoadEnd={() => setIsLoading(false)}
          onError={() => {
            setIsLoading(false);
            setHasError(true);
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  placeholderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});

export default LoadingImage; 