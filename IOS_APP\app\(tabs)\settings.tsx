import { StyleSheet, View, Text, Pressable, Dimensions, ScrollView, Image } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useState, useEffect } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import CustomAlert from '@/components/CustomAlert';
import HelpSupportModal from '@/components/HelpSupportModal';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import MalhaLogo from '@/assets/images/malha_logo.svg';

// Portuguese translations
const pt = {
  privacy: 'Privacidade',
  helpSupport: 'Ajuda e Suporte',
  about: 'Sobre ProROLA',
  weatherConditions: 'Condições para Observação',
};

function SettingsItem({ 
  icon, 
  title, 
  onPress 
}: { 
  icon: string; 
  title: string; 
  onPress?: () => void;
}) {
  const handlePress = () => {
    onPress?.();
  };

  return (
    <Pressable 
      style={({ pressed }) => [
        styles.settingsItem,
        pressed && styles.settingsItemPressed
      ]}
      onPress={handlePress}
    >
      <FontAwesome name={icon as any} size={28} color="#0996a8" />
      <Text style={styles.settingsText}>{title}</Text>
      <FontAwesome name="chevron-right" size={18} color="#666" />
    </Pressable>
  );
}

export default function SettingsScreen() {
  const [screenData, setScreenData] = useState(Dimensions.get('window'));
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const [showHelpModal, setShowHelpModal] = useState(false);
  const isLandscape = screenData.width > screenData.height;

  useEffect(() => {
    const onChange = (result: { window: any }) => {
      setScreenData(result.window);
    };
    
    const subscription = Dimensions.addEventListener('change', onChange);
    return () => subscription?.remove();
  }, []);

  const handleWeatherConditions = () => {
    router.push('./weather-conditions');
  };

  const handlePrivacy = () => {
    router.push('./privacy');
  };

  const handleHelpSupport = () => {
    setShowHelpModal(true);
  };

  const handleAbout = () => {
    router.push('./about');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={[styles.contentContainer, { paddingTop: isLandscape ? 20 : 40 }]}
        showsVerticalScrollIndicator={true}
      >
        <SettingsItem 
          icon="cloud" 
          title={pt.weatherConditions} 
          onPress={handleWeatherConditions}
        />
        <SettingsItem 
          icon="lock" 
          title={pt.privacy} 
          onPress={handlePrivacy}
        />
        <SettingsItem 
          icon="question-circle" 
          title={pt.helpSupport} 
          onPress={handleHelpSupport}
        />
        <SettingsItem 
          icon="info-circle" 
          title={pt.about} 
          onPress={handleAbout}
        />
       
        
        {/* Add some extra content to test scrolling */}
        <View style={styles.spacer} />
        
        
        {/* Desenvolvido por section */}
        <View style={styles.developedByContainer}>
          <Text style={styles.developedByText}>Desenvolvido por</Text>
          <MalhaLogo 
            width={100}
            height={50}
            style={styles.malhaLogo}
          />
        </View>
        
        <Text style={styles.versionText}>Versão 1.0.12</Text> 
       
      </ScrollView>

      <CustomAlert
        visible={isVisible}
        type={config.type as 'success' | 'error' | 'warning' | 'info'}
        title={config.title}
        message={config.message || ''}
        onClose={hideAlert}
        onConfirm={config.onConfirm}
      />

      <HelpSupportModal
        visible={showHelpModal}
        onClose={() => setShowHelpModal(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    top: -25,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingBottom: 20, // Add extra padding for tab bar
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 28,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
    minHeight: 90,
  },
  settingsItemPressed: {
    backgroundColor: '#f0f0f0',
  },
  settingsText: {
    fontSize: 16,
    marginLeft: 20,
    flex: 1,
    color: '#666',
    fontWeight: '500',
  },
  spacer: {
    height: 20,
  },
  versionText: {
    fontSize: 9,
    color: '#666',
    textAlign: 'center',
    paddingVertical: 5,
  },
  developedByContainer: {
    alignItems: 'center',
    paddingVertical: 0,
  },
  developedByText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginBottom: 4,
  },
  malhaLogo: {
    width: 100,
    height: 50,
  },
}); 