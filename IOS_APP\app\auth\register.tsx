import React, { useState } from 'react';
import { StyleSheet, View, TextInput, TouchableOpacity, ScrollView, ActivityIndicator, Image, Platform, KeyboardAvoidingView } from 'react-native';
import { Text } from 'react-native';
import { router, useRootNavigationState, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome } from '@expo/vector-icons';
import { useAuth } from '@/contexts/AuthContext';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';

// Portuguese translations
const pt = {
  createAccount: 'Criar Conta',
  createAccountAs: 'Criar conta como',
  usernamePlaceholder: 'Nome de utilizador',
  emailPlaceholder: 'Introduza o seu email',
  passwordPlaceholder: 'Introduza a sua password',
  confirmPasswordPlaceholder: 'Confirmar password',
  register: 'Registar',
  haveAccount: 'Já tem uma conta?',
  login: 'Entrar',
  errorTitle: 'Erro',
  usernameError: 'O nome de utilizador deve ter pelo menos 3 caracteres',
  emailError: 'Por favor, insira um email válido',
  passwordError: 'A palavra-passe deve ter pelo menos 6 caracteres',
  passwordMatchError: 'As palavras-passe não coincidem',
  registrationFailed: 'Falha no registo. Por favor, tente novamente.',
};

export default function RegisterScreen() {
  const rootNavigationState = useRootNavigationState();
  const { registrationType } = useLocalSearchParams<{ registrationType?: string }>();
  const { register } = useAuth();
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  if (!rootNavigationState?.key) {
    return null;
  }

  const screenTitle = registrationType 
    ? `${pt.createAccountAs} ${registrationType}` 
    : pt.createAccount;

  const validateForm = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      showAlert({
        type: 'error',
        message: pt.emailError
      });
      return false;
    }

    if (formData.password.length < 6) {
      showAlert({
        type: 'error',
        message: pt.passwordError
      });
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      showAlert({
        type: 'error',
        message: pt.passwordMatchError
      });
      return false;
    }

    return true;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      await register(formData.email, formData.password, registrationType || 'Colaborador');
    } catch (error: any) {
      // Check if it's the "email already in use" error to show as info instead of error
      const isEmailAlreadyInUse = error.message === 'Este email já está registado.';
      
      showAlert({
        type: isEmailAlreadyInUse ? 'info' : 'error',
        message: error.message || pt.registrationFailed
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
      <View style={styles.mainContainer}>
        <View style={styles.header}>
          <SafeAreaView edges={['top']} style={styles.headerContent}>
            <Image 
              source={require('../../assets/images/header-logo.png')}
              style={styles.logo}
            />
          </SafeAreaView>
        </View>

        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <View style={styles.formContainer}>
            <Text style={styles.welcomeText}>{screenTitle}</Text>

            <View style={styles.inputContainer}>
              <View style={styles.inputWrapper}>
                <View style={styles.iconContainer}>
                  <FontAwesome name="envelope" size={20} color="#999" />
                </View>
                <TextInput
                  style={[styles.input, styles.inputWithIcon]}
                  placeholder={pt.emailPlaceholder}
                  placeholderTextColor="#999"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  value={formData.email}
                  onChangeText={(text) => setFormData({ ...formData, email: text })}
                  editable={!isLoading}
                />
              </View>
            </View>

            <View style={styles.inputContainer}>
              <View style={styles.inputWrapper}>
                <View style={styles.iconContainer}>
                  <FontAwesome name="lock" size={20} color="#999" />
                </View>
                <TextInput
                  style={[styles.input, styles.inputWithIcon, styles.passwordInput]}
                  placeholder={pt.passwordPlaceholder}
                  placeholderTextColor="#999"
                  secureTextEntry={!showPassword}
                  value={formData.password}
                  onChangeText={(text) => setFormData({ ...formData, password: text })}
                  editable={!isLoading}
                />
                <TouchableOpacity 
                  style={styles.eyeIcon}
                  onPress={() => setShowPassword(!showPassword)}>
                  <FontAwesome 
                    name={showPassword ? "eye" : "eye-slash"} 
                    size={20} 
                    color="#999" 
                  />
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <View style={styles.inputWrapper}>
                <View style={styles.iconContainer}>
                  <FontAwesome name="lock" size={20} color="#999" />
                </View>
                <TextInput
                  style={[styles.input, styles.inputWithIcon, styles.passwordInput]}
                  placeholder={pt.confirmPasswordPlaceholder}
                  placeholderTextColor="#999"
                  secureTextEntry={!showConfirmPassword}
                  value={formData.confirmPassword}
                  onChangeText={(text) => setFormData({ ...formData, confirmPassword: text })}
                  editable={!isLoading}
                />
                <TouchableOpacity 
                  style={styles.eyeIcon}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
                  <FontAwesome 
                    name={showConfirmPassword ? "eye" : "eye-slash"} 
                    size={20} 
                    color="#999" 
                  />
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity
              style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
              onPress={handleRegister}
              disabled={isLoading}>
              {isLoading ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <>
                  <FontAwesome name="user-plus" size={20} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.buttonText}>{pt.register}</Text>
                </>
              )}
            </TouchableOpacity>

            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>{pt.haveAccount}</Text>
              <TouchableOpacity 
                style={styles.loginButton}
                onPress={() => router.push('/auth/login')} 
                disabled={isLoading}>
                <FontAwesome name="sign-in" size={20} color="#FFFFFF" style={styles.buttonIcon} />
                <Text style={styles.loginButtonText}>{pt.login}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>

        <SafeAreaView edges={['bottom']} style={styles.footer}>
          <Text style={styles.copyrightText}>
            © {new Date().getFullYear()} Todos os direitos reservados ICNF
          </Text>
        </SafeAreaView>
      </View>

      <CustomAlert
        visible={isVisible}
        type={config.type as 'success' | 'error' | 'warning' | 'info'}
        message={config.message || ''}
        onClose={hideAlert}
        onConfirm={config.onConfirm}
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#0996a8',
    height: 80,
    zIndex: 2,
    elevation: 2,
  },
  headerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 20,
  },
  logo: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
    top: 30,
    zIndex: 2,
  },
  scrollView: {
    flex: 1,
    marginTop: 50,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  formContainer: {
    padding: 20,
    paddingTop: 20,
  },
  welcomeText: {
    fontSize: 16,
    fontWeight: '400',
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    marginTop: 10,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  iconContainer: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: '#000',
  },
  inputWithIcon: {
    paddingRight: 15,
  },
  passwordInput: {
    paddingRight: 50,
  },
  eyeIcon: {
    position: 'absolute',
    right: 15,
    top: 15,
  },
  button: {
    flexDirection: 'row',
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0996a8',
    marginTop: 20,
    alignSelf: 'center',
    paddingHorizontal: 40,
    minWidth: 220,
  },
  buttonIcon: {
    marginRight: 10,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  loginContainer: {
    marginTop: 30,
    alignItems: 'center',
    gap: 10,
  },
  loginText: {
    color: '#666',
    fontSize: 14,
  },
  loginButton: {
    flexDirection: 'row',
    backgroundColor: '#0996a8',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    alignItems: 'center',
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    backgroundColor: '#0996a8',
    padding: 10,
    width: '100%',
  },
  copyrightText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
  },
}); 