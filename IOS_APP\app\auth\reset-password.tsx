import React, { useState } from 'react';
import { StyleSheet, View, TextInput, TouchableOpacity, ActivityIndicator, Image, Platform, ScrollView } from 'react-native';
import { Text } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
import { auth, db } from '@/config/firebase';
import { sendPasswordResetEmail } from 'firebase/auth';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { useNetwork } from '@/contexts/NetworkContext';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import NetworkStatusIndicator from '@/components/NetworkStatusIndicator';

// Portuguese translations
const pt = {
  welcomeText: 'Recuperar Password',
  subtitle: 'Introduza o seu email para recuperar a password',
  email: 'Email',
  emailPlaceholder: 'Introduza o seu email',
  submit: 'Enviar',
  cancel: 'Cancelar',
  errorTitle: 'Erro',
  successTitle: 'Sucesso',
  emailNotFound: 'Email não encontrado. Por favor, verifique o email introduzido.',
  emailInvalid: 'Email inválido. Por favor, verifique o email introduzido.',
  generalError: 'Ocorreu um erro. Por favor, tente novamente.',
  emailSent: 'Foi enviado um email com instruções para recuperar a password.',
  fillEmail: 'Por favor, introduza o seu email.',
  noInternetReset: 'Não é possível recuperar password sem ligação à Internet',
  offlineMode: 'Modo Offline',
  offlineMessage: 'A recuperação de password requer ligação à Internet.',
};

export default function ResetPasswordScreen() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const { hasInternetConnection } = useNetwork();

  const checkEmailExists = async (email: string) => {
    try {
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('email', '==', email.toLowerCase()));
      const querySnapshot = await getDocs(q);
      return !querySnapshot.empty;
    } catch (error) {
      console.error('Error checking email:', error);
      return false;
    }
  };

  const handleResetPassword = async () => {
    if (!hasInternetConnection()) {
      showAlert({
        type: 'warning',
        message: pt.noInternetReset
      });
      return;
    }

    if (!email) {
      showAlert({
        type: 'error',
        message: pt.fillEmail
      });
      return;
    }

    setIsLoading(true);
    try {
      // First check if email exists in our database
      const emailExists = await checkEmailExists(email);
      if (!emailExists) {
        showAlert({
          type: 'error',
          message: pt.emailNotFound
        });
        return;
      }

      // If email exists, send reset email
      await sendPasswordResetEmail(auth, email);
      showAlert({
        type: 'success',
        message: pt.emailSent,
        onConfirm: () => router.back()
      });
    } catch (error: any) {
      console.error('Password reset error:', error);
      let errorMessage = pt.generalError;
      
      if (error.code === 'auth/invalid-email') {
        errorMessage = pt.emailInvalid;
      }
      
      showAlert({
        type: 'error',
        message: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <SafeAreaView edges={['top']} style={styles.headerContent}>
          <Image 
            source={require('../../assets/images/header-logo.png')}
            style={styles.logo}
          />
        </SafeAreaView>
      </View>

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}>
        <View style={styles.formContainer}>
          <View style={styles.titleContainer}>
            <Text style={styles.welcomeText}>{pt.welcomeText}</Text>
            <NetworkStatusIndicator />
          </View>
          <Text style={styles.subtitle}>{pt.subtitle}</Text>

          {!hasInternetConnection() && (
            <View style={styles.offlineCard}>
              <FontAwesome6 
                name="wifi" 
                size={24} 
                color="#ff9500" 
                style={{ opacity: 0.8 }}
              />
              <Text style={styles.offlineModeText}>{pt.offlineMode}</Text>
              <Text style={styles.offlineMessageText}>{pt.offlineMessage}</Text>
            </View>
          )}

          <View style={styles.inputContainer}>
            <Text style={styles.label}>{pt.email}</Text>
            <View style={styles.inputWrapper}>
              <View style={styles.iconContainer}>
                <FontAwesome name="envelope" size={20} color="#999" />
              </View>
              <TextInput
                style={[styles.input, styles.inputWithIcon]}
                placeholder={pt.emailPlaceholder}
                placeholderTextColor="#999"
                keyboardType="email-address"
                autoCapitalize="none"
                value={email}
                onChangeText={setEmail}
                editable={!isLoading && hasInternetConnection()}
              />
            </View>
          </View>

          {hasInternetConnection() ? (
            <>
              <TouchableOpacity
                style={[styles.submitButton, { opacity: isLoading ? 0.7 : 1 }]}
                onPress={handleResetPassword}
                disabled={isLoading}>
                {isLoading ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <>
                    <FontAwesome name="paper-plane" size={20} color="#FFFFFF" style={styles.buttonIcon} />
                    <Text style={styles.submitButtonText}>{pt.submit}</Text>
                  </>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => router.back()}
                disabled={isLoading}>
                <FontAwesome name="times" size={20} color="#666" style={styles.buttonIcon} />
                <Text style={styles.cancelButtonText}>{pt.cancel}</Text>
              </TouchableOpacity>
            </>
          ) : (
            <>
              <View style={[styles.submitButton, styles.disabledButton]}>
                <FontAwesome name="paper-plane" size={20} color="#999" style={styles.buttonIcon} />
                <Text style={[styles.submitButtonText, styles.disabledButtonText]}>{pt.submit}</Text>
              </View>

              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => router.back()}>
                <FontAwesome name="times" size={20} color="#666" style={styles.buttonIcon} />
                <Text style={styles.cancelButtonText}>{pt.cancel}</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </ScrollView>

      <SafeAreaView edges={['bottom']} style={styles.footer}>
        <Text style={styles.copyrightText}>
          © {new Date().getFullYear()} Todos os direitos reservados ICNF
        </Text>
      </SafeAreaView>

      <CustomAlert
        visible={isVisible}
        type={config.type}
        message={config.message}
        onClose={hideAlert}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#0996a8',
    height: 80,
    zIndex: 2,
    elevation: 2,
  },
  headerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 20,
  },
  logo: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
    top: 30,
    zIndex: 2,
  },
  scrollView: {
    flex: 1,
    marginTop: 50,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'ios' ? 100 : 80,
  },
  formContainer: {
    padding: 20,
    paddingTop: 20,
  },
  titleContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  welcomeText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  iconContainer: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: '#000',
  },
  inputWithIcon: {
    paddingRight: 15,
  },
  submitButton: {
    flexDirection: 'row',
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0996a8',
    marginTop: 20,
    alignSelf: 'center',
    width: '60%',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  cancelButton: {
    flexDirection: 'row',
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    marginTop: 10,
    alignSelf: 'center',
    width: '60%',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  buttonIcon: {
    marginRight: 10,
  },
  footer: {
    backgroundColor: '#0996a8',
    padding: 10,
    width: '100%',
  },
  copyrightText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
  },
  offlineCard: {
    backgroundColor: '#fef7f0',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    alignItems: 'center',
    borderLeftWidth: 4,
    borderLeftColor: '#ff9500',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  offlineModeText: {
    color: '#e65100',
    fontSize: 16,
    fontWeight: '600',
    marginTop: 10,
    marginBottom: 8,
  },
  offlineMessageText: {
    color: '#bf360c',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 18,
  },
  disabledButton: {
    flexDirection: 'row',
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    marginTop: 20,
    alignSelf: 'center',
    width: '60%',
  },
  disabledButtonText: {
    color: '#999',
    fontSize: 16,
    fontWeight: '500',
  },
}); 