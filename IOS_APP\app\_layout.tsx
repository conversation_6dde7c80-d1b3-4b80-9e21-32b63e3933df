import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { useEffect, useState } from 'react';
import { useColorScheme } from '@/hooks/useColorScheme';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import { NetworkProvider } from '@/contexts/NetworkContext';
import { View, ActivityIndicator, Image, StyleSheet, Animated, Dimensions } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import SafeSystemBars from '@/components/SafeSystemBars';
import { SafeAreaView } from 'react-native-safe-area-context';

function IntroScreen({ onComplete }: { onComplete: () => void }) {
  const fadeAnim = useState(new Animated.Value(0))[0];
  const [screenData, setScreenData] = useState(Dimensions.get('window'));
  const isLandscape = screenData.width > screenData.height;

  useEffect(() => {
    const onChange = (result: { window: any }) => {
      setScreenData(result.window);
    };
    
    const subscription = Dimensions.addEventListener('change', onChange);
    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    // Start with a slight delay to ensure smooth transition
    setTimeout(() => {
      // Fade in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }).start();

      // Hide after 5 seconds
      setTimeout(() => {
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }).start(onComplete);
      }, 4000);
    }, 100);
  }, []);

  return (
    <View style={styles.introContainer}>
              <SafeSystemBars style="light" backgroundColor="transparent" />
      {/* Original: <StatusBar style="light" backgroundColor="transparent" translucent /> */}
      <Animated.View style={[styles.introImageContainer, { opacity: fadeAnim }]}>
        <Image
          source={isLandscape 
            ? require('../assets/images/splash-landscape.png')
            : require('../assets/images/splash.png')
          }
          style={styles.introImage}
          resizeMode="cover"
        />
      </Animated.View>
    </View>
  );
}

function RootLayoutNav() {
  const { isLoading } = useAuth();
  const colorScheme = useColorScheme();
  const [showIntro, setShowIntro] = useState(true);

  if (isLoading || showIntro) {
    return (
      <IntroScreen onComplete={() => {
        setShowIntro(false);
      }} />
    );
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack>
        <Stack.Screen name="index" options={{ headerShown: false }} />
        <Stack.Screen name="auth" options={{ headerShown: false }} />
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen 
          name="report" 
          options={{ 
            headerShown: false,
            presentation: 'modal',
            gestureEnabled: false,
            animationTypeForReplace: 'push'
          }} 
        />
        <Stack.Screen name="weather-conditions" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" options={{ title: 'Oops!' }} />
      </Stack>
    </ThemeProvider>
  );
}

export default function RootLayout() {
  const [loaded, error] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (error) console.error('Font loading error:', error);
  }, [error]);

  if (!loaded) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0996a8" />
      </View>
    );
  }

  return (
    <NetworkProvider>
    <AuthProvider>
      <RootLayoutNav />
    </AuthProvider>
    </NetworkProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  introContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  introImageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  introImage: {
    width: '100%',
    height: '100%',
  },
});
