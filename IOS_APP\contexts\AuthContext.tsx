import { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  onAuthStateChanged,
  User,
  EmailAuthProvider,
  reauthenticateWithCredential,
  updatePassword as firebaseUpdatePassword,
  sendEmailVerification
} from 'firebase/auth';
import { ref, uploadBytes, getDownloadURL, listAll, deleteObject } from 'firebase/storage';
import { auth, storage, db } from '@/config/firebase';
import { doc, setDoc, serverTimestamp, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import * as FileSystem from 'expo-file-system';

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  userRole: string | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, registrationType: string) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateUserProfile: (displayName?: string, photoURI?: string | null) => Promise<void>;
  deleteAccount: (password?: string) => Promise<void>;
  reauthenticate: (password: string) => Promise<void>;
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  resendVerificationEmail: () => Promise<void>;
  fetchUserRole: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoginInProgress, setIsLoginInProgress] = useState(false);
  const loginInProgressRef = useRef(false);
  const logoutInProgressRef = useRef(false);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedAuth = await AsyncStorage.getItem('isAuthenticated');
        
        const unsubscribe = onAuthStateChanged(auth, async (user) => {
          if (user) {
            // Check if this restored user matches the last logged-in email
            const lastLoginEmail = await AsyncStorage.getItem('lastLoginEmail');
            console.log(`🔍 Session restoration check: lastLoginEmail=${lastLoginEmail}, currentUser=${user.email}`);
            console.log(`🔍 Email match: ${lastLoginEmail === user.email}`);
            
            // If no lastLoginEmail is stored, check if login is in progress
            if (!lastLoginEmail) {
              console.log(`🚫 No lastLoginEmail found - checking if login is in progress`);
              
              // If login is in progress, allow it to proceed without validation
              if (loginInProgressRef.current) {
                console.log(`✅ Login in progress - allowing authentication to proceed without validation`);
                setIsLoading(false);
                return;
              }
              
              // Otherwise, force manual login to ensure correct account
              console.log(`🚫 No login in progress - forcing manual login to ensure correct account`);
              await signOut(auth);
              setIsAuthenticated(false);
              setUser(null);
              setUserRole(null);
              await AsyncStorage.removeItem('isAuthenticated');
              router.replace('/auth/login');
              setIsLoading(false);
              return;
            }
            
            if (user.email !== lastLoginEmail) {
              console.log(`🚫 Session restored wrong user: expected ${lastLoginEmail}, got ${user.email}`);
              
              // If login is in progress, allow it to proceed without validation
              if (loginInProgressRef.current) {
                console.log(`✅ Login in progress - allowing authentication to proceed without validation`);
                setIsLoading(false);
                return;
              }
              
              console.log(`🔄 Signing out wrong user and redirecting to login`);
              // Sign out the wrong user and redirect to login
              await signOut(auth);
              setIsAuthenticated(false);
              setUser(null);
              setUserRole(null);
              await AsyncStorage.removeItem('isAuthenticated');
              router.replace('/auth/login');
              setIsLoading(false);
              return;
            }
            
            console.log(`✅ Session restored correct user: ${user.email}`);
            
            // User is signed in with correct email
            setUser(user);
            
            // Don't navigate if login is in progress - let the login function handle it
            if (loginInProgressRef.current) {
              setIsLoading(false);
              return;
            }
            
            if (user.emailVerified) {
              // Check if this is a newly verified user who needs Firestore document creation
              try {
                await ensureUserDocumentExists(user);
              } catch (error) {
                console.error('Failed to create user document:', error);
              }
              
              // Check if this is a technician that needs approval before allowing access
              const userDocRef = doc(db, 'users', user.uid);
              const userDoc = await getDoc(userDocRef);
              
              let userData = null;
              let userExists = false;
              
              if (userDoc.exists()) {
                const tempUserData = userDoc.data();
                // Verify email matches to prevent cross-account access
                if (tempUserData.email === user.email) {
                  userData = tempUserData;
                  userExists = true;
                }
              }
              
              // Only check gestoresZonaCaca if no valid match in users collection
              if (!userExists) {
                const gestorDocRef = doc(db, 'gestoresZonaCaca', user.uid);
                const gestorDoc = await getDoc(gestorDocRef);
                
                if (gestorDoc.exists()) {
                  const tempGestorData = gestorDoc.data();
                  // Verify email matches to prevent cross-account access
                  if (tempGestorData.email === user.email) {
                    userData = tempGestorData;
                    userExists = true;
                    
                    // For gestor_caca users, update Firebase Auth profile if it's missing
                    if (userData.role === 'gestor_caca' && userData.name && !user.displayName) {
                      try {
                        await updateProfile(user, {
                          displayName: userData.name
                        });
                        // Force refresh the user object to get the updated displayName
                        await user.reload();
                        setUser({ ...user });
                      } catch (error) {
                        console.error('Error updating Firebase Auth profile for gestor_caca user:', error);
                      }
                    }
                  }
                }
              }
              
              if (userExists && userData) {
                // If user is an admin, block access to mobile app
                if (userData.role === 'admin' || userData.role === 'administrador') {
                  setIsAuthenticated(false);
                  setUser(null);
                  await AsyncStorage.removeItem('isAuthenticated');
                  // Store a message to show on login screen
                  await AsyncStorage.setItem('pendingApprovalMessage', 'As contas de administrador só podem ser utilizadas no sistema web de administração. Por favor, utilize uma conta de utilizador móvel para aceder à aplicação.');
                  await signOut(auth);
                  router.replace('/auth/login');
                  setIsLoading(false);
                  return;
                }
                
                // If user is a technician and not verified, sign them out and redirect to login
                if (userData.role === 'tecnico_prorola' && !userData.verified) {
                  setIsAuthenticated(false);
                  setUser(null);
                  await AsyncStorage.removeItem('isAuthenticated');
                  // Store a message to show on login screen
                  await AsyncStorage.setItem('pendingApprovalMessage', 'A sua conta de técnico ainda não foi aprovada por um responsável ProROLA. Por favor, aguarde a aprovação antes de tentar fazer login.');
                  await signOut(auth);
                  router.replace('/auth/login');
                  setIsLoading(false);
                  return;
                }
              }
              
              setIsAuthenticated(true);
              await AsyncStorage.setItem('isAuthenticated', 'true');
              
              // Always redirect verified users to the app
              const needsProfileSetup = !user.displayName;
              if (needsProfileSetup) {
                await AsyncStorage.setItem('needsProfileSetup', 'true');
                router.replace('/(tabs)/profile');
              } else {
                router.replace('/(tabs)');
              }
            } else {
              setIsAuthenticated(false);
              await AsyncStorage.removeItem('isAuthenticated');
              router.replace('/auth/verify-email');
            }
          } else {
            // User is signed out
            setIsAuthenticated(false);
            setUser(null);
            await AsyncStorage.removeItem('isAuthenticated');
            if (storedAuth) {
              router.replace('/auth/login');
            }
          }
          setIsLoading(false);
        });

        // Set a timeout to prevent infinite loading
        const timeoutId = setTimeout(() => {
          setIsLoading(false);
        }, 5000);

        return () => {
          unsubscribe();
          clearTimeout(timeoutId);
        };
      } catch (error) {
        console.error('Auth initialization error:', error);
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Robust function to ensure user document exists with retry logic
  const ensureUserDocumentExists = async (user: User, maxRetries = 3) => {
    const userDocRef = doc(db, 'users', user.uid);
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Check if document already exists in users collection
        const userDoc = await getDoc(userDocRef);
        if (userDoc.exists()) {
          return true;
        }
        
        // Check if user exists in gestoresZonaCaca collection (gestor_caca users)
        const gestorDocRef = doc(db, 'gestoresZonaCaca', user.uid);
        const gestorDoc = await getDoc(gestorDocRef);
        if (gestorDoc.exists()) {
          // User exists as gestor_caca, no need to create in users collection
          return true;
        }
        
        // Try to get pending registration data first
        const pendingRegistration = await AsyncStorage.getItem('pendingUserRegistration');
        
        let userRole = 'colaborador'; // Safe default
        let foundPendingData = false;

        if (pendingRegistration) {
          try {
            const registrationData = JSON.parse(pendingRegistration);
            
            if (registrationData.uid === user.uid) {
              userRole = registrationData.role;
              foundPendingData = true;
            }
          } catch (error) {
            console.error('Error parsing pending registration:', error);
          }
        }

        // If no pending data, try to infer role from email verification time
        if (!foundPendingData) {
          // Check if this user was recently created (likely a registration)
          const userCreatedTime = user.metadata.creationTime;
          const now = new Date();
          const creationDate = userCreatedTime ? new Date(userCreatedTime) : new Date();
          const timeDiff = now.getTime() - creationDate.getTime();
          const isRecentRegistration = timeDiff < 24 * 60 * 60 * 1000; // Within 24 hours

          if (isRecentRegistration) {
            // For recent registrations without pending data, default to colaborador
            // This is safer than guessing tecnico_prorola
            userRole = 'colaborador';
          } else {
            userRole = 'colaborador';
          }
        }

        // Create the user document
        const userData = {
          email: user.email,
          name: user.email?.split('@')[0] || 'Utilizador',
          role: userRole,
          verified: userRole !== 'tecnico_prorola', // Only technicians need approval
          created_at: serverTimestamp(),
          updated_at: serverTimestamp(),
          uid: user.uid,
          // Add metadata to track this was auto-created
          auto_created: !foundPendingData,
          auto_created_at: serverTimestamp()
        };

        await setDoc(userDocRef, userData);
        
        // Verify the document was actually created
        const verificationDoc = await getDoc(userDocRef);
        if (!verificationDoc.exists()) {
          throw new Error('Document verification failed after creation');
        }

        // Clear pending registration if we used it
        if (foundPendingData) {
          await AsyncStorage.removeItem('pendingUserRegistration');
        }

        return true;

      } catch (error) {
        console.error(`Attempt ${attempt} failed to create user document:`, error);
        
        if (attempt === maxRetries) {
          console.error('All attempts failed to create user document');
          // Don't clear pending registration on final failure
          throw error;
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
      }
    }
    
    return false;
  };

  // Function to fetch user role from Firestore
  const fetchUserRole = async () => {
    if (!user) return;
    
    console.log(`🔍 fetchUserRole: Starting role fetch for user ${user.uid} (${user.email})`);
    
    try {
      let finalRole = null;
      let finalUserData = null;
      
      // Check both collections but prioritize based on email match, not just existence
      const userDocRef = doc(db, 'users', user.uid);
      const gestorDocRef = doc(db, 'gestoresZonaCaca', user.uid);
      
      // Get both documents simultaneously
      const [userDoc, gestorDoc] = await Promise.all([
        getDoc(userDocRef),
        getDoc(gestorDocRef)
      ]);
      
      let usersCollectionMatch = false;
      let gestorCollectionMatch = false;
      
      // Check users collection - must match email exactly
      if (userDoc.exists()) {
        const userData = userDoc.data();
        const emailMatches = userData.email === user.email;
        console.log(`✅ fetchUserRole: Found user in 'users' collection with role: ${userData.role}, email match: ${emailMatches}`);
        
        if (userData.role && userData.role !== 'undefined' && emailMatches) {
          finalRole = userData.role;
          finalUserData = userData;
          usersCollectionMatch = true;
          console.log(`🎯 fetchUserRole: Valid match in 'users' collection: ${finalRole}`);
        }
      }
      
      // Check gestoresZonaCaca collection - must match email exactly
      if (gestorDoc.exists()) {
        const gestorData = gestorDoc.data();
        const emailMatches = gestorData.email === user.email;
        console.log(`✅ fetchUserRole: Found user in 'gestoresZonaCaca' collection with role: ${gestorData.role}, email match: ${emailMatches}`);
        console.log(`📧 fetchUserRole: Gestor data:`, { email: gestorData.email, name: gestorData.name, role: gestorData.role });
        
        if (gestorData.role && gestorData.role !== 'undefined' && emailMatches) {
          gestorCollectionMatch = true;
          
          // Only override users collection if it didn't have a valid match
          if (!usersCollectionMatch) {
            finalRole = gestorData.role;
            finalUserData = gestorData;
            console.log(`🎯 fetchUserRole: Using role from 'gestoresZonaCaca' collection: ${finalRole}`);
          } else {
            console.log(`⚠️ fetchUserRole: Found matches in both collections, prioritizing 'users' collection for ${user.email}`);
          }
          
          // For gestor_caca users, update Firebase Auth profile if it's missing
          if (gestorData.role === 'gestor_caca' && gestorData.name && !user.displayName && !usersCollectionMatch) {
            try {
              console.log(`🔄 fetchUserRole: Updating Firebase Auth displayName for gestor_caca user`);
              await updateProfile(user, {
                displayName: gestorData.name
              });
              // Force refresh the user object to get the updated displayName
              await user.reload();
              setUser({ ...user });
            } catch (error) {
              console.error('Error updating Firebase Auth profile for gestor_caca user:', error);
            }
          }
        }
      }
      
      // If we found matches in both collections, log a warning
      if (usersCollectionMatch && gestorCollectionMatch) {
        console.log(`⚠️ fetchUserRole: User ${user.email} exists in both collections. Using 'users' collection role: ${finalRole}`);
      }
      
      if (finalRole) {
        console.log(`✅ fetchUserRole: Final role set to: ${finalRole}`);
        setUserRole(finalRole);
      } else {
        console.log('❌ fetchUserRole: No valid role found in either collection');
        setUserRole(null);
      }
      
    } catch (error: any) {
      // Only log errors that aren't related to being offline
      if (error.code !== 'unavailable' && !error.message.includes('client is offline')) {
        console.error('❌ fetchUserRole: Error fetching user role:', error);
      }
      setUserRole(null);
    }
  };

  // Fetch user role when user changes
  useEffect(() => {
    if (user && isAuthenticated) {
      fetchUserRole();
    } else {
      setUserRole(null);
    }
  }, [user, isAuthenticated]);

  const login = async (email: string, password: string) => {
    // Set the ref IMMEDIATELY to prevent any race conditions
    loginInProgressRef.current = true;
    setIsLoginInProgress(true);
    
    // Add a small delay to ensure the ref is set and any pending auth state changes complete
    await new Promise(resolve => setTimeout(resolve, 100));
    
    try {
      console.log(`🔐 Starting login process for: ${email}`);
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      console.log(`✅ Firebase Auth successful for: ${email}`);
      if (userCredential.user) {
        // First check user role in Firestore before email verification
        let userData = null;
        let userExists = false;
        
        try {
          const userDocRef = doc(db, 'users', userCredential.user.uid);
          const userDoc = await getDoc(userDocRef);
          
          if (userDoc.exists()) {
            const tempUserData = userDoc.data();
            // Verify email matches to prevent cross-account access
            if (tempUserData.email === userCredential.user.email) {
              userData = tempUserData;
              userExists = true;
            }
          }
          
          // Only check gestoresZonaCaca if no valid match in users collection
          if (!userExists) {
            const gestorDocRef = doc(db, 'gestoresZonaCaca', userCredential.user.uid);
            const gestorDoc = await getDoc(gestorDocRef);
            
            if (gestorDoc.exists()) {
              const tempGestorData = gestorDoc.data();
              // Verify email matches to prevent cross-account access
              if (tempGestorData.email === userCredential.user.email) {
                userData = tempGestorData;
                userExists = true;
                
                // For gestor_caca users, update Firebase Auth profile if it's missing
                if (userData.role === 'gestor_caca' && userData.name && !userCredential.user.displayName) {
                  try {
                    await updateProfile(userCredential.user, {
                      displayName: userData.name
                    });
                  } catch (error) {
                    console.error('Error updating Firebase Auth profile for gestor_caca user during login:', error);
                  }
                }
              }
            }
          }
        } catch (firestoreError: any) {
          console.error('Firestore error during login:', firestoreError);
          
          // Handle specific Firestore permission errors
          if (firestoreError.code === 'permission-denied' || 
              firestoreError.message?.includes('Missing or insufficient permissions')) {
            console.log('Firestore permissions error - continuing with limited functionality');
            // Continue without user data - the user might still be able to login
            // The ensureUserDocumentExists function will handle creating the document if needed
          } else {
            // For other Firestore errors, re-throw to handle them properly
            throw firestoreError;
          }
        }
        
        // Check user role restrictions FIRST - regardless of email verification status
        if (userExists && userData) {
          // Check if user is an admin - block access to mobile app (regardless of email verification)
          if (userData.role === 'admin' || userData.role === 'administrador') {
            await signOut(auth); // Sign out the user
            const errorMessage = 'As contas de administrador só podem ser utilizadas no sistema web de administração. Por favor, utilize uma conta de utilizador móvel para aceder à aplicação.';
            throw new Error(errorMessage);
          }
          
          // Check if user is a technician and needs verification
          if (userData.role === 'tecnico_prorola' && !userData.verified) {
            await signOut(auth); // Sign out the user
            const errorMessage = 'A sua conta de técnico ainda não foi aprovada por um responsável ProROLA. Por favor, aguarde a aprovação antes de tentar fazer login.';
            throw new Error(errorMessage);
          }
        }
        
        // Now check email verification for non-admin, non-blocked users
        if (!userCredential.user.emailVerified) {
          router.replace('/auth/verify-email');
          throw new Error('Por favor, verifique seu email antes de fazer login. Você pode reenviar o email de verificação na próxima tela se necessário.');
        }
        
        if (!userExists) {
          // User exists in Firebase Auth but not in Firestore - this is the broken state!
          console.log('Detected broken user state: exists in Auth but not Firestore');
          
          try {
            // Use the same robust function to create the missing document
            await ensureUserDocumentExists(userCredential.user);
            console.log('User document created successfully during login');
            
            } catch (error) {
            console.error('Failed to create user document during login:', error);
            await signOut(auth);
            throw new Error('Erro ao criar conta de utilizador. Por favor, tente novamente ou contacte o suporte.');
          }
          
          // Now re-fetch the document to check the role (outside try-catch for document creation)
          const newUserDocRef = doc(db, 'users', userCredential.user.uid);
          const newUserDoc = await getDoc(newUserDocRef);
          if (newUserDoc.exists()) {
            const newUserData = newUserDoc.data();
          
            // If user was created as technician, block login until approved
            if (newUserData.role === 'tecnico_prorola' && !newUserData.verified) {
            await signOut(auth);
            throw new Error('A sua conta de técnico ainda não foi aprovada por um responsável ProROLA. Por favor, aguarde a aprovação antes de tentar fazer login.');
            }
          }
        }

        setUser(userCredential.user);
        setIsAuthenticated(true);
        await AsyncStorage.setItem('isAuthenticated', 'true');
        
        // Store the last successful login email for session restoration
        const loginEmail = userCredential.user.email || '';
        console.log(`📧 About to store lastLoginEmail: ${loginEmail}`);
        await AsyncStorage.setItem('lastLoginEmail', loginEmail);
        console.log(`📧 Successfully stored lastLoginEmail: ${loginEmail}`);
        
        // Verify storage worked
        const verifyStored = await AsyncStorage.getItem('lastLoginEmail');
        console.log(`📧 Verified stored lastLoginEmail: ${verifyStored}`);
        
        // Update lastLogin timestamp in Firestore
        try {
          // Determine the correct collection based on user data
          let collectionName = 'users';
          let targetUserDocRef = doc(db, 'users', userCredential.user.uid);
          
          // If user is gestor_caca, use gestoresZonaCaca collection
          if (userData && (userData as any).role === 'gestor_caca') {
            collectionName = 'gestoresZonaCaca';
            targetUserDocRef = doc(db, 'gestoresZonaCaca', userCredential.user.uid);
          }
          
          const loginData = {
            lastLogin: serverTimestamp(),
            updated_at: serverTimestamp()
          };
          
          console.log(`Attempting to update lastLogin in ${collectionName} for user ${userCredential.user.uid}`);
          console.log('Login data to set:', loginData);
          
          await setDoc(targetUserDocRef, loginData, { merge: true });
          
          console.log(`✅ Successfully updated lastLogin timestamp in ${collectionName} collection for user ${userCredential.user.uid}`);
          
          // Verify the update by reading the document back
          const updatedDoc = await getDoc(targetUserDocRef);
          if (updatedDoc.exists()) {
            const updatedData = updatedDoc.data();
            console.log('Verified updated document data:', {
              lastLogin: updatedData.lastLogin,
              updated_at: updatedData.updated_at,
              role: updatedData.role
            });
          }
        } catch (error) {
          console.error('❌ Error updating lastLogin timestamp:', error);
          // Don't throw error - login should still succeed even if timestamp update fails
        }
        
        // Check if user needs to set up their profile
        const needsProfileSetup = !userCredential.user.displayName;
        if (needsProfileSetup) {
          await AsyncStorage.setItem('needsProfileSetup', 'true');
          router.replace('/(tabs)/profile');
        } else {
          router.replace('/(tabs)');
        }
      }
    } catch (error: any) {
      // Handle specific Firebase Auth error codes
      if (error.code === 'auth/user-not-found') {
        throw new Error('Não existe conta com este email. Por favor, verifique o email introduzido ou registe-se.');
      }
      if (error.code === 'auth/wrong-password') {
        throw new Error('Palavra-passe incorreta. Por favor, tente novamente.');
      }
      if (error.code === 'auth/invalid-credential') {
        // Try to provide more helpful feedback by checking if email format is valid
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          throw new Error('Formato de email inválido.');
        }
        
        // Check if email looks like a common typo
        const commonDomains = ['gmail.com', 'hotmail.com', 'outlook.com', 'yahoo.com', 'sapo.pt', 'clix.pt'];
        const emailDomain = email.split('@')[1]?.toLowerCase();
        
        if (emailDomain && !commonDomains.includes(emailDomain)) {
          // For uncommon domains, suggest checking the email
          throw new Error('INFO:Verifique o domínio do email (@' + emailDomain + ') ou use "Esqueceu a password?".');
        }
        
        throw new Error('INFO:Dados incorretos. Verifique o email e password ou use "Esqueceu a password?".');
      }
      if (error.code === 'auth/invalid-email') {
        throw new Error('Formato de email inválido.');
      }
      if (error.code === 'auth/user-disabled') {
        throw new Error('Esta conta foi desativada. Por favor, contacte o suporte.');
      }
      if (error.code === 'auth/too-many-requests') {
        throw new Error('Demasiadas tentativas de login. Por favor, aguarde alguns minutos antes de tentar novamente ou redefina a sua palavra-passe.');
      }
      
      // Handle Firestore permission errors
      if (error.code === 'permission-denied' || 
          error.message?.includes('Missing or insufficient permissions')) {
        console.error('Firestore permissions error during login:', error);
        throw new Error('Erro de permissões. Por favor, tente novamente ou contacte o suporte se o problema persistir.');
      }
      
      // Don't log expected error messages
      if (!error.message.includes('verifique seu email') && 
          !error.message.includes('aguarde a aprovação') && 
          !error.message.includes('administrador só podem ser utilizadas')) {
        console.error('Login error:', error);
      }
      throw error;
    } finally {
      loginInProgressRef.current = false;
      setIsLoginInProgress(false);
    }
  };

  const register = async (email: string, password: string, registrationType: string) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      if (userCredential.user) {
        setUser(userCredential.user);

        // Map display registrationType to database role value
        let role = 'colaborador'; // Default role
        if (registrationType === 'Colaborador') {
          role = 'colaborador';
        } else if (registrationType === 'Técnico ProROLA') {
          role = 'tecnico_prorola';
        } else if (registrationType === 'Zona de Caça') {
          role = 'zona_de_caca'; 
        }

        // Store registration info temporarily until email is verified
        const pendingData = {
          email: userCredential.user.email,
          role: role,
          uid: userCredential.user.uid
        };
        
        await AsyncStorage.setItem('pendingUserRegistration', JSON.stringify(pendingData));

        await sendEmailVerification(userCredential.user, {
          url: 'https://prorola.app/email/',
          handleCodeInApp: false
        });
        router.replace('/auth/verify-email');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      throw new Error(getErrorMessage(error.code));
    }
  };

  const resendVerificationEmail = async () => {
    if (!user) throw new Error('No user logged in');
    try {
      await user.reload();
      
      if (user.emailVerified) {
        throw new Error('Email já foi verificado. Por favor, faça login.');
      }

      const lastEmailSent = await AsyncStorage.getItem('lastVerificationEmailSent');
      const now = Date.now();
      
      // Check Firebase's rate limit (max 1 email per minute)
      const lastFirebaseAttempt = await AsyncStorage.getItem('lastFirebaseEmailAttempt');
      if (lastFirebaseAttempt) {
        const timeSinceLastAttempt = now - parseInt(lastFirebaseAttempt);
        if (timeSinceLastAttempt < 60000) { // 1 minute in milliseconds
          throw new Error('Muitas tentativas. Por favor, aguarde um minuto antes de tentar novamente.');
        }
      }

      // Check our app's cooldown (30 seconds)
      if (lastEmailSent && (now - parseInt(lastEmailSent)) < 30000) {
        const remainingTime = Math.ceil((30000 - (now - parseInt(lastEmailSent))) / 1000);
        throw new Error(`Por favor, aguarde ${remainingTime} segundos antes de reenviar o email.`);
      }

      // Store the attempt timestamp before making the request
      await AsyncStorage.setItem('lastFirebaseEmailAttempt', now.toString());

      await sendEmailVerification(user, {
        url: 'https://prorola.app/email/',
        handleCodeInApp: false
      });

      // Only store the successful send timestamp after the email is sent
      await AsyncStorage.setItem('lastVerificationEmailSent', now.toString());
    } catch (error: any) {
      console.error('Error sending verification email:', error);
      
      if (error.code === 'auth/too-many-requests') {
        // Store the failed attempt to prevent rapid retries
        const now = Date.now();
        await AsyncStorage.setItem('lastFirebaseEmailAttempt', now.toString());
        throw new Error('Muitas tentativas. Por favor, aguarde alguns minutos antes de tentar novamente.');
      } else if (error.code === 'auth/invalid-email') {
        throw new Error('Email inválido.');
      } else if (error.code === 'auth/user-not-found') {
        throw new Error('Usuário não encontrado.');
      } else if (error.message) {
        throw new Error(error.message);
      } else {
        throw new Error('Erro ao reenviar email de verificação. Por favor, tente novamente em alguns minutos.');
      }
    }
  };

  const logout = async () => {
    // Prevent multiple simultaneous logout calls
    if (logoutInProgressRef.current) {
      return;
    }
    
    logoutInProgressRef.current = true;
    
    try {
      await signOut(auth);
      setIsAuthenticated(false);
      setUser(null);
      setUserRole(null);
      await AsyncStorage.removeItem('isAuthenticated');
      await AsyncStorage.removeItem('pendingUserRegistration');
      await AsyncStorage.removeItem('lastLoginEmail'); // Clear stored login email
      
      // Add a small delay to ensure the auth state is updated before navigation
      setTimeout(() => {
        try {
          router.replace('/auth/login');
        } catch (navError) {
          console.error('Navigation error during logout:', navError);
          // Force a page reload as fallback if navigation fails
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
        } finally {
          logoutInProgressRef.current = false;
        }
      }, 100);
    } catch (error: any) {
      console.error('Logout error:', error);
      logoutInProgressRef.current = false;
      throw new Error('Failed to logout. Please try again.');
    }
  };

  const resetPassword = async (email: string) => {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      console.error('Reset password error:', error);
      throw new Error(getErrorMessage(error.code));
    }
  };

  const updateUserProfile = async (displayName?: string, photoURI?: string | null) => {
    try {
      if (!auth.currentUser) throw new Error('No user logged in');

      let photoURL = auth.currentUser.photoURL;
      
      // Handle photo deletion (when photoURI is explicitly null)
      if (photoURI === null) {
        try {
          // Delete all existing profile images from storage
          const userProfilePath = `profile_images/${auth.currentUser.uid}`;
          const profileImagesRef = ref(storage, userProfilePath);
          const listResult = await listAll(profileImagesRef);
          
                    // Delete all existing profile images
          await Promise.all(listResult.items.map(item => deleteObject(item)));

        } catch (error: any) {
          // Continue with profile update even if storage deletion fails
        }
        
        // Set photoURL to null to remove from profile  
        photoURL = null;
      }
      // Handle photo upload (when photoURI is provided)
      else if (photoURI) {
        try {
          // Create a unique filename using timestamp
          const timestamp = new Date().getTime();
          const filename = `${timestamp}.jpg`;
          const userProfilePath = `profile_images/${auth.currentUser.uid}`;
          const storageRef = ref(storage, `${userProfilePath}/${filename}`);

          // List all existing profile images for this user
          const profileImagesRef = ref(storage, userProfilePath);
          const listResult = await listAll(profileImagesRef);
          
          // Delete all existing profile images
          await Promise.all(listResult.items.map(item => deleteObject(item)));

          // Convert local file URI to blob using fetch
          const response = await fetch(photoURI);
          const blob = await response.blob();

          // Upload the new file
          await uploadBytes(storageRef, blob, {
            contentType: 'image/jpeg',
          });

          // Get the download URL
          photoURL = await getDownloadURL(storageRef);

        } catch (error: any) {
          console.error('Detailed upload error:', error);
          if (error instanceof TypeError && error.message === 'Network request failed') {
            throw new Error('Network error. Please check your internet connection and try again.');
          } else if (error.code === 'storage/unauthorized') {
            throw new Error('Permission denied. Please check Firebase Storage rules.');
          } else if (error.code === 'storage/unknown') {
            throw new Error('Upload failed. Please try again later.');
          }
          throw error;
        }
      }

      // For deletion, try both null and empty string approaches
      if (photoURL === null) {
        // Try first with null
        try {
          await updateProfile(auth.currentUser, {
            displayName: displayName || auth.currentUser.displayName,
            photoURL: null,
          });
          
          // If null didn't work, try empty string
          if (auth.currentUser.photoURL !== null) {
            await updateProfile(auth.currentUser, {
              displayName: displayName || auth.currentUser.displayName,
              photoURL: '',
            });
          }
        } catch (error) {
          throw error;
        }
      } else {
        // Normal update for non-deletion cases
        await updateProfile(auth.currentUser, {
          displayName: displayName || auth.currentUser.displayName,
          photoURL: photoURL,
        });
      }

      // Update Firestore document if displayName was provided (before reloading user)
      if (displayName) {
        try {
          const userDocRef = doc(db, 'users', auth.currentUser.uid);
          await setDoc(userDocRef, {
            name: displayName,
            updated_at: serverTimestamp()
          }, { merge: true });
        } catch (firestoreError) {
          console.error('Failed to update Firestore name field:', firestoreError);
          // Don't throw error - profile update in Auth was successful
        }
      }

      // Force reload the user to get the updated profile
      await auth.currentUser.reload();
      
      // Update user state directly without clearing it first (prevents auth state loss)
      setUser({ ...auth.currentUser } as User);
    } catch (error: any) {
      console.error('Update profile error:', {
        message: error.message,
        code: error.code,
        stack: error.stack,
        name: error.name
      });
      throw error;
    }
  };

  const reauthenticate = async (password: string) => {
    try {
      if (!auth.currentUser || !auth.currentUser.email) {
        throw new Error('No user logged in');
      }
      const credential = EmailAuthProvider.credential(auth.currentUser.email, password);
      await reauthenticateWithCredential(auth.currentUser, credential);
    } catch (error: any) {
      console.error('Reauthentication error:', error);
      if (error.code === 'auth/invalid-credential' || error.code === 'auth/wrong-password') {
        throw new Error('Password atual incorreta.');
      }
      throw error;
    }
  };

  const deleteAccount = async (password?: string) => {
    try {
      if (!auth.currentUser) throw new Error('No user logged in');
      
      if (password) {
        await reauthenticate(password);
      }
      
      // Before deleting the user, you might want to delete their Firestore data
      const userDocRef = doc(db, 'users', auth.currentUser.uid);
      await setDoc(userDocRef, { deleted: true, deleted_at: serverTimestamp() }, { merge: true }); // Soft delete
      // Or hard delete: await deleteDoc(userDocRef);

      // Delete profile images from storage
      const userProfilePath = `profile_images/${auth.currentUser.uid}`;
      const profileImagesRef = ref(storage, userProfilePath);
      const listResult = await listAll(profileImagesRef);
      await Promise.all(listResult.items.map(item => deleteObject(item)));
      console.log('Profile images deleted before account deletion');
      
      await auth.currentUser.delete();
      setIsAuthenticated(false);
      setUser(null);
      await AsyncStorage.clear(); // Clear all async storage on account deletion
      router.replace('/auth/login');
    } catch (error: any) {
      console.error('Delete account error:', error);
      if (error.code === 'auth/requires-recent-login') {
        throw new Error('auth/requires-recent-login');
      }
      throw new Error('Falha ao eliminar conta. Por favor, tente novamente.');
    }
  };

  const updatePassword = async (currentPassword: string, newPassword: string) => {
    try {
      if (!auth.currentUser) throw new Error('No user logged in');
      
      // First reauthenticate
      await reauthenticate(currentPassword);
      
      // Then update password
      await firebaseUpdatePassword(auth.currentUser, newPassword);
    } catch (error: any) {
      console.error('Update password error:', error);
      if (error.code === 'auth/requires-recent-login') {
        throw new Error('Para sua segurança, por favor termine sessão e volte a entrar antes de alterar a palavra-passe.');
      }
      // Pass through the error message from reauthenticate
      throw error;
    }
  };

  // Helper function to get user-friendly error messages
  const getErrorMessage = (errorCode: string): string => {
    switch (errorCode) {
      case 'auth/invalid-credential':
        return 'Email ou palavra-passe incorretos.';
      case 'auth/invalid-email':
        return 'Email inválido.';
      case 'auth/user-disabled':
        return 'Esta conta foi desativada.';
      case 'auth/user-not-found':
        return 'Não existe conta com este email.';
      case 'auth/wrong-password':
        return 'Palavra-passe incorreta.';
      case 'auth/email-already-in-use':
        return 'Este email já está registado.';
      case 'auth/weak-password':
        return 'A palavra-passe deve ter pelo menos 6 caracteres.';
      case 'auth/operation-not-allowed':
        return 'Autenticação por email/password não está ativada.';
      case 'auth/unverified-email':
        return 'Por favor, verifique seu email antes de fazer login.';
      case 'auth/too-many-requests':
        return 'Demasiadas tentativas de login. Por favor, aguarde alguns minutos antes de tentar novamente.';
      default:
        return 'Ocorreu um erro. Por favor, tente novamente.';
    }
  };

  return (
    <AuthContext.Provider 
      value={{ 
        isAuthenticated, 
        user,
        userRole,
        isLoading,
        login, 
        register, 
        logout,
        resetPassword,
        updateUserProfile,
        deleteAccount,
        reauthenticate,
        updatePassword,
        resendVerificationEmail,
        fetchUserRole,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Utility function to check if user is gestor de caça (hunt manager)
export function isGestorDeCaca(userRole: string | null): boolean {
  return userRole === 'gestor_caca';
}

// Utility function to determine if location permission should be requested every time
export function shouldRequestLocationEveryTime(userRole: string | null): boolean {
  return isGestorDeCaca(userRole);
} 