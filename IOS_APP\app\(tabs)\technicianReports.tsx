import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { collection, query, where, getDocs, deleteDoc, doc, orderBy } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { router } from 'expo-router';

interface TechnicianReport {
  id: string;
  protocol: string;
  sessionId: string;
  observersCount: number;
  startTime: any;
  endTime: any;
  sessionDuration: number;
  deviceInfo: any;
  createdAt: any;
  userName: string;
  userRole: string;
  type: string;
}

const TechnicianReports = () => {
  const { user } = useAuth();
  const [reports, setReports] = useState<TechnicianReport[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadTechnicianReports();
    }
  }, [user]);

  const loadTechnicianReports = async () => {
    try {
      setLoading(true);
      const reportsRef = collection(db, 'reports');
      const q = query(
        reportsRef,
        where('userId', '==', user?.uid),
        where('type', 'in', ['tecnico_monitoring_report', 'monitoring_session_summary']),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const reportsData: TechnicianReport[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        reportsData.push({
          id: doc.id,
          protocol: data.protocol || 'N/A',
          sessionId: data.sessionId || 'N/A',
          observersCount: data.observersCount || 0,
          startTime: data.startTime,
          endTime: data.endTime,
          sessionDuration: data.sessionDuration || 0,
          deviceInfo: data.deviceInfo || {},
          createdAt: data.createdAt,
          userName: data.userName || 'N/A',
          userRole: data.userRole || 'N/A',
          type: data.type || 'N/A'
        });
      });
      
      setReports(reportsData);
    } catch (error) {
      console.error('Error loading technician reports:', error);
      Alert.alert('Erro', 'Não foi possível carregar os relatórios técnicos.');
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getProtocolDisplayName = (protocol: string) => {
    const protocolNames: { [key: string]: string } = {
      'trajeto': 'Trajeto',
      'estacoes_escuta': 'Estações de escuta',
      'metodo_mapas': 'Método dos mapas',
      'contagens_pontos': 'Contagens em pontos',
      'captura_marcacao': 'Captura e marcação',
      'acompanhamento_cacadas': 'Acompanhamento de caçadas',
      'registos_ocasionais': 'Registos ocasionais'
    };
    return protocolNames[protocol] || protocol;
  };

  const getReportTypeDisplayName = (type: string) => {
    const typeNames: { [key: string]: string } = {
      'tecnico_monitoring_report': 'Relatório de Monitorização',
      'monitoring_session_summary': 'Resumo de Sessão'
    };
    return typeNames[type] || type;
  };

  const handleDeleteReport = async (reportId: string) => {
    Alert.alert(
      'Confirmar exclusão',
      'Tem certeza que deseja excluir este relatório técnico?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteDoc(doc(db, 'reports', reportId));
              setReports(reports.filter(report => report.id !== reportId));
              Alert.alert('Sucesso', 'Relatório excluído com sucesso.');
            } catch (error) {
              console.error('Error deleting report:', error);
              Alert.alert('Erro', 'Não foi possível excluir o relatório.');
            }
          }
        }
      ]
    );
  };

  const renderReportItem = ({ item }: { item: TechnicianReport }) => {
    const createdDate = item.createdAt?.toDate ? item.createdAt.toDate() : new Date(item.createdAt);
    const startTime = item.startTime?.toDate ? item.startTime.toDate() : new Date(item.startTime);
    const endTime = item.endTime?.toDate ? item.endTime.toDate() : new Date(item.endTime);

    return (
      <TouchableOpacity
        style={styles.reportItem}
        activeOpacity={1}
      >
        <View style={styles.reportHeader}>
          <Text style={styles.reportType}>{getReportTypeDisplayName(item.type)}</Text>
          <View style={styles.reportActions}>
            <TouchableOpacity
              onPress={() => router.push({
                pathname: '/technicianMapView',
                params: { reportId: item.id, sessionId: item.sessionId }
              })}
              style={styles.mapButton}
            >
              <Ionicons name="map-outline" size={18} color="#059669" />
              <Text style={styles.mapButtonText}>Ver no Mapa</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => handleDeleteReport(item.id)}
              style={styles.deleteButton}
            >
              <Ionicons name="trash-outline" size={20} color="#dc2626" />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.sessionInfo}>
          <Text style={styles.sessionId}>Sessão: {item.sessionId}</Text>
          <Text style={styles.protocol}>{getProtocolDisplayName(item.protocol)}</Text>
        </View>
        
        <View style={styles.reportDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="people-outline" size={16} color="#6b7280" />
            <Text style={styles.detailText}>Observadores: {item.observersCount}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="time-outline" size={16} color="#6b7280" />
            <Text style={styles.detailText}>
              Duração: {formatDuration(item.sessionDuration)}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="calendar-outline" size={16} color="#6b7280" />
            <Text style={styles.detailText}>
              {createdDate.toLocaleDateString('pt-BR')} às {createdDate.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
            </Text>
          </View>
          
          {startTime && endTime && (
            <View style={styles.detailRow}>
              <Ionicons name="play-outline" size={16} color="#6b7280" />
              <Text style={styles.detailText}>
                {startTime.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })} - {endTime.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0ea5e9" />
          <Text style={styles.loadingText}>Carregando relatórios técnicos...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Relatórios Técnicos</Text>
        <Text style={styles.subtitle}>Monitorização e sessões técnicas</Text>
      </View>

      <FlatList
        data={reports}
        renderItem={renderReportItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={reports.length === 0 ? styles.emptyContainer : styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <View style={styles.emptyIcon}>
              <Ionicons name="document-text-outline" size={64} color="#cbd5e1" />
            </View>
            <Text style={styles.emptyTitle}>Nenhum relatório técnico encontrado</Text>
            <Text style={styles.emptySubtitle}>
              Os seus relatórios de monitorização técnica aparecerão aqui quando forem criados durante as sessões de campo.
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  listContainer: {
    padding: 20,
  },
  reportItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  reportType: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0ea5e9',
    backgroundColor: '#f0f9ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  reportActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0fdf4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
    borderWidth: 1,
    borderColor: '#bbf7d0',
  },
  mapButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#059669',
  },
  deleteButton: {
    padding: 4,
  },
  sessionInfo: {
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  sessionId: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 4,
  },
  protocol: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '500',
  },
  reportDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#6b7280',
  },
  emptyContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyState: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  emptyIcon: {
    marginBottom: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default TechnicianReports; 