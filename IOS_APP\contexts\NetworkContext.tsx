import React, { createContext, useContext, ReactNode, useState } from 'react';
import { useNetworkConnectivity, NetworkState } from '@/hooks/useNetworkConnectivity';
import CustomAlert from '@/components/CustomAlert';
import { t } from '@/config/translations';

interface NetworkContextType extends NetworkState {
  hasInternetConnection: () => boolean;
  getConnectionType: () => string;
  checkNetworkAndWarn: (operation: string, onProceed?: () => void) => boolean;
}

const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

export function NetworkProvider({ children }: { children: ReactNode }) {
  const networkState = useNetworkConnectivity();
  const [showNetworkAlert, setShowNetworkAlert] = useState(false);
  const [networkAlertMessage, setNetworkAlertMessage] = useState('');

  // Function to check network and show warning if needed
  const checkNetworkAndWarn = (operation: string, onProceed?: () => void): boolean => {
    if (!networkState.hasInternetConnection()) {
      setNetworkAlertMessage(
        `Não é possível ${operation} sem ligação à Internet. Por favor, verifique a sua ligação e tente novamente.`
      );
      setShowNetworkAlert(true);
      return false;
    }
    
    if (onProceed) {
      onProceed();
    }
    return true;
  };

  const contextValue: NetworkContextType = {
    ...networkState,
    checkNetworkAndWarn,
  };

  return (
    <NetworkContext.Provider value={contextValue}>
      {children}
      <CustomAlert
        visible={showNetworkAlert}
        type="warning"
        title={t('alerts.network.title')}
        message={networkAlertMessage}
        onClose={() => setShowNetworkAlert(false)}
      />
    </NetworkContext.Provider>
  );
}

export function useNetwork() {
  const context = useContext(NetworkContext);
  if (context === undefined) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  return context;
} 