import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, Dimensions, Platform, Animated } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome } from '@expo/vector-icons';
import MapView, { Mark<PERSON>, PROVIDER_GOOGLE, Polyline } from 'react-native-maps';
import * as Location from 'expo-location';

interface ContactPlacementModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (observerLocation: LocationCoordinate, contactLocation: LocationCoordinate, distance: number) => void;
  userLocation: Location.LocationObject | null;
  currentBearing?: number;
  currentZoom?: number;
  currentMapType?: 'standard' | 'satellite';
}

interface LocationCoordinate {
  latitude: number;
  longitude: number;
}

const ContactPlacementModal: React.FC<ContactPlacementModalProps> = ({
  visible,
  onClose,
  onConfirm,
  userLocation,
  currentBearing,
  currentZoom,
  currentMapType
}) => {
  const mapRef = useRef<MapView>(null);
  const [mapCenter, setMapCenter] = useState<LocationCoordinate | null>(null);
  const [currentRegion, setCurrentRegion] = useState<any>(null);
  const [distance, setDistance] = useState<number>(0);
  const [bearing, setBearing] = useState<number>(0);
  const [mapType, setMapType] = useState<'standard' | 'satellite'>(currentMapType || 'standard');
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const screenData = Dimensions.get('window');
  const regionUpdateTimer = useRef<NodeJS.Timeout | null>(null);

  // Initialize map center when modal opens
  useEffect(() => {
    if (visible && userLocation) {
      console.log('ContactPlacementModal received currentZoom:', currentZoom);
      console.log('ContactPlacementModal received currentBearing:', currentBearing);
      
      const initialCenter = {
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude
      };
      
      // Start with crosshair exactly on user location
      setMapCenter(initialCenter);
      setCurrentRegion(initialCenter); // Set currentRegion to user location
      setDistance(0); // Distance should be 0 when crosshair is on user
      setBearing(0); // Bearing should be 0 when crosshair is on user
      
      // Animate to exact same camera position as main screen after map loads
      setTimeout(() => {
        if (mapRef.current) {
          console.log('Animating ContactPlacementModal camera to zoom:', currentZoom, 'bearing:', currentBearing);
          mapRef.current.animateCamera({
            center: {
              latitude: userLocation.coords.latitude,
              longitude: userLocation.coords.longitude,
            },
            pitch: 0,
            heading: currentBearing || 0,
            altitude: 500,
            zoom: currentZoom || 17,
          }, { duration: 500 });
        }
      }, 200);
    }
  }, [visible, userLocation, currentBearing, currentZoom]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (regionUpdateTimer.current) {
        clearTimeout(regionUpdateTimer.current);
      }
    };
  }, []);

  // Update map type when currentMapType prop changes
  useEffect(() => {
    if (currentMapType) {
      setMapType(currentMapType);
    }
  }, [currentMapType]);



  // Calculate distance between two points in meters
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    // If coordinates are essentially the same (within very small tolerance)
    const latDiff = Math.abs(lat1 - lat2);
    const lngDiff = Math.abs(lng1 - lng2);
    
    if (latDiff < 0.00001 && lngDiff < 0.00001) {
      return 0;
    }
    
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lng2-lng1) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    const distance = R * c;
    
    // Round to nearest meter to avoid showing decimals for short distances
    return Math.round(distance);
  };

  // Calculate bearing (direction) from observer to contact in degrees
  const calculateBearing = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    // If coordinates are essentially the same, bearing is 0
    const latDiff = Math.abs(lat1 - lat2);
    const lngDiff = Math.abs(lng1 - lng2);
    
    if (latDiff < 0.00001 && lngDiff < 0.00001) {
      return 0;
    }

    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δλ = (lng2-lng1) * Math.PI/180;

    const x = Math.sin(Δλ) * Math.cos(φ2);
    const y = Math.cos(φ1) * Math.sin(φ2) - Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);

    const θ = Math.atan2(x, y);
    
    // Convert to degrees and normalize to 0-360
    const bearing = (θ * 180/Math.PI + 360) % 360;
    
    return Math.round(bearing);
  };

  // Handle region change during map movement for real-time updates
  const handleRegionChange = (region: any) => {
    setCurrentRegion(region);
    
    // Clear previous timer
    if (regionUpdateTimer.current) {
      clearTimeout(regionUpdateTimer.current);
    }
    
    // Update map center and distance immediately for visual feedback
    const newCenter = {
      latitude: region.latitude,
      longitude: region.longitude
    };
    setMapCenter(newCenter);

    // Calculate distance and bearing from user location to crosshair in real-time
    if (userLocation) {
      // Check if the region is very close to user location
      const latDiff = Math.abs(region.latitude - userLocation.coords.latitude);
      const lngDiff = Math.abs(region.longitude - userLocation.coords.longitude);
      
      let dist, bear;
      // Use reasonable tolerance for genuine proximity detection
      if (latDiff < 0.00005 && lngDiff < 0.00005) {
        // Treat as same location when very close (within ~5 meters)
        dist = 0;
        bear = 0;
      } else {
        dist = calculateDistance(
          userLocation.coords.latitude,
          userLocation.coords.longitude,
          region.latitude,
          region.longitude
        );
        bear = calculateBearing(
          userLocation.coords.latitude,
          userLocation.coords.longitude,
          region.latitude,
          region.longitude
        );
      }
      
      setDistance(dist);
      setBearing(bear);
    }
  };

  // Handle region change to update crosshair position
  const handleRegionChangeComplete = (region: any) => {
    setCurrentRegion(region);
    const newCenter = {
      latitude: region.latitude,
      longitude: region.longitude
    };
    setMapCenter(newCenter);

    // Calculate distance and bearing from user location to crosshair
    if (userLocation) {
      // Check if the region is very close to user location
      const latDiff = Math.abs(region.latitude - userLocation.coords.latitude);
      const lngDiff = Math.abs(region.longitude - userLocation.coords.longitude);
      
      let dist, bear;
      // Use reasonable tolerance for genuine proximity detection
      if (latDiff < 0.00005 && lngDiff < 0.00005) {
        // Treat as same location when very close (within ~5 meters)
        dist = 0;
        bear = 0;
      } else {
        dist = calculateDistance(
          userLocation.coords.latitude,
          userLocation.coords.longitude,
          region.latitude,
          region.longitude
        );
        bear = calculateBearing(
          userLocation.coords.latitude,
          userLocation.coords.longitude,
          region.latitude,
          region.longitude
        );
      }
      
      setDistance(dist);
      setBearing(bear);
    }
  };

  // Format distance display
  const formatDistance = (meters: number): string => {
    // If distance is very small (within 3 meters), show as 0m
    if (meters < 3) {
      return '0m';
    }
    
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    } else {
      return `${(meters / 1000).toFixed(2)}km`;
    }
  };

  // Get the exact center coordinates of the map view
  const getMapCenter = (): LocationCoordinate | null => {
    // If we haven't moved the map yet (currentRegion matches user location), 
    // use user location as the contact location
    if (userLocation && currentRegion) {
      const latDiff = Math.abs(currentRegion.latitude - userLocation.coords.latitude);
      const lngDiff = Math.abs(currentRegion.longitude - userLocation.coords.longitude);
      
      // If the region is very close to user location (within ~5 meters)
      if (latDiff < 0.00005 && lngDiff < 0.00005) {
        return {
          latitude: userLocation.coords.latitude,
          longitude: userLocation.coords.longitude
        };
      }
    }
    
    // Use the most recent region data as it represents the current view center
    if (currentRegion) {
      return {
        latitude: currentRegion.latitude,
        longitude: currentRegion.longitude
      };
    }
    return mapCenter;
  };

  // Handle confirm button press
  const handleConfirm = () => {
    if (!userLocation) return;

    // Get the most accurate center coordinates
    const centerToUse = getMapCenter();
    
    if (!centerToUse) return;

    const observerLocation = {
      latitude: userLocation.coords.latitude,
      longitude: userLocation.coords.longitude
    };

    // Recalculate distance with exact coordinates
    const finalDistance = calculateDistance(
      userLocation.coords.latitude,
      userLocation.coords.longitude,
      centerToUse.latitude,
      centerToUse.longitude
    );

    onConfirm(observerLocation, centerToUse, finalDistance);
  };

  // Center map on user location without coordinate drift
  const centerOnUser = () => {
    if (userLocation && mapRef.current) {
      // Use animateToRegion instead of animateCamera to avoid coordinate drift
      mapRef.current.animateToRegion({
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude,
        latitudeDelta: currentZoom ? (360 / Math.pow(2, currentZoom + 8)) : 0.005,
        longitudeDelta: currentZoom ? (360 / Math.pow(2, currentZoom + 8)) : 0.005,
      }, 1000);
      
      // Reset distance and region to user location
      setDistance(0);
      setCurrentRegion({
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude
      });
      setMapCenter({
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude
      });
    }
  };

  // Toggle map type between standard and satellite
  const toggleMapType = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      setMapType(prev => prev === 'standard' ? 'satellite' : 'standard');
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    });
  };

  if (!userLocation) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <View style={styles.container}>
        <StatusBar style="light" />
        {/* Header */}
        <SafeAreaView edges={['top']} style={styles.headerSafeArea}>
          <View style={styles.header}>
            <FontAwesome name="map-marker" size={20} color="#fff" style={styles.headerIcon} />
            <Text style={styles.headerTitle}>Localização do Contacto</Text>
          </View>
        </SafeAreaView>

        {/* Instructions */}
        <View style={styles.instructionsContainer}>
          <FontAwesome name="crosshairs" size={14} color="#0996a8" style={styles.instructionsIcon} />
          <Text style={styles.instructionsText}>
            Aponte a mira para o local do contacto
          </Text>
        </View>

        {/* Map Container */}
        <View style={styles.mapContainer}>
          <Animated.View style={[styles.map, { opacity: fadeAnim }]}>
            <MapView
              ref={mapRef}
              style={styles.map}
              provider={PROVIDER_GOOGLE}
              mapType={mapType}
              initialCamera={{
                center: {
                  latitude: userLocation.coords.latitude,
                  longitude: userLocation.coords.longitude,
                },
                pitch: 0, // Flat view to avoid coordinate drift
                heading: currentBearing || 0, // Same bearing as main screen
                altitude: 500,
                zoom: currentZoom || 17, // Use exact same zoom as main screen
              }}
              onRegionChangeComplete={handleRegionChangeComplete}
              onRegionChange={handleRegionChange}
              showsUserLocation={false}
              showsMyLocationButton={false}
              showsCompass={true}
              rotateEnabled={true}
              pitchEnabled={false}
              scrollEnabled={true}
              zoomEnabled={true}
            >
              {/* User location marker (observer) */}
              <Marker
                coordinate={{
                  latitude: userLocation.coords.latitude,
                  longitude: userLocation.coords.longitude
                }}
                title="Sua Localização"
                description="Posição do observador"
                anchor={{ x: 0.5, y: 0.5 }}
                centerOffset={{ x: 0, y: 0 }}
              >
                <View style={styles.userMarker}>
                  <FontAwesome name="user" size={16} color="#fff" />
                </View>
              </Marker>

              {/* Line from user to crosshair position */}
              {userLocation && distance >= 3 && (() => {
                const targetCoords = currentRegion ? {
                  latitude: currentRegion.latitude,
                  longitude: currentRegion.longitude
                } : mapCenter;
                
                if (!targetCoords) return null;
                
                return (
                  <>
                    {/* White outline */}
                    <Polyline
                      coordinates={[
                        {
                          latitude: userLocation.coords.latitude,
                          longitude: userLocation.coords.longitude
                        },
                        targetCoords
                      ]}
                      strokeColor="#FFFFFF"
                      strokeWidth={5}
                      zIndex={1}
                    />
                    {/* Main line */}
                    <Polyline
                      coordinates={[
                        {
                          latitude: userLocation.coords.latitude,
                          longitude: userLocation.coords.longitude
                        },
                        targetCoords
                      ]}
                      strokeColor="#0996a8"
                      strokeWidth={3}
                      lineDashPattern={[8, 4]}
                      zIndex={2}
                    />
                  </>
                );
              })()}
            </MapView>
          </Animated.View>

          {/* Crosshair overlay */}
          <View style={styles.crosshairContainer}>
            <View style={styles.crosshair}>
              <FontAwesome name="crosshairs" size={24} color="#0996a8" />
            </View>
          </View>

          {/* Map controls */}
          <View style={styles.mapControls}>
            <TouchableOpacity style={styles.controlButton} onPress={centerOnUser}>
              <FontAwesome name="crosshairs" size={20} color="#0996a8" />
            </TouchableOpacity>
            
            <TouchableOpacity style={[styles.controlButton, { marginTop: 8 }]} onPress={toggleMapType}>
              <FontAwesome 
                name={mapType === 'standard' ? 'globe' : 'map'} 
                size={20} 
                color="#0996a8" 
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Direction and Distance info */}
        <View style={styles.infoContainer}>
          <View style={styles.infoRow}>
            <FontAwesome name="compass" size={16} color="#666" />
            <Text style={styles.infoText}>
              Direção: {bearing}°
            </Text>
          </View>
          <View style={styles.infoRow}>
            <FontAwesome name="arrows-h" size={16} color="#666" />
            <Text style={styles.infoText}>
              Distância do observador: {formatDistance(distance)}
            </Text>
          </View>
        </View>

        {/* Bottom buttons */}
        <SafeAreaView edges={['bottom']} style={styles.buttonSafeArea}>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <FontAwesome name="times" size={16} color="#fff" style={styles.buttonIcon} />
            <Text style={styles.cancelButtonText}>Cancelar</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.confirmButton} onPress={handleConfirm}>
            <FontAwesome name="check" size={16} color="#fff" style={styles.buttonIcon} />
            <Text style={styles.confirmButtonText}>Confirmar</Text>
          </TouchableOpacity>
        </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f4f4f4',
  },
  headerSafeArea: {
    backgroundColor: '#0996a8',
  },
  header: {
    backgroundColor: '#0996a8',
    paddingBottom: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerIcon: {
    marginRight: 12,
  },
  instructionsContainer: {
    backgroundColor: '#e8f4f8',
    padding: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#d0e6ed',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  instructionsIcon: {
    marginRight: 8,
  },
  instructionsText: {
    fontSize: 12,
    color: '#0996a8',
    fontWeight: '500',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  crosshairContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  crosshair: {
    backgroundColor: 'rgba(255, 255, 255, 0.75)',
    borderRadius: 20, // Exactly half of width/height for perfect circle
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    // Add subtle border for better definition
    borderWidth: 1,
    borderColor: 'rgba(9, 150, 168, 0.3)',
  },
  userMarker: {
    backgroundColor: '#007AFF',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  mapControls: {
    position: 'absolute',
    right: 15,
    top: 15,
  },
  controlButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    width: 40,
    height: 40,
    borderRadius: 2,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 1,
    shadowRadius: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  infoContainer: {
    padding: 5,
    backgroundColor: '#f8f9fa',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 5,
  },
  infoText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
    marginLeft: 8,
  },
  buttonSafeArea: {
    backgroundColor: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 15,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#6B7280',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#0996a8',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ContactPlacementModal; 