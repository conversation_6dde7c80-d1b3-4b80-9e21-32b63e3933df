import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Modal,
  Platform,
  Image,
  ScrollView,
  Dimensions,
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>er, PROVIDER_GOOGLE, MapType, Polyline, Callout } from 'react-native-maps';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
import { Timestamp, DocumentData } from 'firebase/firestore';
import { db } from '@/config/firebase';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import LoadingImage from './LoadingImage';

interface SessionData {
  sessionId: string;
  pathCoordinates: Array<{
    latitude: number;
    longitude: number;
    timestamp?: number;
  }>;
  startTime?: number | Timestamp;
  endTime?: number | Timestamp;
  totalDistance?: number;
}

interface ContactEvent {
  sessionId: string;
  timestamp: number | Timestamp;
  observerLocation: {
    latitude: number;
    longitude: number;
  };
  contactLocation: {
    latitude: number;
    longitude: number;
  };
  distance: number;
  bearing?: number;
  circumstances?: any;
  contactLocationDetails?: any;
  images?: string[];
}

interface TechnicianReport extends DocumentData {
  id: string;
  userId: string;
  userName: string;
  userEmail?: string;
  userRole: 'tecnico_prorola';
  type: 'tecnico_monitoring_report' | 'monitoring_session_summary';
  protocol: string;
  sessionId: string;
  comment?: string;
  createdAt: Timestamp | null | undefined;
  images?: string[];
  location: {
    latitude: number;
    longitude: number;
  };
  deviceInfo: {
    platform: string;
    model?: string;
    os?: string;
    version?: string;
  };
  weather?: {
    temperature: number;
    feelsLike: number;
    description: string;
    humidity: number;
    pressure: number;
    windSpeed: number;
    windDirection: number;
    visibility: number;
    cloudiness: number;
    icon: string;
    timestamp: number;
    sunrise: number;
    sunset: number;
  };
  observersCount: number;
  startTime: Timestamp | null | undefined;
  endTime?: Timestamp | null | undefined;
  sessionDuration?: number;
  contactEventsCount?: number;
}

interface TechnicianMapScreenProps {
  visible: boolean;
  report: TechnicianReport | null;
  onClose: () => void;
}

const pt = {
  close: 'Fechar',
  satellite: 'Satélite',
  standard: 'Mapa',
  technicianReport: 'Relatório Técnico',
  trajeto: 'Trajeto',
  estacoes_escuta: 'Estações de escuta',
  metodo_mapas: 'Método dos mapas',
  contagens_pontos: 'Contagens em pontos',
  captura_marcacao: 'Captura e marcação',
  acompanhamento_cacadas: 'Acompanhamento de caçadas',
  registos_ocasionais: 'Registos ocasionais'
};

export default function TechnicianMapScreen({ visible, report, onClose }: TechnicianMapScreenProps) {
  const [mapReady, setMapReady] = useState(false);
  const [mapType, setMapType] = useState<MapType>('standard');
  const [currentRegion, setCurrentRegion] = useState({
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421
  });
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const [contactEvents, setContactEvents] = useState<ContactEvent[]>([]);
  const [loading, setLoading] = useState(false);
  const [lightboxVisible, setLightboxVisible] = useState(false);
  const [currentImages, setCurrentImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const mapRef = useRef<MapView>(null);
  const markerRef = useRef<MapMarker>(null);

  useEffect(() => {
    if (report && report.sessionId && visible) {
      fetchRouteData();
    }
  }, [report, visible]);

  const fetchRouteData = async () => {
    if (!report || !report.sessionId) return;
    
    setLoading(true);
    try {
      const sessionsQuery = query(
        collection(db, 'monitoringSessions'),
        where('sessionId', '==', report.sessionId)
      );
      const sessionsSnapshot = await getDocs(sessionsQuery);
      
      let fetchedSessionData: SessionData | null = null;
      sessionsSnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.sessionId === report.sessionId) {
          fetchedSessionData = data as SessionData;
        }
      });
      
      const contactsQuery = query(
        collection(db, 'contactEvents'),
        where('sessionId', '==', report.sessionId),
        orderBy('timestamp', 'asc')
      );
      const contactsSnapshot = await getDocs(contactsQuery);
      
      const fetchedContacts: ContactEvent[] = [];
      contactsSnapshot.forEach((doc) => {
        fetchedContacts.push(doc.data() as ContactEvent);
      });
      
      setSessionData(fetchedSessionData);
      setContactEvents(fetchedContacts);
    } catch (error) {
      console.error('Error fetching route data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (mapReady && report && mapRef.current) {
      if (sessionData?.pathCoordinates && sessionData.pathCoordinates.length > 0) {
        const coordinates = [...sessionData.pathCoordinates];
        
        contactEvents.forEach(contact => {
          if (contact.observerLocation) {
            coordinates.push(contact.observerLocation);
          }
          if (contact.contactLocation) {
            coordinates.push(contact.contactLocation);
          }
        });
        
        if (coordinates.length > 1) {
          mapRef.current.fitToCoordinates(coordinates, {
            edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
            animated: true,
          });
        } else {
          mapRef.current.animateToRegion({
            latitude: coordinates[0].latitude,
            longitude: coordinates[0].longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }, 1000);
        }
      } else {
        mapRef.current.animateToRegion({
          latitude: report.location.latitude,
          longitude: report.location.longitude,
          latitudeDelta: currentRegion.latitudeDelta,
          longitudeDelta: currentRegion.longitudeDelta,
        }, 1000);
      }
    }
  }, [report, mapReady, sessionData, contactEvents]);

  const getProtocolDisplayName = (protocol?: string): string => {
    return pt[protocol as keyof typeof pt] || protocol || 'Protocolo desconhecido';
  };

  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    } else {
      return `${(meters / 1000).toFixed(2)}km`;
    }
  };

  const formatBearing = (bearing?: number): string => {
    if (bearing === undefined || bearing === null) return 'N/A';
    return `${Math.round(bearing)}°`;
  };

  const getCircumstanceText = (circumstances: any): string => {
    if (!circumstances) return 'N/A';
    
    const circumstanceTranslations: { [key: string]: string } = {
      'rolaAdultaCantando': 'Rola adulta a cantar',
      'rolaEmVoo': 'Rola em voo',
      'adultoPousado': 'Adulto pousado',
      'adultoEmDisplay': 'Adulto em display',
      'adultoAIncubar': 'Adulto a incubar',
      'juvenile': 'Juvenil',
      'crias': 'Crias',
      'ovos': 'Ovos',
      'nichoOcupado': 'Nicho ocupado',
      'ninhoVazio': 'Ninho vazio',
      'outraQual': 'Outra'
    };

    // Find which circumstance is true
    for (const [key, value] of Object.entries(circumstances)) {
      if (value === true && circumstanceTranslations[key]) {
        return circumstanceTranslations[key];
      }
    }
    
    // Check for outraQualText if outraQual is true
    if (circumstances.outraQual && circumstances.outraQualText) {
      return circumstances.outraQualText;
    }
    
    return 'N/A';
  };

  const getLocationDetails = (locationDetails: any): string => {
    if (!locationDetails) return 'N/A';
    
    const locationTranslations: { [key: string]: string } = {
      'arvore': 'Árvore',
      'arbusto': 'Arbusto',
      'clareira': 'Clareira',
      'parcelaAgricola': 'Parcela agrícola',
      'pontoDeAgua': 'Ponto de água',
      'outraQual': 'Outra'
    };

    // Find which location type is true
    for (const [key, value] of Object.entries(locationDetails)) {
      if (value === true && locationTranslations[key]) {
        return locationTranslations[key];
      }
    }
    
    // Check for outraQualText if outraQual is true
    if (locationDetails.outraQual && locationDetails.outraQualText) {
      return locationDetails.outraQualText;
    }
    
    return 'N/A';
  };

  const formatTimestamp = (timestamp: any): string => {
    try {
      let date: Date;
      
      if (timestamp && typeof timestamp.toDate === 'function') {
        // Firestore Timestamp
        date = timestamp.toDate();
      } else if (timestamp instanceof Date) {
        // Already a Date object
        date = timestamp;
      } else if (typeof timestamp === 'number') {
        // Unix timestamp
        date = new Date(timestamp);
      } else if (typeof timestamp === 'string') {
        // ISO string
        date = new Date(timestamp);
      } else {
        return 'N/A';
      }
      
      return date.toLocaleTimeString('pt-PT', { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      });
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'N/A';
    }
  };

  const handleClose = () => {
    setMapReady(false);
    onClose();
  };

  const openLightbox = (images: string[], startIndex: number = 0) => {
    setCurrentImages(images);
    setCurrentImageIndex(startIndex);
    setLightboxVisible(true);
  };

  const closeLightbox = () => {
    setLightboxVisible(false);
    setCurrentImages([]);
    setCurrentImageIndex(0);
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % currentImages.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + currentImages.length) % currentImages.length);
  };

  if (Platform.OS === 'web') {
    return (
      <Modal visible={visible} animationType="slide" transparent={false}>
        <View style={styles.container}>
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>
              O mapa não está disponível na versão web.
            </Text>
            <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
              <Text style={styles.closeButtonText}>{pt.close}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  }

  return (
    <>
      <Modal
        animationType="slide"
        transparent={false}
        visible={visible}
        onRequestClose={handleClose}
      >
      <SafeAreaView style={styles.container} edges={['top']}>
        {report && (
          <>
            <MapView
              ref={mapRef}
              style={styles.map}
              initialRegion={{
                latitude: report.location.latitude,
                longitude: report.location.longitude,
                latitudeDelta: 0.5,
                longitudeDelta: 0.5,
              }}
              onMapReady={() => setMapReady(true)}
              onRegionChangeComplete={(region) => {
                setCurrentRegion({
                  latitudeDelta: region.latitudeDelta,
                  longitudeDelta: region.longitudeDelta
                });
              }}
              mapType={mapType}
              provider={PROVIDER_GOOGLE}
            >
              {mapReady && (
                <>
                  {sessionData?.pathCoordinates && sessionData.pathCoordinates.length > 1 && (
                    <Polyline
                      coordinates={sessionData.pathCoordinates}
                      strokeColor="#0ea5e9"
                      strokeWidth={4}
                    />
                  )}

                  {/* Start marker - use monitoring start location if available, fallback to first GPS point */}
                  {sessionData?.pathCoordinates && sessionData.pathCoordinates.length > 0 && (
                    <Marker
                      coordinate={sessionData.monitoringStartLocation || sessionData.pathCoordinates[0]}
                      title="Início do trajeto"
                      description="Ponto de partida"
                      tracksViewChanges={false}
                    >
                      <View style={styles.startMarker}>
                        <FontAwesome name="play" size={16} color="white" />
                      </View>
                    </Marker>
                  )}

                  {sessionData?.pathCoordinates && sessionData.pathCoordinates.length > 1 && (
                    <Marker
                      coordinate={sessionData.pathCoordinates[sessionData.pathCoordinates.length - 1]}
                      title="Fim do trajeto"
                      description="Ponto de chegada"
                      tracksViewChanges={false}
                    >
                      <View style={styles.endMarker}>
                        <FontAwesome name="stop" size={16} color="white" />
                      </View>
                    </Marker>
                  )}

                  {contactEvents.map((contact, index) => (
                    <React.Fragment key={`contact-${index}`}>
                      {contact.observerLocation && (
                        <Marker
                          coordinate={contact.observerLocation}
                          tracksViewChanges={false}
                        >
                          <View style={styles.observerMarker}>
                            <Text style={styles.observerMarkerText}>{index + 1}</Text>
                          </View>
                          <Callout style={styles.observerCallout} tooltip={false}>
                            <View style={styles.observerCalloutContainer}>
                              <View style={styles.calloutHeader}>
                                <FontAwesome name="user" size={16} color="#0ea5e9" />
                                <Text style={[styles.calloutTitle, { color: '#0ea5e9' }]}>
                                  Contacto {index + 1}
                                </Text>
                              </View>
                              
                              <View style={styles.calloutRow}>
                                <FontAwesome name="map-marker" size={12} color="#6b7280" />
                                <Text style={styles.calloutText}>
                                  Posição do observador
                                </Text>
                              </View>
                              
                              <View style={styles.calloutRow}>
                                <FontAwesome name="clock-o" size={12} color="#6b7280" />
                                <Text style={styles.calloutText}>
                                  {formatTimestamp(contact.timestamp)}
                                </Text>
                              </View>
                            </View>
                          </Callout>
                        </Marker>
                      )}

                      {contact.contactLocation && (
                        <Marker
                          coordinate={contact.contactLocation}
                          tracksViewChanges={false}
                        >
                          <View style={styles.contactMarker}>
                            <FontAwesome6 name="dove" size={16} color="white" />
                          </View>
                          <Callout 
                            style={styles.callout}
                            tooltip={false}
                            onPress={() => {
                              console.log('🎯 Callout pressed!');
                              if (contact.images && contact.images.length > 0) {
                                console.log('📸 Opening lightbox with', contact.images.length, 'images');
                                openLightbox(contact.images, 0);
                              } else {
                                console.log('❌ No images to show');
                              }
                            }}
                          >
                            <View style={styles.calloutContainer}>
                              <View style={styles.calloutHeader}>
                                <FontAwesome6 name="dove" size={16} color="#059669" />
                                <Text style={styles.calloutTitle}>Contacto {index + 1}</Text>
                              </View>
                              
                              <View style={styles.calloutRow}>
                                <FontAwesome name="location-arrow" size={12} color="#6b7280" />
                                <Text style={styles.calloutText}>
                                  Distância: {formatDistance(contact.distance)} • Direção: {formatBearing(contact.bearing)}
                                </Text>
                              </View>
                              
                              <View style={styles.calloutRow}>
                                <FontAwesome name="eye" size={12} color="#6b7280" />
                                <Text style={styles.calloutText}>
                                  Circunstância: {getCircumstanceText(contact.circumstances)}
                                </Text>
                              </View>
                              
                              <View style={styles.calloutRow}>
                                <FontAwesome name="tree" size={12} color="#6b7280" />
                                <Text style={styles.calloutText}>
                                  Local: {getLocationDetails(contact.contactLocationDetails)}
                                </Text>
                              </View>
                              
                              {contact.images && contact.images.length > 0 && (
                                <View style={styles.calloutPhotoSection}>
                                  <View style={styles.calloutRow}>
                                    <FontAwesome name="camera" size={12} color="#6b7280" />
                                    <Text style={styles.calloutText}>
                                      Fotografias ({contact.images.length})
                                    </Text>
                                  </View>
                                  <View style={{
                                    marginTop: 8,
                                    backgroundColor: '#0996a8',
                                    paddingHorizontal: 12,
                                    paddingVertical: 6,
                                    borderRadius: 6,
                                    alignItems: 'center'
                                  }}>
                                    <Text style={{
                                      color: '#ffffff',
                                      fontSize: 12,
                                      fontWeight: '600'
                                    }}>
                                      Ver fotografias →
                                    </Text>
                                  </View>
                                </View>
                              )}
                            </View>
                          </Callout>
                        </Marker>
                      )}

                      {contact.observerLocation && contact.contactLocation && (
                        <Polyline
                          coordinates={[contact.observerLocation, contact.contactLocation]}
                          strokeColor="#059669"
                          strokeWidth={2}
                        />
                      )}
                    </React.Fragment>
                  ))}

                  {!sessionData?.pathCoordinates && (
                    <Marker
                      ref={markerRef}
                      coordinate={{
                        latitude: report.location.latitude,
                        longitude: report.location.longitude,
                      }}
                      title={`${pt.technicianReport} - ${getProtocolDisplayName(report.protocol)}`}
                      description={report.comment || `Sessão: ${report.sessionId}`}
                      tracksViewChanges={false}
                    />
                  )}
                </>
              )}
            </MapView>

            <View style={styles.mapControls}>
              {loading && (
                <View style={styles.loadingIndicator}>
                  <Text style={styles.loadingText}>Carregando rota...</Text>
                </View>
              )}
              
              <TouchableOpacity
                style={styles.mapTypeButton}
                onPress={() => setMapType(mapType === 'standard' ? 'satellite' : 'standard')}
              >
                <FontAwesome 
                  name={mapType === 'standard' ? 'map' : 'globe'} 
                  size={16} 
                  color="#FFFFFF" 
                  style={styles.buttonIcon} 
                />
                <Text style={styles.mapTypeButtonText}>
                  {mapType === 'standard' ? pt.satellite : pt.standard}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.closeButton}
                onPress={handleClose}
              >
                <FontAwesome name="times" size={16} color="#FFFFFF" style={styles.buttonIcon} />
                <Text style={styles.closeButtonText}>{pt.close}</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </SafeAreaView>
    </Modal>

    {/* Lightbox Modal */}
    <Modal
      visible={lightboxVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={closeLightbox}
    >
      <View style={styles.lightboxContainer}>
        <TouchableOpacity 
          style={styles.lightboxCloseButton}
          onPress={closeLightbox}
        >
          <FontAwesome name="times" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        
        {currentImages.length > 1 && (
          <>
            <TouchableOpacity 
              style={styles.lightboxNavLeft}
              onPress={prevImage}
            >
              <FontAwesome name="chevron-left" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.lightboxNavRight}
              onPress={nextImage}
            >
              <FontAwesome name="chevron-right" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </>
        )}
        
        <View style={styles.lightboxImageContainer}>
          {currentImages.length > 0 && (
            <LoadingImage
              source={{ uri: currentImages[currentImageIndex] }}
              style={styles.lightboxImage}
              placeholderIcon="image"
              placeholderIconSize={48}
              placeholderIconColor="#ffffff"
              containerStyle={{ backgroundColor: 'transparent' }}
            />
          )}
        </View>
        
        {currentImages.length > 1 && (
          <View style={styles.lightboxCounter}>
            <Text style={styles.lightboxCounterText}>
              {currentImageIndex + 1} de {currentImages.length}
            </Text>
          </View>
        )}
      </View>
    </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: 'absolute',
    top: 25,
    right: 15,
    gap: 10,
  },
  loadingIndicator: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  mapTypeButton: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  mapTypeButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  closeButton: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buttonIcon: {
    marginRight: 6,
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  observerMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#0ea5e9',
    borderWidth: 3,
    borderColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  observerMarkerText: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  contactMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#059669',
    borderWidth: 3,
    borderColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  startMarker: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#10b981',
    borderWidth: 3,
    borderColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  endMarker: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#ef4444',
    borderWidth: 3,
    borderColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  callout: {
    width: 280,
    minHeight: 140,
    borderRadius: 25,
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 8,
  },
  observerCallout: {
    width: 280,
    minHeight: 100,
    borderRadius: 25,
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 8,
  },
  calloutContainer: {
    padding: 12,
    backgroundColor: 'transparent',
    alignItems: 'center',
  },
  observerCalloutContainer: {
    padding: 12,
    backgroundColor: 'transparent',
    alignItems: 'center',
  },
  calloutHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    paddingBottom: 6,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  calloutTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#059669',
    marginLeft: 8,
  },
  calloutRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  calloutText: {
    fontSize: 13,
    color: '#374151',
    marginLeft: 8,
    lineHeight: 18,
    textAlign: 'center',
  },
  calloutPhotoSection: {
    marginTop: 4,
  },
  thumbnailContainer: {
    marginTop: 8,
    marginLeft: 20,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  thumbnailContent: {
    paddingRight: 20,
  },
  thumbnailButton: {
    marginRight: 8,
    position: 'relative',
  },
  thumbnail: {
    width: 50,
    height: 50,
    borderRadius: 6,
    backgroundColor: '#f9f9f9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  thumbnailPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    borderStyle: 'dashed',
  },
  lightboxContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  lightboxCloseButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 10,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  lightboxNavLeft: {
    position: 'absolute',
    left: 20,
    top: '50%',
    marginTop: -22,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  lightboxNavRight: {
    position: 'absolute',
    right: 20,
    top: '50%',
    marginTop: -22,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  lightboxImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  lightboxImage: {
    width: Dimensions.get('window').width - 40,
    height: Dimensions.get('window').height - 100,
    resizeMode: 'contain',
  },
  lightboxCounter: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  lightboxCounterText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
});