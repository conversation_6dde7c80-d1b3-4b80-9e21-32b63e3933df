import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  Modal,
  Platform,
  Image,
  Dimensions,
  ScrollView,
  Pressable,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useNetwork } from '@/contexts/NetworkContext';
import app, { db } from '@/config/firebase';
import {
  collection,
  query,
  where,
  orderBy,
  getDocs,
  Timestamp,
  deleteDoc,
  doc,
  onSnapshot,
  getFirestore,
  DocumentData,
  updateDoc,
} from 'firebase/firestore';
import { ref as storageRef, deleteObject } from 'firebase/storage';
import { storage } from '@/config/firebase';
import MapView, { Marker, MapMarker, PROVIDER_GOOGLE, MapType, Polyline } from 'react-native-maps';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import LoadingImage from '@/components/LoadingImage';
import { GestorReport } from '@/types/reports';
import GestorReportCreation from './GestorReportCreation';

/**
 * GESTOR REPORTS - DEVELOPMENT MODE
 * 
 * To enable the full functionality for testing/development:
 * 1. Change `showComingSoon` state from `true` to `false` in the component
 * 2. All the report creation, viewing, and management features will be available
 * 
 * To disable for production (show "coming soon"):
 * 1. Keep `showComingSoon` state as `true`
 * 2. Users will see the "Em Breve" message instead of the actual functionality
 */

// Portuguese translations for gestor reports
const pt = {
  noReports: 'Não existem relatórios de gestão',
  noInternetReports: 'Não é possível carregar relatórios sem ligação à Internet',
  loading: 'A carregar relatórios de gestão...',
  date: 'Data',
  location: 'Localização',
  comment: 'Comentário',
  zone: 'Zona',
  protocol: 'Protocolo',
  error: 'Erro ao carregar relatórios',
  retry: 'Tentar novamente',
  view: 'Ver',
  delete: 'Eliminar',
  cancel: 'Cancelar',
  confirmDelete: 'Tem certeza que deseja eliminar este relatório?',
  yes: 'Sim',
  no: 'Não',
  reportOptions: 'Opções do Relatório',
  deleteSuccess: 'Relatório eliminado com sucesso',
  deleteError: 'Erro ao eliminar relatório',
  success: 'Sucesso',
  viewImages: 'Ver Imagens',
  closeImages: 'Fechar',
  deleteImage: 'Eliminar imagem',
  confirmDeleteImage: 'Tem certeza que deseja eliminar esta imagem?',
  imageDeleteSuccess: 'Imagem eliminada com sucesso',
  imageDeleteError: 'Erro ao eliminar imagem',
  deleteSelected: 'Eliminar Selecionadas',
  cancelSelection: 'Cancelar',
  noImagesSelected: 'Selecione as imagens para eliminar',
  selectedCount: 'Selecionadas: ',
  // Gestor-specific translations
  managementActivity: 'Atividade de Gestão',
  trajectory: 'Trajeto',
  contacts: 'Contactos',
  distance: 'Distância',
  duration: 'Duração',
  observers: 'Observadores',
  sessionReport: 'Relatório de Sessão',
  viewOnMap: 'Ver no mapa',
  weatherConditions: 'Condições Meteorológicas',
  temperature: 'Temperatura',
  humidity: 'Humidade',
  pressure: 'Pressão',
  windSpeed: 'Vento',
  visibility: 'Visibilidade',
  cloudiness: 'Nebulosidade',
  protocolOptions: {
    trajeto: 'Trajeto',
    estacoes_escuta: 'Estações de escuta',
    metodo_mapas: 'Método dos mapas',
    contagens_pontos: 'Contagens em pontos específicos',
    captura_marcacao: 'Captura e marcação',
    acompanhamento_cacadas: 'Acompanhamento das caçadas',
    registos_ocasionais: 'Registos ocasionais',
  },
  activityTypes: {
    patrulha: 'Patrulha',
    manutencao: 'Manutenção',
    observacao: 'Observação',
    gestao_habitat: 'Gestão de Habitat',
    outro: 'Outro',
  },
};

export default function GestorReportsScreen() {
  const { user } = useAuth();
  const { hasInternetConnection } = useNetwork();
  
  // TEMPORARY: Set to true to show "coming soon", false to show actual functionality
  const [showComingSoon] = useState(true);
  
  const [reports, setReports] = useState<GestorReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedReport, setSelectedReport] = useState<GestorReport | null>(null);
  const [isMapModalVisible, setMapModalVisible] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [isImageViewerVisible, setImageViewerVisible] = useState(false);
  const screenWidth = Dimensions.get('window').width;
  const [selectionMode, setSelectionMode] = useState(false);
  const [selectedImageIndexes, setSelectedImageIndexes] = useState<number[]>([]);
  const [activeReport, setActiveReport] = useState<GestorReport | null>(null);
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const [mapType, setMapType] = useState<MapType>('standard');
  const mapRef = useRef<MapView>(null);
  const markerRef = useRef<MapMarker>(null);
  const [showCreateReportModal, setShowCreateReportModal] = useState(false);
  const [contactMarkers, setContactMarkers] = useState<Array<{
    id: string;
    coordinate: { latitude: number; longitude: number };
    observerLocation: { latitude: number; longitude: number };
    contactNumber: number;
    timestamp: string;
  }>>([]);

  // Add timeout for refresh state
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Show coming soon overlay if enabled
  if (showComingSoon) {
    return (
      <View style={styles.comingSoonContainer}>
        <View style={styles.comingSoonCard}>
          <View style={styles.comingSoonIconContainer}>
            <FontAwesome name="file-text-o" size={64} color="#0996a8" />
          </View>
          
          <Text style={styles.comingSoonTitle}>Relatórios de Gestores</Text>
          
          <Text style={styles.comingSoonDescription}>
            O sistema de relatórios para gestores de zonas de caça está em desenvolvimento.
          </Text>
          
          <Text style={styles.comingSoonSubtext}>
            Em breve poderá criar e gerir relatórios específicos para as suas zonas de caça.
          </Text>
          
          <View style={styles.comingSoonBadge}>
            <Text style={styles.comingSoonBadgeText}>Em Breve</Text>
          </View>
        </View>
      </View>
    );
  }

  // Auto-fit map when contact markers change
  useEffect(() => {
    if (contactMarkers.length > 0 && isMapModalVisible) {
      setTimeout(() => {
        console.log('🔄 REPORTS: Auto-fitting map due to contact markers change');
        fitMapToTrajectory();
      }, 500);
    }
  }, [contactMarkers, isMapModalVisible]);

  useEffect(() => {
    if (!user) return;

    const db = getFirestore(app);
    const reportsRef = collection(db, 'gestoresReports');
    
    // Query for gestor reports only
    const q = query(
      reportsRef,
      where('userId', '==', user.uid)
    );

    const unsubscribe = onSnapshot(q, async (snapshot) => {
      try {
        console.log('🔄 REPORTS: onSnapshot triggered, processing reports...');
        const allReports: any[] = [];
        snapshot.forEach((doc) => {
          try {
            const data = doc.data();
            // Get all gestor-related reports and ensure data integrity
            if (data && data.userRole === 'gestor_caca' && doc.id) {
              allReports.push({ 
                id: doc.id, 
                ...data,
                // Ensure required fields are safe
                zoneName: data.zoneName || 'Zona N/A',
                protocol: data.protocol || 'trajeto',
                createdAt: data.createdAt || new Date(),
                observersCount: typeof data.observersCount === 'number' ? data.observersCount : 1,
                contactEventsCount: typeof data.contactEventsCount === 'number' ? data.contactEventsCount : 0,
                images: Array.isArray(data.images) ? data.images : [],
                comment: typeof data.comment === 'string' ? data.comment : '',
              });
            }
          } catch (docError) {
            console.warn('Error processing document:', doc.id, docError);
          }
        });
      
        console.log(`🔄 REPORTS: Found ${allReports.length} reports`);
      
        // Group reports by sessionId like technician reports do
        const reportsBySession: { [key: string]: any[] } = {};
        const reportsWithoutSession: any[] = [];
        
        allReports.forEach((report) => {
          const sessionId = report.sessionId;
          if (sessionId) {
            if (!reportsBySession[sessionId]) {
              reportsBySession[sessionId] = [];
            }
            reportsBySession[sessionId].push(report);
          } else {
            reportsWithoutSession.push(report);
          }
        });
        
        // Process grouped reports and merge images
        const finalReports: GestorReport[] = [];
        
        // Get contact counts for sessions (from gestoresReports_contacts collection)
        const contactCountMap: { [sessionId: string]: number } = {};
        try {
          const sessionIds = Object.keys(reportsBySession);
          console.log(`🔄 REPORTS: Fetching contact counts for ${sessionIds.length} sessions...`);
          
          if (sessionIds.length > 0) {
            const contactEventsRef = collection(db, 'gestoresReports_contacts');
            
            // Use Promise.all to fetch all contact counts in parallel
            const contactPromises = sessionIds.map(async (sessionId) => {
              const contactQuery = query(
                contactEventsRef,
                where('sessionId', '==', sessionId)
              );
              const contactSnapshot = await getDocs(contactQuery);
              return { sessionId, count: contactSnapshot.size };
            });
            
            const contactResults = await Promise.all(contactPromises);
            contactResults.forEach(({ sessionId, count }) => {
              contactCountMap[sessionId] = count;
            });
          }
          
          console.log(`🔄 REPORTS: Contact counts fetched:`, contactCountMap);
        } catch (error) {
          console.error('Error fetching contact counts from gestoresReports_contacts:', error);
        }
        
        Object.values(reportsBySession).forEach((sessionReports) => {
          let sessionReport: DocumentData | undefined;
          let contactReport: DocumentData | undefined;
          
          // Find session report and contact report
          sessionReports.forEach((report) => {
            const reportType = report.type;
            if (reportType === 'gestor_monitoring_report' || reportType === 'gestor_report') {
              sessionReport = report;
            } else if (reportType === 'monitoring_session') {
              contactReport = report;
            }
          });
          
          // Use session report as base, but merge images from contact report
          if (sessionReport) {
            const mergedReport: GestorReport = {
              ...sessionReport,
              images: sessionReport.images || [],
              contactEventsCount: contactCountMap[sessionReport.sessionId] || 0
            } as GestorReport;
            
            // Merge images from contact report if available
            if (contactReport && Array.isArray((contactReport as any).images)) {
              mergedReport.images = (contactReport as any).images;
            }
            
            finalReports.push(mergedReport);
          } else if (contactReport) {
            // If only contact report exists, use it
            const contactOnlyReport = {
              ...contactReport,
              contactEventsCount: contactCountMap[contactReport.sessionId] || 0
            } as GestorReport;
            finalReports.push(contactOnlyReport);
          }
        });
        
        // Add reports without sessionId (regular observations)
        reportsWithoutSession.forEach((report) => {
          if (report.type === 'gestor_monitoring_report' || report.type === 'gestor_report') {
            const reportWithContactCount = {
              ...report,
              contactEventsCount: 0 // These don't have sessions, so no contact events
            } as GestorReport;
            finalReports.push(reportWithContactCount);
          }
        });
        
        // Sort by creation date (newest first)
        finalReports.sort((a, b) => {
          const aTime = a.createdAt?.toMillis ? a.createdAt.toMillis() : 0;
          const bTime = b.createdAt?.toMillis ? b.createdAt.toMillis() : 0;
          return bTime - aTime;
        });
        
        console.log(`🔄 REPORTS: Final reports count: ${finalReports.length}`);
        setReports(finalReports);
        setLoading(false);
        setError(null);
        
        // Clear refresh state and timeout
        if (refreshTimeoutRef.current) {
          clearTimeout(refreshTimeoutRef.current);
          refreshTimeoutRef.current = null;
        }
        setRefreshing(false);
        
      } catch (processError) {
        console.error('Error processing reports data:', processError);
        setReports([]);
        setError('Erro ao processar relatórios');
        setLoading(false);
        
        // Clear timeout on error
        if (refreshTimeoutRef.current) {
          clearTimeout(refreshTimeoutRef.current);
          refreshTimeoutRef.current = null;
        }
        setRefreshing(false);
      }
    }, (error: any) => {
      console.error('Error fetching gestor reports:', error);
      setError(pt.error);
      setLoading(false);
      
      // Clear timeout on error
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
      setRefreshing(false);
    });

    return () => {
      unsubscribe();
      // Clear timeout on cleanup
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
    };
  }, [user]);

  const onRefresh = () => {
    console.log('🔄 REPORTS: Manual refresh triggered');
    setRefreshing(true);
    setError(null);
    
    // Clear any existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }
    
    // Set a timeout to prevent infinite loading
    refreshTimeoutRef.current = setTimeout(() => {
      console.log('⏰ REPORTS: Refresh timeout reached, stopping refresh');
      setRefreshing(false);
    }, 10000); // 10 seconds timeout
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    
    let date: Date;
    if (timestamp.toDate) {
      date = timestamp.toDate();
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'string') {
      date = new Date(timestamp);
    } else {
      return 'N/A';
    }
    
    return date.toLocaleDateString('pt-PT') + ' ' + date.toLocaleTimeString('pt-PT', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds || seconds <= 0) return 'N/A';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const formatDistance = (meters?: number): string => {
    if (!meters || meters <= 0) return 'N/A';
    
    if (meters >= 1000) {
      return `${(meters / 1000).toFixed(1)} km`;
    } else {
      return `${Math.round(meters)} m`;
    }
  };

  const formatContactCount = (count?: number): string => {
    return count ? count.toString() : '0';
  };

  const handleReportPress = (report: GestorReport) => {
    setSelectedReport(report);
    setMapModalVisible(true);
    
    // Auto-fit map after modal opens
    setTimeout(() => {
      fitMapToTrajectory();
    }, 100);
    
    if (report.sessionId) {
      loadContactMarkers(report.sessionId);
    }
  };

  // Load contact markers for the selected report
  const loadContactMarkers = async (sessionId: string) => {
    console.log('🔄 REPORTS: Loading contact markers for sessionId:', sessionId);
    
    if (!sessionId) {
      console.log('❌ REPORTS: No sessionId provided');
      setContactMarkers([]);
      return;
    }

    try {
      const db = getFirestore(app);
      const contactEventsRef = collection(db, 'gestoresReports_contacts');
      
      console.log('🔄 REPORTS: Checking all documents in gestoresReports_contacts...');
      const allDocsSnapshot = await getDocs(contactEventsRef);
      
      console.log('🔄 REPORTS: Total documents in gestoresReports_contacts:', allDocsSnapshot.size);
      
      // Log all documents for debugging
      allDocsSnapshot.forEach((doc) => {
        const data = doc.data();
        console.log('🔄 REPORTS: Document', doc.id, '- sessionId:', data.sessionId, '- contactNumber:', data.contactNumber);
      });
      
      // Try to query by sessionId with fallback
      let contactsSnapshot;
      try {
        const contactsQuery = query(
          contactEventsRef,
          where('sessionId', '==', sessionId),
          orderBy('timestamp', 'asc')
        );
        contactsSnapshot = await getDocs(contactsQuery);
        console.log('🔄 REPORTS: Found', contactsSnapshot.size, 'contacts with sessionId:', sessionId);
      } catch (indexError) {
        console.warn('🔄 REPORTS: Index error, trying without orderBy:', indexError);
        // Fallback without orderBy if index doesn't exist
        const contactsQuery = query(
          contactEventsRef,
          where('sessionId', '==', sessionId)
        );
        contactsSnapshot = await getDocs(contactsQuery);
        console.log('🔄 REPORTS: Found', contactsSnapshot.size, 'contacts with sessionId (no orderBy):', sessionId);
      }

      const markers: Array<{
        id: string;
        coordinate: { latitude: number; longitude: number };
        observerLocation: { latitude: number; longitude: number };
        contactNumber: number;
        timestamp: string;
      }> = [];

      contactsSnapshot.forEach((doc) => {
        const data = doc.data();
        console.log('🔄 REPORTS: Processing contact document:', doc.id, data);
        
        if (data.contactLocation && data.observerLocation) {
          markers.push({
            id: doc.id,
            coordinate: {
              latitude: data.contactLocation.latitude,
              longitude: data.contactLocation.longitude,
            },
            observerLocation: {
              latitude: data.observerLocation.latitude,
              longitude: data.observerLocation.longitude,
            },
            contactNumber: data.contactNumber || 1,
            timestamp: data.timestamp?.toDate?.()?.toISOString() || data.timestamp,
          });
          console.log('🔄 REPORTS: Added marker for contact', data.contactNumber);
        } else {
          console.warn('🔄 REPORTS: Contact missing location data:', doc.id);
        }
      });

      console.log('🔄 REPORTS: Total markers created:', markers.length);
      setContactMarkers(markers);
      
    } catch (error) {
      console.error('❌ REPORTS: Error loading contact markers:', error);
      setContactMarkers([]);
    }
  };

  // Function to fit map to trajectory bounds
  const fitMapToTrajectory = () => {
    if (!mapRef.current || !selectedReport) return;

    const allCoordinates = [];
    
    // Add report location
    allCoordinates.push({
      latitude: selectedReport.location.latitude,
      longitude: selectedReport.location.longitude,
    });
    
    // Add trajectory path coordinates
    if (selectedReport.pathCoordinates && selectedReport.pathCoordinates.length > 0) {
      allCoordinates.push(...selectedReport.pathCoordinates);
    }
    
    // Add contact markers
    contactMarkers.forEach(marker => {
      allCoordinates.push(marker.coordinate);
      allCoordinates.push(marker.observerLocation);
    });

    if (allCoordinates.length > 0) {
      try {
        mapRef.current.fitToCoordinates(allCoordinates, {
          edgePadding: { 
            top: 100, 
            right: 100, 
            bottom: 100, 
            left: 100 
          },
          animated: true,
        });
      } catch (error) {
        console.error('Error fitting map to coordinates:', error);
      }
    }
  };

  const handleDelete = async (report: GestorReport) => {
    showAlert({
      type: 'warning',
      message: pt.confirmDelete,
      onConfirm: async () => {
        try {
          // Delete images from storage first (only Firebase Storage URLs)
          if (report.images && report.images.length > 0) {
            for (const imageUrl of report.images) {
              try {
                // Only try to delete if it's a Firebase Storage URL
                if (imageUrl && typeof imageUrl === 'string' && 
                    (imageUrl.startsWith('https://firebasestorage.googleapis.com') || 
                     imageUrl.startsWith('gs://'))) {
                  const imageRef = storageRef(storage, imageUrl);
                  await deleteObject(imageRef);
                  console.log('✅ Deleted Firebase Storage image:', imageUrl);
                } else {
                  console.log('⏭️ Skipping local image (not in Firebase Storage):', imageUrl);
                }
              } catch (imageError) {
                console.error('Error deleting image:', imageUrl, imageError);
                // Continue with report deletion even if image deletion fails
              }
            }
          }
          
          // Delete the report document
          await deleteDoc(doc(db, 'gestoresReports', report.id));
          
          // Immediately update local state to remove the report from the list
          // This provides instant feedback while Firestore listener updates
          setReports(prevReports => prevReports.filter(r => r.id !== report.id));
          
          showAlert({
            type: 'success',
            message: pt.deleteSuccess,
          });
        } catch (error) {
          console.error('Error deleting report:', error);
          showAlert({
            type: 'error',
            message: pt.deleteError,
          });
        }
      },
    });
  };

  const handleViewImages = (images: string[]) => {
    setSelectedImages(images);
    setImageViewerVisible(true);
  };

  const handleImageLongPress = (report: GestorReport, index: number) => {
    setActiveReport(report);
    setSelectionMode(true);
    setSelectedImageIndexes([index]);
  };

  const handleImageSelect = (index: number) => {
    if (selectedImageIndexes.includes(index)) {
      setSelectedImageIndexes(selectedImageIndexes.filter(i => i !== index));
    } else {
      setSelectedImageIndexes([...selectedImageIndexes, index]);
    }
  };

  const handleDeleteSelectedImages = async () => {
    if (!activeReport || selectedImageIndexes.length === 0) {
      showAlert({
        type: 'error',
        message: pt.noImagesSelected,
      });
      return;
    }

    showAlert({
      type: 'warning',
      message: `${pt.confirmDeleteImage} (${selectedImageIndexes.length} ${selectedImageIndexes.length === 1 ? 'imagem' : 'imagens'})`,
      onConfirm: async () => {
        try {
          const updatedImages = [...(activeReport.images || [])];
          const imagesToDelete = selectedImageIndexes
            .sort((a, b) => b - a) // Sort in descending order to avoid index issues
            .map(index => updatedImages[index]);

          // Remove images from array (in reverse order to maintain indexes)
          selectedImageIndexes.sort((a, b) => b - a).forEach(index => {
            updatedImages.splice(index, 1);
          });

          // Update Firestore document
          await updateDoc(doc(db, 'gestoresReports', activeReport.id), {
            images: updatedImages
          });

          // Delete images from storage (only Firebase Storage URLs)
          for (const imageUrl of imagesToDelete) {
            try {
              // Only try to delete if it's a Firebase Storage URL
              if (imageUrl && typeof imageUrl === 'string' && 
                  (imageUrl.startsWith('https://firebasestorage.googleapis.com') || 
                   imageUrl.startsWith('gs://'))) {
                const imageRef = storageRef(storage, imageUrl);
                await deleteObject(imageRef);
                console.log('✅ Deleted Firebase Storage image:', imageUrl);
              } else {
                console.log('⏭️ Skipping local image (not in Firebase Storage):', imageUrl);
              }
            } catch (imageError) {
              console.error('Error deleting image from storage:', imageUrl, imageError);
            }
          }

          showAlert({
            type: 'success',
            message: pt.imageDeleteSuccess,
          });

          setSelectionMode(false);
          setSelectedImageIndexes([]);
          setActiveReport(null);
        } catch (error) {
          console.error('Error deleting images:', error);
          showAlert({
            type: 'error',
            message: pt.imageDeleteError,
          });
        }
      },
    });
  };

  const renderItem = ({ item }: { item: GestorReport }) => {
    try {
      if (!item || typeof item !== 'object' || !item.id) {
        console.warn('Invalid report item passed to renderItem. Skipping.', { item });
        return null;
      }

      // --- Data Sanitization ---
      const zoneName = String(item.zoneName || 'Zona N/A');
      
      let displayDate = 'Data inválida';
      try {
        if (item.createdAt) displayDate = formatDate(item.createdAt);
      } catch (e) {
        console.warn(`Error formatting date for report ${item.id}`, e);
      }

      const protocolKey = item.protocol as keyof typeof pt.protocolOptions;
      const protocol = String((pt.protocolOptions && pt.protocolOptions[protocolKey]) ? pt.protocolOptions[protocolKey] : (item.protocol || 'N/A'));

      const comment = String(item.comment || '').trim();
      const observersCount = String(item.observersCount || 1);
      const contactCount = String(item.contactEventsCount || 0);
      const images = Array.isArray(item.images) ? item.images : [];
      const imagesCount = String(images.length);

      const duration = item.sessionDuration ? formatDuration(item.sessionDuration) : '';
      const distance = item.totalDistance ? formatDistance(item.totalDistance) : '';

      let temperature = '';
      if (item.weather && typeof item.weather === 'object' && item.weather.temperature) {
        temperature = `${item.weather.temperature}°`;
      }

      return (
        <View style={styles.reportCard}>
          {/* Header row with protocol and delete button */}
          <View style={styles.headerRow}>
            <View style={styles.protocolBadge}>
              <FontAwesome name="bars" size={12} color="#6b7280" />
              <Text style={styles.protocolBadgeText}>{protocol}</Text>
            </View>
            <TouchableOpacity
              style={styles.deleteReportButton}
              onPress={() => handleDelete(item)}>
              <FontAwesome name="trash" size={16} color="#ff3b30" />
            </TouchableOpacity>
          </View>

          {/* Date and Zone on same line - clean and simple */}
          <View style={styles.dateLocationRow}>
            <Text style={styles.reportDate}>{displayDate}</Text>
            <Text style={styles.locationText}>{zoneName}</Text>
          </View>

          {/* Compact stats row */}
          <View style={styles.compactStatsRow}>
            <View style={styles.compactStatItem}>
              <FontAwesome name="users" size={10} color="#6b7280" />
              <Text style={styles.compactStatValue}>{observersCount}</Text>
            </View>
            
            <View style={styles.compactStatItem}>
              <FontAwesome6 name="dove" size={10} color="#6b7280" />
              <Text style={styles.compactStatValue}>{contactCount}</Text>
            </View>
            
            <View style={styles.compactStatItem}>
              <FontAwesome name="camera" size={10} color="#6b7280" />
              <Text style={styles.compactStatValue}>{imagesCount}</Text>
            </View>

            {/* Weather as fourth stat item */}
            {!!temperature && (
              <View style={styles.compactStatItem}>
                <FontAwesome name="thermometer-half" size={10} color="#6b7280" />
                <Text style={styles.compactStatValue}>{temperature}</Text>
              </View>
            )}
          </View>
          
          {/* Comments - only if present */}
          {!!comment && (
            <Text style={styles.compactComment} numberOfLines={2}>
              {comment}
            </Text>
          )}

          {/* Compact map button */}
          <TouchableOpacity
            style={styles.compactMapButton}
            onPress={() => {
              setSelectedReport(item);
              setMapModalVisible(true);
            }}>
            <FontAwesome name="map" size={12} color="#ffffff" />
            <Text style={styles.compactMapButtonText}>Ver no mapa</Text>
          </TouchableOpacity>
        </View>
      );
    } catch (error) {
      console.error(`Fatal error rendering report item with id: ${item?.id}.`, error, item);
      return (
        <View style={styles.errorCard}>
          <Text style={styles.errorCardText}>Erro ao carregar este relatório.</Text>
        </View>
      );
    }
  };

  const renderEmptyComponent = () => {
    if (loading) return null;
    
    if (!hasInternetConnection()) {
      return (
        <View style={styles.emptyContainer}>
          <View style={styles.noInternetCard}>
            <FontAwesome6 
              name="wifi" 
              size={48} 
              color="#ff9500" 
              style={{ opacity: 0.6 }}
            />
            <Text style={styles.noInternetText}>
              Não é possível carregar relatórios sem ligação à Internet
            </Text>
          </View>
        </View>
      );
    }

          return (
        <View style={styles.emptyContainer}>
          <View style={styles.modernEmptyCard}>
            <View style={styles.iconContainer}>
              <FontAwesome 
                name="list" 
                size={32} 
                color="#0996a8" 
              />
            </View>
            <View style={styles.titleContainer}>
              <FontAwesome6 
                name="circle-info" 
                size={18} 
                color="#7f8c8d" 
                style={{ marginRight: 8, marginTop: 0 }}
              />
              <Text style={styles.modernTitle}>Relatórios</Text>
            </View>
            <Text style={styles.modernSubtitle}>
              Os relatórios criam trajetos adicionais para contribuição das ZC para estudos complementares
            </Text>
            
            <View style={styles.infoSection}>
              <Text style={styles.infoTitle}>Como criar um relatório:</Text>
              <View style={styles.infoItem}>
                <FontAwesome6 name="arrow-right" size={14} color="#0996a8" />
                <Text style={styles.infoText}>Clique em "Criar Relatório"</Text>
              </View>
              <View style={styles.infoItem}>
                <FontAwesome6 name="route" size={14} color="#0996a8" />
                <Text style={styles.infoText}>Crie um trajeto de monitorização</Text>
              </View>
              <View style={styles.infoItem}>
                <FontAwesome6 name="clipboard-list" size={14} color="#0996a8" />
                <Text style={styles.infoText}>Contribua para estudos complementares</Text>
              </View>
              <View style={styles.infoItem}>
                <FontAwesome6 name="camera" size={14} color="#0996a8" />
                <Text style={styles.infoText}>Adicione fotos e observações</Text>
              </View>
            </View>
          </View>
        </View>
      );
  };

  if (Platform.OS === 'web') {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>
          O mapa não está disponível na versão web.
        </Text>
      </View>
    );
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#0996a8" />
          <Text style={styles.loadingText}>
            A carregar relatórios de gestão...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>
            {error}
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              setLoading(true);
              setError(null);
            }}>
            <Text style={styles.retryButtonText}>{pt.retry || 'Tentar Novamente'}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      {selectionMode && (
        <View style={styles.selectionHeader}>
          <Text style={styles.selectionText}>
            {String(pt.selectedCount || 'Selecionadas: ')}{String(selectedImageIndexes.length)}
          </Text>
          <View style={styles.selectionButtons}>
            <TouchableOpacity
              style={styles.cancelSelectionButton}
              onPress={() => {
                setSelectionMode(false);
                setSelectedImageIndexes([]);
                setActiveReport(null);
              }}
            >
              <Text style={styles.cancelSelectionText}>{String(pt.cancelSelection || 'Cancelar')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.deleteSelectedButton}
              onPress={handleDeleteSelectedImages}
            >
              <Text style={styles.deleteSelectedText}>{String(pt.deleteSelected || 'Eliminar Selecionadas')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <FlatList
        data={reports || []}
        renderItem={renderItem}
        keyExtractor={(item, index) => {
          try {
            return String(item?.id || `report-${index}-${Date.now()}`);
          } catch (e) {
            return `fallback-${index}-${Date.now()}`;
          }
        }}
        contentContainerStyle={styles.listContainer}
        style={styles.list}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#0996a8']}
            tintColor="#0996a8"
          />
        }
        ListEmptyComponent={renderEmptyComponent}
        removeClippedSubviews={false}
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        windowSize={10}
      />

      {/* Create Report Button - matches app theme */}
      <View style={styles.createReportContainer}>
        <TouchableOpacity
          style={styles.createReportButton}
          onPress={() => setShowCreateReportModal(true)}
          activeOpacity={0.7}
        >
          <FontAwesome name="plus" size={16} color="#ffffff" style={styles.createReportIcon} />
          <Text style={styles.createReportText}>Criar Relatório</Text>
        </TouchableOpacity>
      </View>

      {/* Map Modal */}
      <Modal
        animationType="slide"
        transparent={false}
        visible={isMapModalVisible}
        onRequestClose={() => setMapModalVisible(false)}>
        <View style={styles.container}>
          {selectedReport && (
            <>
              <MapView
                ref={mapRef}
                style={styles.map}
                initialRegion={{
                  latitude: selectedReport.location.latitude,
                  longitude: selectedReport.location.longitude,
                  latitudeDelta: 0.01,
                  longitudeDelta: 0.01,
                }}
                mapType={mapType}
                provider={PROVIDER_GOOGLE}>
                
                {/* Report location marker */}
                <Marker
                  ref={markerRef}
                  coordinate={{
                    latitude: selectedReport.location.latitude,
                    longitude: selectedReport.location.longitude,
                  }}
                  title={selectedReport.zoneName}
                  description={selectedReport.comment || pt.sessionReport}
                  tracksViewChanges={false}
                />

                {/* Contact markers with sighting lines */}
                {contactMarkers.map((contact, index) => (
                  <React.Fragment key={`contact-${contact.id}`}>
                    {/* Sighting line from observer to contact */}
                    <Polyline
                      coordinates={[contact.observerLocation, contact.coordinate]}
                      strokeColor="#ff6b6b"
                      strokeWidth={2}
                      lineDashPattern={[5, 5]}
                    />
                    
                    {/* Observer location marker */}
                    <Marker
                      coordinate={contact.observerLocation}
                      title={`Observador ${contact.contactNumber}`}
                      description="Posição do observador"
                      tracksViewChanges={false}
                    >
                      <View style={styles.observerMarker}>
                        <FontAwesome name="user" size={12} color="#ffffff" />
                      </View>
                    </Marker>
                    
                    {/* Contact location marker */}
                    <Marker
                      coordinate={contact.coordinate}
                      title={`Contacto ${contact.contactNumber}`}
                      description={`${new Date(contact.timestamp).toLocaleString('pt-PT')}`}
                      tracksViewChanges={false}
                    >
                      <View style={styles.contactMarker}>
                        <Text style={styles.contactMarkerText}>{contact.contactNumber}</Text>
                      </View>
                    </Marker>
                  </React.Fragment>
                ))}

                {/* Path coordinates for trajectory reports */}
                {selectedReport.pathCoordinates && selectedReport.pathCoordinates.length > 1 && (
                  <Polyline
                    coordinates={selectedReport.pathCoordinates}
                    strokeColor="#0996a8"
                    strokeWidth={3}
                  />
                )}
              </MapView>
              
              <View style={styles.mapControls}>
                <TouchableOpacity
                  style={styles.mapTypeButton}
                  onPress={() => setMapType(mapType === 'standard' ? 'satellite' : 'standard')}>
                  <FontAwesome 
                    name={mapType === 'standard' ? 'map' : 'globe'} 
                    size={16} 
                    color="#FFFFFF" 
                    style={styles.buttonIcon} 
                  />
                  <Text style={styles.mapTypeButtonText}>
                    {mapType === 'standard' ? 'Satélite' : 'Mapa'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setMapModalVisible(false)}>
                  <FontAwesome name="times" size={16} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.closeButtonText}>Fechar</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </Modal>

      {/* Image Viewer Modal */}
      <Modal
        visible={isImageViewerVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setImageViewerVisible(false)}
      >
        <View style={styles.imageViewerContainer}>
          <View style={styles.imageViewerHeader}>
            <TouchableOpacity
              style={styles.imageViewerCloseButton}
              onPress={() => setImageViewerVisible(false)}
            >
              <Text style={styles.closeButtonText}>{pt.closeImages}</Text>
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.imageViewerScrollView}>
            {selectedImages.map((imageUrl, index) => (
              <LoadingImage
                key={index}
                source={{ uri: imageUrl }}
                style={{
                  ...styles.fullImage,
                  width: screenWidth,
                  height: screenWidth
                }}
                placeholderIcon="image"
                placeholderIconSize={32}
                containerStyle={{ backgroundColor: '#000' }}
              />
            ))}
          </ScrollView>
        </View>
      </Modal>

      {/* Create Report Component */}
      <GestorReportCreation
        visible={showCreateReportModal}
        onClose={() => setShowCreateReportModal(false)}
        onReportCreated={() => {
          console.log('🔄 REPORTS: Report created, closing modal and refreshing...');
          setShowCreateReportModal(false);
          
          // Add a small delay to ensure modal is closed before refreshing
          setTimeout(() => {
            console.log('🔄 REPORTS: Starting refresh after report creation...');
            setRefreshing(true);
            onRefresh();
          }, 200);
        }}
      />

      <CustomAlert
        visible={isVisible}
        type={config.type}
        message={config.message}
        onClose={hideAlert}
        onConfirm={config.onConfirm}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    marginTop: -25,
  },
  list: {
    flex: 1,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingTop: 30,
    paddingBottom: 100,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyText: {
    fontSize: 18,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubText: {
    fontSize: 14,
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 20,
  },
  reportCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    elevation: 3,
    shadowColor: '#0996a8',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    borderWidth: 0,
    borderLeftWidth: 4,
    borderLeftColor: '#0996a8',
    borderRightWidth: 4,
    borderRightColor: '#0996a8',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    position: 'relative',
  },
  protocolBadge: {
    flexDirection: 'row',
    backgroundColor: '#f8fafc',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    alignItems: 'center',
    gap: 6,
  },
  protocolBadgeText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '600',
  },
  deleteReportButton: {
    backgroundColor: '#fff',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    position: 'absolute',
    right: 0,
  },
  dateLocationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  locationText: {
    fontSize: 11,
    color: '#0996a8',
    fontWeight: '600',
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  zoneName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginLeft: 8,
    flex: 1,
  },
  reportDate: {
    fontSize: 11,
    color: '#6b7280',
    fontWeight: '500',
  },
  reportContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  protocolRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  protocolText: {
    fontSize: 14,
    color: '#4b5563',
    marginLeft: 8,
    fontWeight: '500',
  },
  activityRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityText: {
    fontSize: 14,
    color: '#4b5563',
    marginLeft: 8,
    fontStyle: 'italic',
  },
  compactStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f8fffe',
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e0f2f1',
  },
  compactStatItem: {
    alignItems: 'center',
    flex: 1,
    gap: 1,
  },
  compactStatValue: {
    fontSize: 13,
    fontWeight: '700',
    color: '#0996a8',
    marginTop: 1,
  },
  compactComment: {
    fontSize: 11,
    color: '#6b7280',
    fontStyle: 'italic',
    marginBottom: 8,
    lineHeight: 15,
  },
  imagesContainer: {
    marginBottom: 12,
  },
  imagePreview: {
    marginRight: 8,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  previewImage: {
    width: 60,
    height: 60,
  },
  selectedImage: {
    borderWidth: 2,
    borderColor: '#0996a8',
  },
  imageSelectButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreImagesIndicator: {
    width: 60,
    height: 60,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreImagesText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  compactMapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0996a8',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 0,
    elevation: 2,
    shadowColor: '#0996a8',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  compactMapButtonText: {
    color: '#ffffff',
    marginLeft: 4,
    fontSize: 11,
    fontWeight: '600',
  },
  deleteButton: {
    backgroundColor: '#ef4444',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    justifyContent: 'center',
  },
  deleteButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  selectionHeader: {
    backgroundColor: '#0996a8',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectionText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  selectionButtons: {
    flexDirection: 'row',
  },
  cancelSelectionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginRight: 8,
  },
  cancelSelectionText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  deleteSelectedButton: {
    backgroundColor: '#ef4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  deleteSelectedText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: 'absolute',
    top: 50,
    right: 16,
    flexDirection: 'column',
  },
  mapTypeButton: {
    backgroundColor: '#0996a8',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 8,
  },
  mapTypeButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  closeButton: {
    backgroundColor: '#ef4444',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  closeButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  buttonIcon: {
    marginRight: 4,
  },
  imageViewerContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  imageViewerHeader: {
    position: 'absolute',
    top: 50,
    right: 16,
    zIndex: 1,
  },
  imageViewerCloseButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  imageViewerScrollView: {
    flex: 1,
  },
  fullImage: {
    marginBottom: 16,
  },
  createReportContainer: {
    position: 'absolute',
    bottom: 20,
    left: 40,
    right: 40,
  },
  createReportButton: {
    backgroundColor: '#0996a8',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
  },
  createReportIcon: {
    marginRight: 8,
  },
  createReportText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingTop: 20,
    paddingBottom: 100,
    paddingHorizontal: 16,
    minHeight: 300,
  },
  noInternetCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#ff9500',
    marginHorizontal: 20,
  },
  noInternetText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginVertical: 16,
  },
  modernEmptyCard: {
    width: '100%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 10,
    elevation: 5,
    borderWidth: 1,
    borderColor: '#eef2f3',
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 16,
    transform: [{ scale: 1.1 }],
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  modernTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#34495e',
    textAlign: 'center',
  },
  modernSubtitle: {
    fontSize: 14,
    color: '#7f8c8d',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  infoSection: {
    marginTop: 10,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#eef2f3',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#34495e',
    marginBottom: 12,
    textAlign: 'center',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#34495e',
    marginLeft: 10,
  },
  errorCard: {
    backgroundColor: '#fff0f0',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderColor: '#ffc0c0',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorCardText: {
    color: '#d8000c',
    fontSize: 14,
    textAlign: 'center',
  },
  contactMarker: {
    backgroundColor: '#0996a8',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  contactMarkerText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  observerMarker: {
    backgroundColor: '#34495e',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  fitBoundsButton: {
    backgroundColor: '#0996a8',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  fitBoundsButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  comingSoonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 20,
    top: -70,
  },
  comingSoonCard: {
    backgroundColor: '#fff',
    padding: 30,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#0996a8',
    marginHorizontal: 20,
    width: '100%',
    maxWidth: 350,
  },
  comingSoonIconContainer: {
    marginBottom: 20,
  },
  comingSoonTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 15,
  },
  comingSoonDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 15,
    lineHeight: 22,
  },
  comingSoonSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  comingSoonBadge: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  comingSoonBadgeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});