import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function JornadasScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <View style={styles.alertContainer}>
          <View style={styles.alertHeader}>
            <FontAwesome name="warning" size={20} color="#f39c12" />
            <Text style={styles.alertTitle}>Aviso</Text>
          </View>
          
          <Text style={styles.alertMessage}>
            A criação de jornadas estará disponível a partir de <Text style={styles.highlightText}>24 de Agosto</Text>.
          </Text>
          
          <View style={styles.tipContainer}>
            <FontAwesome name="map-marker" size={16} color="#e67e22" style={styles.tipIcon} />
            <Text style={styles.tipLabel}>Dica:</Text>
            <Text style={styles.tipText}>
              Certifique-se de que tem trajetos criados nas suas zonas de caça para poder criar jornadas.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    top: -25,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 100, // Add extra padding for tab bar
    justifyContent: 'center',
  },
  alertContainer: {
    backgroundColor: '#fff3cd',
    borderColor: '#ffeaa7',
    borderWidth: 1,
    borderRadius: 8,
    padding: 20,
    marginHorizontal: 10,
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  alertTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#f39c12',
    marginLeft: 10,
  },
  alertMessage: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  highlightText: {
    fontWeight: 'bold',
    color: '#e67e22',
  },
  tipContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#e67e22',
  },
  tipIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  tipLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#e67e22',
    marginRight: 5,
  },
  tipText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
    lineHeight: 20,
  },
}); 