import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, ActivityIndicator, TouchableOpacity, Image, RefreshControl } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import SafeSystemBars from '@/components/SafeSystemBars';
import * as Location from 'expo-location';
import { weatherService, WeatherData, WeatherForecast, ForecastDay } from '@/services/weatherService';

// Portuguese translations
const pt = {
  weatherConditions: 'Condições Meteorológicas',
  currentConditions: 'Condições Atuais',
  forecast3Days: 'Previsão 5 Dias',
  birdWatchingConditions: 'Condições para Observação',
  fetchingWeather: 'A obter dados meteorológicos...',
  weatherError: 'Erro ao obter dados meteorológicos',
  retryWeather: 'Tentar novamente',
  locationError: 'Erro ao obter localização',
  locationPermissionDenied: 'Permissão de localização negada',
  temperature: 'Temperatura',
  feelsLike: 'Sensação térmica',
  humidity: 'Humidade',
  pressure: 'Pressão atmosférica',
  windSpeed: 'Vento',
  windGust: 'Rajadas',
  visibility: 'Visibilidade',
  cloudiness: 'Nebulosidade',
  uvIndex: 'Índice UV',
  dewPoint: 'Ponto de orvalho',
  sunrise: 'Nascer do sol',
  sunset: 'Pôr do sol',
  moonPhase: 'Fase lunar',
  barometricTrend: 'Tendência barométrica',
  windCondition: 'Condição do vento',
  lastUpdated: 'Última atualização',
  pullToRefresh: 'Puxe para atualizar',
  weatherDetails: 'Detalhes Meteorológicos',
  astronomicalData: 'Dados Astronómicos',
  birdWatchingAssessment: 'Observação da Rola-Comum',
  // Days of week
  monday: 'Segunda',
  tuesday: 'Terça',
  wednesday: 'Quarta',
  thursday: 'Quinta',
  friday: 'Sexta',
  saturday: 'Sábado',
  sunday: 'Domingo',
};

export default function WeatherConditionsScreen() {
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [forecastData, setForecastData] = useState<ForecastDay[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [activeTab, setActiveTab] = useState<'current' | 'forecast'>('current');

  const fetchWeatherData = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setIsLoading(true);
    }
    setError(null);

    try {
      // Get current location
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setError(pt.locationPermissionDenied);
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      // Fetch weather forecast data (includes current + 3-day forecast)
      const weatherForecast = await weatherService.getWeatherForecast(
        location.coords.latitude,
        location.coords.longitude
      );

      if (weatherForecast) {
        setWeatherData(weatherForecast.current);
        setForecastData(weatherForecast.forecast);
        setLastUpdated(new Date());
      } else {
        setError(pt.weatherError);
      }
    } catch (error) {
      console.error('Error fetching weather:', error);
      setError(pt.locationError);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchWeatherData();
  }, []);

  const onRefresh = () => {
    fetchWeatherData(true);
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('pt-PT', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatLastUpdated = () => {
    if (!lastUpdated) return '';
    return lastUpdated.toLocaleTimeString('pt-PT', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDayName = (timestamp: number) => {
    const date = new Date(timestamp);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    
    if (date.toDateString() === tomorrow.toDateString()) {
      return 'Amanhã';
    }
    
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayKey = days[date.getDay()] as keyof typeof pt;
    return pt[dayKey];
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('pt-PT', {
      day: 'numeric',
      month: 'short'
    });
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0996a8" />
          <Text style={styles.loadingText}>{pt.fetchingWeather}</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <FontAwesome name="exclamation-triangle" size={48} color="#ff6b6b" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => fetchWeatherData()}>
            <FontAwesome name="refresh" size={16} color="#fff" />
            <Text style={styles.retryButtonText}>{pt.retryWeather}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SafeSystemBars style="light" />
      {/* Original: <StatusBar style="light" /> */}
      
      {/* Header */}
      <View style={styles.header}>
        <SafeAreaView edges={['top']} style={{ backgroundColor: '#0996a8' }}>
          <View style={{ height: 40 }} />
        </SafeAreaView>
        
        {/* Header Logo */}
        <View style={styles.headerLogo}>
          <Image 
            source={require('../assets/images/header-logo.png')}
            style={styles.logoImage}
          />
        </View>
        
        {/* Title Bar */}
        <View style={styles.titleBar}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}>
            <FontAwesome name="arrow-left" size={20} color="#0996a8" />
          </TouchableOpacity>
          <Text style={styles.titleText}>Condições</Text>
          <View style={styles.placeholder} />
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Button Navigation */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, activeTab === 'current' && styles.activeButton]}
            onPress={() => setActiveTab('current')}>
            <FontAwesome 
              name="thermometer-half" 
              size={16} 
              color={activeTab === 'current' ? '#fff' : '#0996a8'} 
              style={styles.buttonIcon}
            />
            <Text style={[styles.buttonText, activeTab === 'current' && styles.activeButtonText]}>
              {pt.currentConditions}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, activeTab === 'forecast' && styles.activeButton]}
            onPress={() => setActiveTab('forecast')}>
            <FontAwesome 
              name="calendar" 
              size={16} 
              color={activeTab === 'forecast' ? '#fff' : '#0996a8'} 
              style={styles.buttonIcon}
            />
            <Text style={[styles.buttonText, activeTab === 'forecast' && styles.activeButtonText]}>
              {pt.forecast3Days}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#0996a8']}
              tintColor="#0996a8"
            />
          }>
          
          {activeTab === 'current' && weatherData && (
            <>
              {/* Current Conditions Header */}
              <View style={styles.currentConditionsHeader}>
                <Image
                  source={{ uri: weatherService.getWeatherIconUrl(weatherData.icon) }}
                  style={styles.weatherIcon}
                  resizeMode="contain"
                />
                <View style={styles.mainWeatherInfo}>
                  <Text style={styles.temperature}>{weatherData.temperature}°C</Text>
                  <Text style={styles.description}>{weatherData.description}</Text>
                  <Text style={styles.feelsLike}>Sensação: {weatherData.feelsLike}°C</Text>
                </View>
              </View>

              {/* Bird Watching Assessment */}
              <View style={styles.assessmentCard}>
                <Text style={styles.sectionTitle}>{pt.birdWatchingAssessment}</Text>
                <Text style={styles.assessmentText}>
                  {weatherService.getBirdWatchingConditions(weatherData)}
                </Text>
                <Text style={styles.windConditionText}>
                  {weatherService.getWindConditionForBirdsWithTime(
                    weatherData.windSpeed,
                    Date.now() < weatherData.sunrise || Date.now() > weatherData.sunset
                  )}
                </Text>
              </View>

              {/* Wind Information */}
              <View style={styles.windCard}>
                <Text style={styles.sectionTitle}>Informação do Vento</Text>
                <View style={styles.windInfo}>
                  <View style={styles.windDetail}>
                    <FontAwesome name="location-arrow" size={20} color="#0996a8" />
                    <Text style={styles.windSpeed}>{weatherData.windSpeed} m/s</Text>
                    <Text style={styles.windDirection}>
                      {weatherService.getWindDirectionText(weatherData.windDirection)}
                    </Text>
                  </View>
                  {weatherData.windGust && (
                    <View style={styles.gustInfo}>
                      <Text style={styles.gustLabel}>Rajadas:</Text>
                      <Text style={styles.gustValue}>{weatherData.windGust} m/s</Text>
                    </View>
                  )}
                </View>
              </View>

              {/* Astronomical Data */}
              <View style={styles.astronomicalCard}>
                <Text style={styles.sectionTitle}>{pt.astronomicalData}</Text>
                <View style={styles.astronomicalGrid}>
                  <View style={styles.astronomicalItem}>
                    <FontAwesome name="sun-o" size={20} color="#ff9800" />
                    <Text style={styles.astronomicalLabel}>{pt.sunrise}</Text>
                    <Text style={styles.astronomicalValue}>
                      {formatTime(weatherData.sunrise)}
                    </Text>
                  </View>
                  
                  <View style={styles.astronomicalItem}>
                    <FontAwesome name="moon-o" size={20} color="#ff9800" />
                    <Text style={styles.astronomicalLabel}>{pt.sunset}</Text>
                    <Text style={styles.astronomicalValue}>
                      {formatTime(weatherData.sunset)}
                    </Text>
                  </View>

                  {weatherData.moonPhase !== undefined && (
                    <View style={styles.astronomicalItem}>
                      <FontAwesome name="circle-o" size={20} color="#666" />
                      <Text style={styles.astronomicalLabel}>{pt.moonPhase}</Text>
                      <Text style={styles.astronomicalValue}>
                        {weatherService.getMoonPhaseDescription(weatherData.moonPhase)}
                      </Text>
                    </View>
                  )}
                </View>
              </View>

              {/* Barometric Trend */}
              {weatherData.barometricTrend && (
                <View style={styles.barometricCard}>
                  <Text style={styles.sectionTitle}>{pt.barometricTrend}</Text>
                  <Text style={styles.barometricText}>{weatherData.barometricTrend}</Text>
                </View>
              )}

              {/* Weather Details - moved to last */}
              <View style={styles.detailsCard}>
                <Text style={styles.sectionTitle}>{pt.weatherDetails}</Text>
                <View style={styles.detailsGrid}>
                  <View style={styles.detailItem}>
                    <FontAwesome name="thermometer-half" size={16} color="#0996a8" />
                    <Text style={styles.detailLabel}>{pt.humidity}</Text>
                    <Text style={styles.detailValue}>{weatherData.humidity}%</Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <FontAwesome name="tachometer" size={16} color="#0996a8" />
                    <Text style={styles.detailLabel}>{pt.pressure}</Text>
                    <Text style={styles.detailValue}>{weatherData.pressure} hPa</Text>
                  </View>

                  <View style={styles.detailItem}>
                    <FontAwesome name="eye" size={16} color="#0996a8" />
                    <Text style={styles.detailLabel}>{pt.visibility}</Text>
                    <Text style={styles.detailValue}>{weatherData.visibility} km</Text>
                  </View>

                  <View style={styles.detailItem}>
                    <FontAwesome name="cloud" size={16} color="#0996a8" />
                    <Text style={styles.detailLabel}>{pt.cloudiness}</Text>
                    <Text style={styles.detailValue}>{weatherData.cloudiness}%</Text>
                  </View>

                  {weatherData.uvIndex !== undefined && (
                    <View style={styles.detailItem}>
                      <FontAwesome name="sun-o" size={16} color="#0996a8" />
                      <Text style={styles.detailLabel}>{pt.uvIndex}</Text>
                      <Text style={styles.detailValue}>
                        {weatherData.uvIndex} ({weatherService.getUVIndexDescription(weatherData.uvIndex)})
                      </Text>
                    </View>
                  )}

                  {weatherData.dewPoint !== undefined && (
                    <View style={styles.detailItem}>
                      <FontAwesome name="tint" size={16} color="#0996a8" />
                      <Text style={styles.detailLabel}>{pt.dewPoint}</Text>
                      <Text style={styles.detailValue}>{weatherData.dewPoint}°C</Text>
                    </View>
                  )}
                </View>
              </View>
            </>
          )}

          {activeTab === 'forecast' && forecastData && (
            <>
              {/* 5-Day Forecast */}
              {forecastData.map((day, index) => (
                <View key={index} style={styles.forecastCard}>
                  <View style={styles.forecastHeader}>
                    <View style={styles.forecastDateContainer}>
                      <Text style={styles.forecastDay}>{getDayName(day.date)}</Text>
                      <Text style={styles.forecastDate}>{formatDate(day.date)}</Text>
                    </View>
                    <View style={styles.forecastWeather}>
                      <Image
                        source={{ uri: weatherService.getWeatherIconUrl(day.icon) }}
                        style={styles.forecastIcon}
                        resizeMode="contain"
                      />
                      <View style={styles.forecastTemps}>
                        <Text style={styles.forecastTempMax}>{day.tempMax}°</Text>
                        <Text style={styles.forecastTempMin}>{day.tempMin}°</Text>
                      </View>
                    </View>
                  </View>
                  <Text style={styles.forecastDescription}>{day.description}</Text>
                  
                  {/* Bird Watching Assessment for Forecast */}
                  <View style={styles.forecastBirdWatchingCard}>
                    <Text style={styles.forecastBirdWatchingTitle}>Observação da Rola-Comum</Text>
                    <Text style={styles.forecastBirdWatchingText}>
                      {day.birdWatchingConditions}
                    </Text>
                    <Text style={styles.forecastWindConditionText}>
                      {day.windConditionForBirds}
                    </Text>
                  </View>
                  
                  <View style={styles.forecastDetails}>
                    <View style={styles.forecastDetailItem}>
                      <FontAwesome name="tint" size={14} color="#0996a8" />
                      <Text style={styles.forecastDetailText}>{day.humidity}%</Text>
                    </View>
                    <View style={styles.forecastDetailItem}>
                      <FontAwesome name="location-arrow" size={14} color="#0996a8" />
                      <Text style={styles.forecastDetailText}>{day.windSpeed} m/s</Text>
                    </View>
                    {day.uvIndex !== undefined && (
                      <View style={styles.forecastDetailItem}>
                        <FontAwesome name="sun-o" size={14} color="#0996a8" />
                        <Text style={styles.forecastDetailText}>UV {day.uvIndex}</Text>
                      </View>
                    )}
                    {day.visibility !== undefined && (
                      <View style={styles.forecastDetailItem}>
                        <FontAwesome name="eye" size={14} color="#0996a8" />
                        <Text style={styles.forecastDetailText}>{day.visibility} km</Text>
                      </View>
                    )}
                  </View>
                </View>
              ))}
            </>
          )}

          {/* Last Updated */}
          <View style={styles.lastUpdatedContainer}>
            <Text style={styles.lastUpdatedText}>
              {pt.lastUpdated}: {formatLastUpdated()}
            </Text>
            <Text style={styles.pullToRefreshText}>{pt.pullToRefresh}</Text>
          </View>
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#0996a8',
  },
  headerLogo: {
    position: 'absolute',
    left: 12,
    top: 42,
    zIndex: 1,
  },
  logoImage: {
    width: 90,
    height: 90,
    resizeMode: 'contain',
  },
  titleBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    height: 45,
    paddingHorizontal: 20,
  },
  backButton: {
    position: 'absolute',
    left: 20,
    padding: 5,
  },
  titleText: {
    fontSize: 15,
    color: '#666',
    fontWeight: '500',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0996a8',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  currentConditionsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#0996a8',
  },
  weatherIcon: {
    width: 80,
    height: 80,
    marginRight: 20,
  },
  mainWeatherInfo: {
    flex: 1,
  },
  temperature: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#0996a8',
  },
  description: {
    fontSize: 18,
    color: '#666',
    textTransform: 'capitalize',
    marginBottom: 4,
  },
  feelsLike: {
    fontSize: 14,
    color: '#888',
  },
  assessmentCard: {
    backgroundColor: '#e8f5e8',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#4caf50',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 12,
    textAlign: 'center',
  },
  assessmentText: {
    fontSize: 16,
    color: '#2e7d32',
    lineHeight: 22,
    marginBottom: 8,
    textAlign: 'center',
  },
  windConditionText: {
    fontSize: 14,
    color: '#388e3c',
    fontWeight: '500',
    textAlign: 'center',
  },
  detailsCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  detailsGrid: {
    flexDirection: 'column',
    gap: 16,
  },
  detailItem: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 12,
    paddingHorizontal: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    flex: 1,
    minWidth: 120,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'right',
    minWidth: 80,
  },
  windCard: {
    backgroundColor: '#fff3e0',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#ff9800',
  },
  windInfo: {
    alignItems: 'center',
  },
  windDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  windSpeed: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#e65100',
  },
  windDirection: {
    fontSize: 16,
    color: '#e65100',
  },
  gustInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  gustLabel: {
    fontSize: 14,
    color: '#e65100',
  },
  gustValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#e65100',
  },
  astronomicalCard: {
    backgroundColor: '#fff8e1',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  astronomicalGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    gap: 16,
  },
  astronomicalItem: {
    alignItems: 'center',
    minWidth: '30%',
  },
  astronomicalLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  astronomicalValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginTop: 2,
  },
  barometricCard: {
    backgroundColor: '#f3e5f5',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#9c27b0',
  },
  barometricText: {
    fontSize: 16,
    color: '#7b1fa2',
    fontWeight: '500',
    textAlign: 'center',
  },
  lastUpdatedContainer: {
    alignItems: 'center',
    paddingVertical: 20,
    marginBottom: 30,
  },
  lastUpdatedText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  pullToRefreshText: {
    fontSize: 12,
    color: '#999',
  },
  buttonContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 15,
    gap: 10,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: '#0996a8',
    backgroundColor: '#fff',
  },
  activeButton: {
    backgroundColor: '#0996a8',
    borderColor: '#0996a8',
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0996a8',
  },
  activeButtonText: {
    color: '#fff',
  },
  buttonIcon: {
    marginRight: 6,
  },
  forecastCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  forecastHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  forecastDateContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  forecastDay: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  forecastDate: {
    fontSize: 14,
    color: '#666',
  },
  forecastWeather: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  forecastIcon: {
    width: 40,
    height: 40,
    marginRight: 12,
  },
  forecastTemps: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  forecastTempMax: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  forecastTempMin: {
    fontSize: 14,
    color: '#666',
  },
  forecastDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  forecastDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    gap: 16,
  },
  forecastDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  forecastDetailText: {
    fontSize: 14,
    color: '#666',
  },
  forecastBirdWatchingCard: {
    backgroundColor: '#e8f5e8',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#4caf50',
  },
  forecastBirdWatchingTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 12,
    textAlign: 'center',
  },
  forecastBirdWatchingText: {
    fontSize: 16,
    color: '#2e7d32',
    lineHeight: 22,
    marginBottom: 8,
    textAlign: 'center',
  },
  forecastWindConditionText: {
    fontSize: 14,
    color: '#388e3c',
    fontWeight: '500',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
}); 