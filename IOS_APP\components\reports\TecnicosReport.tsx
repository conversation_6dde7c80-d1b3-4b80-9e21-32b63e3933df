import React, { useState, forwardRef, useImperativeHandle, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, ActivityIndicator, Image, TextInput, Animated, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ReportRef } from './ReportModal';
import { TecnicoProtocol, TecnicoReportData } from '@/types/reports';
import { WeatherData, WeatherService, weatherService } from '@/services/weatherService';
import { FontAwesome } from '@expo/vector-icons';
import { useNetwork } from '@/contexts/NetworkContext';
import * as Location from 'expo-location';
import ActiveMonitoring from '@/components/ActiveMonitoring';
import SafeSystemBars from '@/components/SafeSystemBars';
import TechnicianTrajectorySelection from '@/components/TechnicianTrajectorySelection';

// Trajectory data interface
interface TrajectoryData {
  id: string;
  name: string;
  description?: string;
  coordinates: Array<{ lat: number; lng: number }>;
  distance?: string;
  pointsCount?: number;
  createdAt: string;
  source: 'kmz' | 'manual';
  originalFileName?: string;
}

// Portuguese translations for technical reports
const pt = {
  reportName: 'Nome',
  reportNamePlaceholder: 'Introduza o nome do relatório...',
  reportNameRequired: 'Por favor, introduza um nome para o relatório',
  selectProtocol: 'Indique o protocolo que irá adotar',
  testVersionNote: 'Versão de teste - Apenas protocolo "Trajeto" disponível',
  protocolOptions: {
    trajeto: 'Trajeto',
    estacoes_escuta: 'Estações de escuta',
    metodo_mapas: 'Método dos mapas',
    contagens_pontos: 'Contagens em pontos específicos',
    captura_marcacao: 'Captura e marcação de indivíduos',
    acompanhamento_cacadas: 'Acompanhamento das caçadas',
    registos_ocasionais: 'Registos ocasionais',
  },
  comingSoon: 'Em breve',
  selectProtocolFirst: 'Por favor, selecione um protocolo primeiro',
  back: 'Voltar',
  continue: 'Continuar',
  iniciar: 'Iniciar',
  weatherConditions: 'Indique as condições meteorológicas',
  fetchingWeather: 'A obter dados meteorológicos...',
  weatherError: 'Erro ao obter dados meteorológicos',
  currentWeather: 'Condições Atuais',
  manualWeatherSelection: 'Seleção Manual das Condições',
  temperature: 'Temperatura',
  humidity: 'Humidade',
  windSpeed: 'Vento',
  pressure: 'Pressão',
  visibility: 'Visibilidade',
  cloudiness: 'Nebulosidade',
  observersCount: 'Indique o número de observadores',
  observersCountPlaceholder: '',
  monitoringMessage: 'Está em condições de iniciar a monitorização e reportar?',
  weatherOptions: {
    ceuLimpo: 'Céu limpo',
    nublado: 'Nublado',
    chuva: 'Chuva',
    ventoModeradoForte: 'Vento moderado a forte',
    outroQual: 'Outro. Qual?',
  },
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff', // White background for the whole screen
  },
  // Header styles are correct from before
  header: {
    backgroundColor: '#0996a8',
  },
  headerSafeArea: {
    backgroundColor: '#0996a8',
  },
  headerSpacing: {
    height: 40,
  },
  headerLogo: {
    position: 'absolute',
    left: 12,
    top: 42,
    zIndex: 1,
  },
  logoImage: {
    width: 90,
    height: 90,
    resizeMode: 'contain',
  },
  headerContent: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    height: 65,
    paddingTop: 0,
    position: 'relative',
  },
  titleText: {
    fontSize: 15,
    color: '#666',
    fontWeight: '500',
  },
  // Flexbox for ScrollView and Buttons
  scrollContent: {
    flex: 1, // This makes the scroll view take up all available space
  },
  scrollContentContainer: {
    paddingHorizontal: 20,
    paddingTop: 2,
    paddingBottom: 20, // Just some space at the very end
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 15,
  },
  // Other styles are correct
  reportNameContainer: {
    marginBottom: 20,
  },
  reportNameLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#676667',
    marginBottom: 8,
    textAlign: 'center',
  },
  reportNameInput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#fff',
    color: '#374151',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 1,
    elevation: 1,
  },
  reportNameInputFocused: {
    borderColor: '#0996a8',
    backgroundColor: '#f8fafc',
  },
  reportNameInputError: {
    borderColor: '#ef4444',
    backgroundColor: '#fef2f2',
  },
  reportNameErrorText: {
    fontSize: 12,
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 4,
    fontWeight: '500',
  },
  protocolSelectionContainer: {
    marginBottom: 5,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 15,
    marginTop: 0, // No extra margin needed
    textAlign: 'center',
  },
  protocolOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 1,
    elevation: 1,
  },
  protocolOptionSelected: {
    backgroundColor: '#f8fafc',
    borderColor: '#0996a8',
    shadowOpacity: 0.08,
    elevation: 2,
  },
  protocolRadio: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#d1d5db',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: '#fff',
  },
  protocolRadioSelected: {
    backgroundColor: '#0996a8',
    borderColor: '#0996a8',
  },
  protocolRadioDot: {
    width: 10,
    height: 10,
    backgroundColor: '#0996a8',
    borderRadius: 5,
  },
  protocolLabel: {
    flex: 1,
    fontSize: 15,
    color: '#374151',
    lineHeight: 20,
    fontWeight: '500',
  },
  protocolLabelSelected: {
    fontWeight: '600',
    color: '#0996a8',
  },
  protocolOptionDisabled: {
    backgroundColor: '#f9fafb',
    borderColor: '#e5e7eb',
    opacity: 0.6,
  },
  protocolRadioDisabled: {
    backgroundColor: '#f3f4f6',
    borderColor: '#d1d5db',
  },
  protocolLabelDisabled: {
    color: '#9ca3af',
  },
  testVersionNote: {
    fontSize: 14,
    color: '#0996a8',
    textAlign: 'center',
    marginBottom: 20,
    fontWeight: '500',
    backgroundColor: '#f0f9ff',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#0996a8',
  },
  comingSoonBadge: {
    backgroundColor: '#f59e0b',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginLeft: 8,
  },
  comingSoonText: {
    color: '#fff',
    fontSize: 11,
    fontWeight: '600',
  },
  protocolNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0996a8',
    marginRight: 8,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#6B7280',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  continueButton: {
    flex: 1,
    backgroundColor: '#0996a8',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  continueButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  backButton: {
    flex: 1,
    backgroundColor: '#6B7280',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Weather screen styles
  weatherContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  weatherCard: {
    backgroundColor: 'transparent',
    padding: 0,
    borderRadius: 0,
    marginBottom: 0,
    borderLeftWidth: 0,
    borderLeftColor: 'transparent',
    shadowColor: 'transparent',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  weatherHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: '#0996a8',
    padding: 16,
    borderRadius: 10,
    shadowColor: '#0996a8',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.12,
    shadowRadius: 3,
    elevation: 3,
  },
  weatherIcon: {
    width: 50,
    height: 50,
    marginRight: 12,
  },
  weatherIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  weatherIconImage: {
    width: 28,
    height: 28,
  },
  weatherMainInfo: {
    flex: 1,
  },
  weatherTemperature: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  weatherDescription: {
    fontSize: 13,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 2,
    textTransform: 'capitalize',
  },
  weatherDetailsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  weatherDetailItem: {
    alignItems: 'center',
    flex: 1,
  },
  weatherDetailLabel: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 2,
    textAlign: 'center',
    fontWeight: '500',
  },
  weatherDetailValue: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    textAlign: 'center',
  },
  weatherDetailIcon: {
    marginBottom: 4,
    width: 14,
    height: 14,
    textAlign: 'center',
  },
  manualWeatherContainer: {
    marginTop: 10,
  },
  weatherOptionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 1,
    elevation: 1,
  },
  weatherOptionSelected: {
    backgroundColor: '#f8fafc',
    borderColor: '#0996a8',
    shadowOpacity: 0.08,
    elevation: 2,
  },
  weatherCheckbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#d1d5db',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: '#fff',
  },
  weatherCheckboxChecked: {
    backgroundColor: '#0996a8',
    borderColor: '#0996a8',
  },
  weatherOptionLabel: {
    flex: 1,
    fontSize: 15,
    color: '#374151',
    lineHeight: 20,
    fontWeight: '500',
  },
  weatherOptionLabelSelected: {
    fontWeight: '600',
    color: '#0996a8',
  },
  weatherTextInput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    minHeight: 72,
    textAlignVertical: 'top',
    backgroundColor: '#fff',
    color: '#374151',
    fontSize: 14,
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 1,
    elevation: 1,
    fontWeight: '500',
  },
  checkboxSection: {
    marginBottom: 0,
  },
  checkboxContainer: {
    marginBottom: 12,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 10,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  checkboxRowSelected: {
    backgroundColor: '#f0f8ff',
    borderColor: '#0996a8',
    shadowOpacity: 0.1,
    elevation: 2,
  },
  checkbox: {
    width: 22,
    height: 22,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: '#fff',
  },
  checkboxChecked: {
    backgroundColor: '#0996a8',
    borderColor: '#0996a8',
  },
  checkboxLabel: {
    flex: 1,
    fontSize: 16,
    color: '#666',
    lineHeight: 20,
  },
  checkboxLabelSelected: {
    fontWeight: '600',
    color: '#0996a8',
  },
  textInputContainer: {
    marginTop: 8,
  },
  checkboxTextInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 10,
    padding: 12,
    minHeight: 80,
    textAlignVertical: 'top',
    backgroundColor: '#fff',
    color: '#666',
    fontSize: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  metricsSection: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  metricsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  metricsLabel: {
    flex: 1,
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  metricsInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 10,
    backgroundColor: '#fff',
    color: '#666',
    fontSize: 16,
    marginLeft: 0,
  },
  imagesSectionContainer: {
    marginTop: 20,
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginTop: 10,
    justifyContent: 'center',
  },
  imagePreviewContainer: {
    position: 'relative',
    width: 100,
    height: 100,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
  },
  imageDarkOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '30%',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 5,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255, 59, 48, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  addImageButton: {
    width: 100,
    height: 100,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#0996a8',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5fcfd',
    gap: 4,
  },
  addImageText: {
    fontSize: 12,
    color: '#0996a8',
    fontWeight: '500',
  },
  observersSection: {
    marginTop: 5,
    marginBottom: 15,
  },
  observersInputContainer: {
    marginBottom: 16,
    backgroundColor: '#ffffff',
    borderRadius: 10,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  observersLabel: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 0,
    fontWeight: '600',
    textAlign: 'center',
    marginLeft: 8,
  },
  observersLabelIcon: {
    marginRight: 8,
    color: '#0996a8',
  },
  observersInput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#f8fafc',
    color: '#374151',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  observersInputFocused: {
    borderColor: '#0996a8',
    backgroundColor: '#ffffff',
  },
  monitoringMessage: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    textAlign: 'left',
    backgroundColor: '#f0f9ff',
    padding: 16,
    borderRadius: 10,
    borderLeftWidth: 3,
    borderLeftColor: '#0996a8',
    fontWeight: '500',
    marginTop: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 1,
    elevation: 1,
  },
  monitoringTextWrapper: {
    alignItems: 'center',
    width: '100%',
  },
  monitoringTextContent: {
    textAlign: 'left',
    maxWidth: '90%',
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    fontWeight: '500',
  },
  loadingPlaceholder: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingPlaceholderText: {
    marginLeft: 8,
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
  },
});

// Protocol Selection Component
const ProtocolSelection = React.memo(({ 
  selectedProtocol,
  onProtocolSelect,
  onContinue,
  onCancel,
  reportName,
  onReportNameChange,
  reportNameError,
  isReportNameFocused,
  onReportNameFocus,
  onReportNameBlur
}: {
  selectedProtocol: TecnicoProtocol | null;
  onProtocolSelect: (protocol: TecnicoProtocol | null) => void;
  onContinue: () => void;
  onCancel?: () => void;
  reportName: string;
  onReportNameChange: (text: string) => void;
  reportNameError: string;
  isReportNameFocused: boolean;
  onReportNameFocus: () => void;
  onReportNameBlur: () => void;
}) => {
  const protocols: Array<{ key: TecnicoProtocol }> = [
    { key: 'trajeto' },
    { key: 'estacoes_escuta' },
    { key: 'metodo_mapas' },
    { key: 'contagens_pontos' },
    { key: 'captura_marcacao' },
    { key: 'acompanhamento_cacadas' },
    { key: 'registos_ocasionais' },
  ];

  const handleProtocolSelect = (protocol: TecnicoProtocol | null) => {
    // Only allow selection of 'trajeto' protocol
    if (protocol === 'trajeto') {
      onProtocolSelect(protocol);
    }
  };

  return (
    <View style={styles.protocolSelectionContainer}>
      {/* Report Name Input */}
      <View style={styles.reportNameContainer}>
        <Text style={styles.reportNameLabel}>{pt.reportName}</Text>
        <TextInput
          style={[
            styles.reportNameInput,
            isReportNameFocused && styles.reportNameInputFocused,
            reportNameError && styles.reportNameInputError
          ]}
          value={reportName}
          onChangeText={onReportNameChange}
          onFocus={onReportNameFocus}
          onBlur={onReportNameBlur}
          placeholder={pt.reportNamePlaceholder}
          placeholderTextColor="#9ca3af"
          maxLength={100}
          returnKeyType="done"
        />
        {reportNameError ? (
          <Text style={styles.reportNameErrorText}>{reportNameError}</Text>
        ) : null}
      </View>
      
      <Text style={styles.sectionTitle}>{pt.selectProtocol}</Text>
      {protocols.map(({ key }) => {
        const isDisabled = key !== 'trajeto';
        return (
          <TouchableOpacity
            key={key}
            style={[
              styles.protocolOption,
              selectedProtocol === key && styles.protocolOptionSelected,
              isDisabled && styles.protocolOptionDisabled
            ]}
            onPress={() => handleProtocolSelect(key)}
            activeOpacity={isDisabled ? 1 : 0.7}
            disabled={isDisabled}
          >
            <View style={[
              styles.protocolRadio,
              selectedProtocol === key && styles.protocolRadioSelected,
              isDisabled && styles.protocolRadioDisabled
            ]}>
              {selectedProtocol === key && <FontAwesome name="check" size={14} color="#fff" />}
            </View>
            <Text style={[
              styles.protocolLabel,
              selectedProtocol === key && styles.protocolLabelSelected,
              isDisabled && styles.protocolLabelDisabled
            ]}>
              {pt.protocolOptions[key]}
            </Text>
            {isDisabled && (
              <View style={styles.comingSoonBadge}>
                <Text style={styles.comingSoonText}>{pt.comingSoon}</Text>
              </View>
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );
});

// Weather Conditions Component
const WeatherConditions = React.memo(({ 
  onBack,
  weatherData,
  isLoadingWeather,
  hasInternet,
  selectedWeatherOptions,
  onWeatherOptionToggle,
  weatherOtherText,
  onWeatherOtherTextChange,
  observersCount,
  onObserversCountChange
}: {
  onBack: () => void;
  weatherData: WeatherData | null;
  isLoadingWeather: boolean;
  hasInternet: boolean;
  selectedWeatherOptions: Record<string, boolean>;
  onWeatherOptionToggle: (option: string) => void;
  weatherOtherText: string;
  onWeatherOtherTextChange: (text: string) => void;
  observersCount: string;
  onObserversCountChange: (text: string) => void;
}) => {
  const weatherOptionsAnimation = useRef(new Animated.Value(1)).current;

  const weatherOptions = [
    { key: 'ceuLimpo', label: pt.weatherOptions.ceuLimpo },
    { key: 'nublado', label: pt.weatherOptions.nublado },
    { key: 'chuva', label: pt.weatherOptions.chuva },
    { key: 'ventoModeradoForte', label: pt.weatherOptions.ventoModeradoForte },
    { key: 'outroQual', label: pt.weatherOptions.outroQual },
  ];

  // Helper function to get selected weather option
  const getSelectedWeatherOption = () => {
    return Object.entries(selectedWeatherOptions).find(([key, value]) => value === true)?.[0];
  };

  // Determine title based on internet connectivity
  const getWeatherTitle = () => {
    if (hasInternet) {
      return null; // No main title when we have internet (loading or loaded)
    }
    return pt.weatherConditions; // "Indique as condições meteorológicas"
  };

  return (
    <View style={styles.weatherContainer}>
      {hasInternet && isLoadingWeather && (
        <View style={styles.weatherCard}>
          <Text style={[styles.sectionTitle, { marginTop: 15, marginBottom: 10 }]}>
            Condições Meteorológicas
          </Text>
          
          <View style={styles.weatherHeader}>
            <View style={styles.weatherIconContainer}>
              <ActivityIndicator size="small" color="rgba(255, 255, 255, 0.8)" />
            </View>
            <View style={styles.weatherMainInfo}>
              <View style={styles.loadingPlaceholder}>
                <ActivityIndicator size="small" color="rgba(255, 255, 255, 0.8)" />
                <Text style={styles.loadingPlaceholderText}>{pt.fetchingWeather}</Text>
              </View>
            </View>
          </View>

          <View style={styles.weatherDetailsGrid}>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.humidity}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.windSpeed}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.pressure}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.visibility}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
          </View>
        </View>
      )}

      {hasInternet && !isLoadingWeather && weatherData && (
        <View style={styles.weatherCard}>
          <Text style={[styles.sectionTitle, { marginTop: 15, marginBottom: 10 }]}>
            Condições Meteorológicas
          </Text>
          
          <View style={styles.weatherHeader}>
            <View style={styles.weatherIconContainer}>
              <Image
                source={{ uri: weatherService.getWeatherIconUrl(weatherData.icon) }}
                style={styles.weatherIconImage}
                resizeMode="contain"
              />
            </View>
            <View style={styles.weatherMainInfo}>
              <Text style={styles.weatherTemperature}>{weatherData.temperature}°C</Text>
              <Text style={styles.weatherDescription}>{weatherData.description}</Text>
            </View>
          </View>

          <View style={styles.weatherDetailsGrid}>
            <View style={styles.weatherDetailItem}>
              <FontAwesome name="tint" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.humidity}</Text>
              <Text style={styles.weatherDetailValue}>{weatherData.humidity}%</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <FontAwesome name="flag" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.windSpeed}</Text>
              <Text style={styles.weatherDetailValue}>{weatherData.windSpeed} km/h</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <FontAwesome name="tachometer" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.pressure}</Text>
              <Text style={styles.weatherDetailValue}>{weatherData.pressure} hPa</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <FontAwesome name="eye" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.visibility}</Text>
              <Text style={styles.weatherDetailValue}>{weatherData.visibility} km</Text>
            </View>
          </View>
        </View>
      )}

      {/* Manual weather selection when offline */}
      {!hasInternet && (
        <View>
          <Text style={[styles.sectionTitle, { marginTop: 15, marginBottom: 15 }]}>
            {pt.weatherConditions}
            </Text>

        <Animated.View 
          style={[
            styles.manualWeatherContainer,
            {
              opacity: weatherOptionsAnimation,
              transform: [{
                scale: weatherOptionsAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.95, 1],
                })
              }]
            }
          ]}
        >
          {(() => {
            const selectedOption = getSelectedWeatherOption();
            
            if (selectedOption) {
              // Show only the selected option
              const selectedWeatherOption = weatherOptions.find(option => option.key === selectedOption);
              if (!selectedWeatherOption) return null;
              
              return (
                <View key={selectedOption}>
                  <TouchableOpacity
                    style={[styles.weatherOptionRow, styles.weatherOptionSelected]}
                    onPress={() => onWeatherOptionToggle(selectedOption)}
                    activeOpacity={0.7}
                  >
                    <View style={[styles.weatherCheckbox, styles.weatherCheckboxChecked]}>
                      <FontAwesome name="check" size={14} color="#fff" />
                    </View>
                    <Text style={[styles.weatherOptionLabel, styles.weatherOptionLabelSelected]}>
                      {selectedWeatherOption.label}
                    </Text>
                  </TouchableOpacity>
                  
                  {selectedOption === 'outroQual' && (
                    <View style={styles.textInputContainer}>
                      <TextInput
                        style={styles.weatherTextInput}
                        placeholder="Especifique as condições meteorológicas..."
                        placeholderTextColor="#999"
                        value={weatherOtherText}
                        onChangeText={onWeatherOtherTextChange}
                        multiline
                        numberOfLines={3}
                      />
                    </View>
                  )}
                </View>
              );
            }
            
            // Show all options when none are selected
            return weatherOptions.map(({ key, label }) => (
              <View key={key}>
                <TouchableOpacity
                  style={[
                    styles.weatherOptionRow,
                    selectedWeatherOptions[key] && styles.weatherOptionSelected
                  ]}
                  onPress={() => onWeatherOptionToggle(key)}
                  activeOpacity={0.7}
                >
                  <View style={[
                    styles.weatherCheckbox,
                    selectedWeatherOptions[key] && styles.weatherCheckboxChecked
                  ]}>
                    {selectedWeatherOptions[key] && <FontAwesome name="check" size={14} color="#fff" />}
                  </View>
                  <Text style={[
                    styles.weatherOptionLabel,
                    selectedWeatherOptions[key] && styles.weatherOptionLabelSelected
                  ]}>
                    {label}
                  </Text>
                </TouchableOpacity>
                
                {key === 'outroQual' && selectedWeatherOptions[key] && (
                  <View style={styles.textInputContainer}>
                    <TextInput
                      style={styles.weatherTextInput}
                      placeholder="Especifique as condições meteorológicas..."
                      placeholderTextColor="#999"
                      value={weatherOtherText}
                      onChangeText={onWeatherOtherTextChange}
                      multiline
                      numberOfLines={3}
                    />
                  </View>
                )}
              </View>
            ));
          })()}
        </Animated.View>
        </View>
      )}

      {/* Observers Count Section - Always show after weather information */}
      <View style={styles.observersSection}>
        <View style={styles.observersInputContainer}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginBottom: 12 }}>
            <FontAwesome name="users" size={18} style={styles.observersLabelIcon} />
            <Text style={styles.observersLabel}>{pt.observersCount}</Text>
          </View>
          <TextInput
            style={styles.observersInput}
            value={observersCount}
            onChangeText={(text) => {
              // Only allow numbers
              const numericText = text.replace(/[^0-9]/g, '');
              onObserversCountChange(numericText);
            }}
            keyboardType="numeric"
            maxLength={3}
            placeholder="0"
            placeholderTextColor="#999"
          />
        </View>
        
        <View style={styles.monitoringMessage}>
          <View style={styles.monitoringTextWrapper}>
            <Text style={styles.monitoringTextContent}>
              {pt.monitoringMessage}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
});

interface TecnicosReportProps {
  onSubmit: (data: TecnicoReportData) => void;
  isLoading: boolean;
  isUploadingImages: boolean;
  uploadProgress: number;
  weatherData?: WeatherData | null;
  showAlert: (config: { type: 'success' | 'error' | 'warning' | 'info'; message: string; onConfirm?: () => void }) => void;
  onProtocolChange?: (protocol: TecnicoProtocol | null) => void;
  onStepChange?: (step: 'protocol' | 'weather' | 'trajectory' | 'monitoring') => void;
  onStartMonitoring?: (protocol: TecnicoProtocol, startTime: Date, weatherData: WeatherData | null, observersCount?: number, reportName?: string, selectedTrajectory?: TrajectoryData | null) => void;
  onCancel?: () => void;
}

const TecnicosReport = forwardRef<ReportRef, TecnicosReportProps>(({ onSubmit, isLoading, isUploadingImages, uploadProgress, weatherData, showAlert, onProtocolChange, onStepChange, onStartMonitoring, onCancel }, ref) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const { hasInternetConnection } = useNetwork();
  
  // Navigation state
  const [currentStep, setCurrentStep] = useState<'protocol' | 'weather' | 'trajectory' | 'monitoring'>('protocol');
  
  // Trajectory state
  const [showTrajectorySelection, setShowTrajectorySelection] = useState(false);
  const [selectedTrajectory, setSelectedTrajectory] = useState<TrajectoryData | null>(null);
  
  // Report name state
  const [reportName, setReportName] = useState('');
  const [reportNameError, setReportNameError] = useState('');
  const [isReportNameFocused, setIsReportNameFocused] = useState(false);
  
  // Protocol selection state
  const [selectedProtocol, setSelectedProtocol] = useState<TecnicoProtocol | null>(null);
  
  // Weather state
  const [currentWeatherData, setCurrentWeatherData] = useState<WeatherData | null>(null);
  const [isLoadingWeather, setIsLoadingWeather] = useState(false);
  const [selectedWeatherOptions, setSelectedWeatherOptions] = useState<Record<string, boolean>>({
    ceuLimpo: false,
    nublado: false,
    chuva: false,
    ventoModeradoForte: false,
    outroQual: false,
  });
  const [weatherOtherText, setWeatherOtherText] = useState('');
  const [observersCount, setObserversCount] = useState('');

  // Monitoring state
  const [monitoringStartTime, setMonitoringStartTime] = useState<Date | null>(null);

  const handleProtocolSelect = (protocol: TecnicoProtocol | null) => {
    setSelectedProtocol(protocol);
    onProtocolChange?.(protocol);
  };

  const validateReportName = () => {
    if (!reportName.trim()) {
      setReportNameError(pt.reportNameRequired);
      return false;
    }
    setReportNameError('');
    return true;
  };

  const handleContinueToWeather = () => {
    // Validate report name before continuing
    if (!validateReportName()) {
      return;
    }
    
    setCurrentStep('weather');
    onStepChange?.('weather');
    
    // Fetch weather data if online
    if (hasInternetConnection()) {
      fetchWeatherData();
    }
  };

  const handleBackToProtocol = () => {
    setCurrentStep('protocol');
    onStepChange?.('protocol');
    setCurrentWeatherData(null);
    setIsLoadingWeather(false);
  };

  const handleCancelProtocol = () => {
    // Call the onCancel prop to close the modal and go back to location screen
    onCancel?.();
  };

  const handleBackToWeather = () => {
    setCurrentStep('weather');
    onStepChange?.('weather');
  };

  const handleContinueToTrajectory = () => {
    // Validate observers count
    if (!observersCount || observersCount.trim() === '' || observersCount === '0') {
      showAlert({
        type: 'info',
        message: 'Por favor, indique o número de observadores antes de continuar (mínimo 1).',
      });
      return;
    }
    
    // Validate weather conditions when offline
    if (!hasInternetConnection()) {
      const hasSelectedWeatherOption = Object.values(selectedWeatherOptions).some(value => value === true);
      if (!hasSelectedWeatherOption) {
        showAlert({
          type: 'info',
          message: 'Por favor, selecione as condições meteorológicas antes de continuar.',
        });
        return;
      }
      
      // If "outroQual" is selected, validate that text is provided
      if (selectedWeatherOptions.outroQual && (!weatherOtherText || weatherOtherText.trim() === '')) {
        showAlert({
          type: 'info',
          message: 'Por favor, especifique as condições meteorológicas.',
        });
        return;
      }
    }
    
    // Show trajectory selection modal
    setShowTrajectorySelection(true);
  };

  const handleTrajectorySelected = (trajectory: TrajectoryData | null) => {
    setSelectedTrajectory(trajectory);
    setShowTrajectorySelection(false);
    handleStartMonitoringWithTrajectory(trajectory);
  };

  const handleContinueWithoutTrajectory = () => {
    setSelectedTrajectory(null);
    setShowTrajectorySelection(false);
    handleStartMonitoring();
  };

  const handleCloseTrajectorySelection = () => {
    setShowTrajectorySelection(false);
  };

  const handleStartMonitoringWithTrajectory = (trajectory: TrajectoryData | null) => {
    // Just call the original handleStartMonitoring but pass the trajectory directly
    handleStartMonitoring(trajectory);
  };

  const handleStartMonitoring = (trajectoryOverride?: TrajectoryData | null) => {
    // Validate report name before starting monitoring
    if (!validateReportName()) {
      return;
    }

    const startTime = new Date();
    setMonitoringStartTime(startTime);
    
    // Pass weather data to parent for monitoring session
    let weatherToPass = null;
    
    if (hasInternetConnection()) {
      // Use online weather data
      weatherToPass = currentWeatherData;
    } else {
      // Create manual weather data from selected options that matches WeatherData interface
      const selectedOption = Object.entries(selectedWeatherOptions).find(([key, value]) => value === true);
      if (selectedOption) {
        const [optionKey] = selectedOption;
        const optionLabel = pt.weatherOptions[optionKey as keyof typeof pt.weatherOptions] || optionKey;
        
        // Map weather options to realistic values
        const weatherDefaults = {
          'ensolarado': { temp: 25, humidity: 40, pressure: 1020, wind: 5, visibility: 15, clouds: 10, icon: '01d' },
          'parcialmenteNublado': { temp: 22, humidity: 50, pressure: 1015, wind: 8, visibility: 12, clouds: 40, icon: '02d' },
          'nublado': { temp: 20, humidity: 60, pressure: 1010, wind: 10, visibility: 10, clouds: 80, icon: '03d' },
          'chuvisco': { temp: 18, humidity: 75, pressure: 1005, wind: 12, visibility: 8, clouds: 90, icon: '09d' },
          'chuva': { temp: 15, humidity: 85, pressure: 1000, wind: 15, visibility: 5, clouds: 95, icon: '10d' },
          'chuvaForte': { temp: 12, humidity: 90, pressure: 995, wind: 20, visibility: 3, clouds: 100, icon: '11d' },
          'nevoeiro': { temp: 16, humidity: 95, pressure: 1013, wind: 3, visibility: 1, clouds: 100, icon: '50d' },
          'vento': { temp: 20, humidity: 45, pressure: 1018, wind: 25, visibility: 15, clouds: 20, icon: '01d' },
        };
        
        const defaults = weatherDefaults[optionKey as keyof typeof weatherDefaults] || weatherDefaults.ensolarado;
        
        // Create a proper WeatherData object
        weatherToPass = {
          temperature: defaults.temp,
          feelsLike: defaults.temp,
          description: optionKey === 'outroQual' && weatherOtherText ? weatherOtherText : optionLabel,
          humidity: defaults.humidity,
          pressure: defaults.pressure,
          windSpeed: defaults.wind,
          windDirection: 0,
          visibility: defaults.visibility,
          cloudiness: defaults.clouds,
          uvIndex: Math.max(0, (defaults.temp - 10) / 5), // Rough UV estimation
          dewPoint: defaults.temp - ((100 - defaults.humidity) / 5), // Rough dew point calculation
          icon: defaults.icon,
          timestamp: Date.now(),
          sunrise: Date.now() - (2 * 60 * 60 * 1000), // 2 hours ago
          sunset: Date.now() + (10 * 60 * 60 * 1000), // 10 hours from now
          barometricTrend: defaults.pressure > 1015 ? 'Pressão alta' : defaults.pressure < 1010 ? 'Pressão baixa' : 'Pressão normal'
        } as WeatherData;
      }
    }
    
    // Call onStartMonitoring callback with report name and selected trajectory
    const trajectoryToUse = trajectoryOverride !== undefined ? trajectoryOverride : selectedTrajectory;

    onStartMonitoring?.(selectedProtocol!, startTime, weatherToPass, observersCount ? parseInt(observersCount) : undefined, reportName, trajectoryToUse);
    
    // Set monitoring step AFTER calling onStartMonitoring
    // This ensures the parent has time to handle the transition
    setCurrentStep('monitoring');
    onStepChange?.('monitoring');
    

  };

  const handleTerminateMonitoring = () => {
    setCurrentStep('weather');
    onStepChange?.('weather');
    setMonitoringStartTime(null);
    
    showAlert({
      type: 'success',
      message: 'Monitorização terminada com sucesso.',
    });
  };

  const fetchWeatherData = async () => {
    setIsLoadingWeather(true);
    try {
      // Get current location
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setIsLoadingWeather(false);
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      // Fetch weather data
      const weather = await weatherService.getCurrentWeather(
        location.coords.latitude,
        location.coords.longitude
      );

      if (weather) {
        setCurrentWeatherData(weather);
      }
    } catch (error) {
      console.error('Error fetching weather:', error);
    } finally {
      setIsLoadingWeather(false);
    }
  };

  const handleWeatherOptionToggle = (option: string) => {
    if (!hasInternetConnection()) {
      // For offline mode, implement single selection like ColaboradorReport
      const wasNothingSelected = !Object.values(selectedWeatherOptions).some(value => value === true);
      const isCurrentlySelected = selectedWeatherOptions[option];
      
      if (wasNothingSelected && !isCurrentlySelected) {
        // Selecting first option - reset all and select this one
        const resetOptions = Object.keys(selectedWeatherOptions).reduce((acc, key) => {
          acc[key] = false;
          return acc;
        }, {} as Record<string, boolean>);
        
        setSelectedWeatherOptions({
          ...resetOptions,
          [option]: true
        });
      } else if (isCurrentlySelected) {
        // Deselecting current option - reset all
        const resetOptions = Object.keys(selectedWeatherOptions).reduce((acc, key) => {
          acc[key] = false;
          return acc;
        }, {} as Record<string, boolean>);
        
        setSelectedWeatherOptions(resetOptions);
        
        // Clear other text if deselecting "outroQual"
        if (option === 'outroQual') {
          setWeatherOtherText('');
        }
      } else {
        // Selecting different option - reset all and select this one
        const resetOptions = Object.keys(selectedWeatherOptions).reduce((acc, key) => {
          acc[key] = false;
          return acc;
        }, {} as Record<string, boolean>);
        
        setSelectedWeatherOptions({
          ...resetOptions,
          [option]: true
        });
        
        // Clear other text if not selecting "outroQual"
        if (option !== 'outroQual') {
          setWeatherOtherText('');
        }
      }
    } else {
      // For online mode, keep the original multi-selection behavior
      setSelectedWeatherOptions(prev => ({
        ...prev,
        [option]: !prev[option]
      }));
      
      // Clear other text if not "outroQual"
      if (option !== 'outroQual') {
        setWeatherOtherText('');
      }
    }
  };

  const handleWeatherOtherTextChange = (text: string) => {
    setWeatherOtherText(text);
  };

  const handleObserversCountChange = (text: string) => {
    setObserversCount(text);
  };

  // Expose submit function to parent
  useImperativeHandle(ref, () => ({
    submit: () => {
      if (currentStep === 'protocol') {
        if (selectedProtocol) {
          handleContinueToWeather();
        } else {
          showAlert({
            type: 'error',
            message: pt.selectProtocolFirst,
          });
        }
      } else if (currentStep === 'weather') {
        // Validate observers count
        if (!observersCount || observersCount.trim() === '' || observersCount === '0') {
          showAlert({
            type: 'info',
            message: 'Por favor, indique o número de observadores antes de iniciar (mínimo 1).',
          });
          return;
        }
        
        // Validate weather conditions when offline
        if (!hasInternetConnection()) {
          const hasSelectedWeatherOption = Object.values(selectedWeatherOptions).some(value => value === true);
          if (!hasSelectedWeatherOption) {
            showAlert({
              type: 'info',
              message: 'Por favor, selecione as condições meteorológicas antes de iniciar.',
            });
            return;
          }
          
          // If "outroQual" is selected, validate that text is provided
          if (selectedWeatherOptions.outroQual && (!weatherOtherText || weatherOtherText.trim() === '')) {
            showAlert({
              type: 'info',
              message: 'Por favor, especifique as condições meteorológicas.',
            });
            return;
          }
        }
        
        handleStartMonitoring();
      } else if (currentStep === 'monitoring') {
        // This shouldn't happen as monitoring screen has its own controls
        return;
      }
    }
  }));

  return (
    <View style={styles.container}>
      {/* Header structure */}
      <View>
        <SafeSystemBars style="light" />
        <View style={styles.header}>
          <SafeAreaView edges={['top']} style={styles.headerSafeArea}>
            <View style={styles.headerSpacing} />
          </SafeAreaView>
          <View style={styles.headerLogo}>
            <Image 
              source={require('../../assets/images/header-logo.png')}
              style={styles.logoImage}
            />
          </View>
          <View style={styles.headerContent}>
            <Text style={styles.titleText}>Novo Relatório</Text>
          </View>
        </View>
      </View>

      <ScrollView 
        style={styles.scrollContent}
        contentContainerStyle={styles.scrollContentContainer}
        keyboardShouldPersistTaps="handled"
      >
        {currentStep === 'protocol' && (
          <ProtocolSelection
            selectedProtocol={selectedProtocol}
            onProtocolSelect={handleProtocolSelect}
            onContinue={handleContinueToWeather}
            onCancel={handleCancelProtocol}
            reportName={reportName}
            onReportNameChange={setReportName}
            reportNameError={reportNameError}
            isReportNameFocused={isReportNameFocused}
            onReportNameFocus={() => setIsReportNameFocused(true)}
            onReportNameBlur={() => {
              setIsReportNameFocused(false);
              validateReportName();
            }}
          />
        )}
        
        {currentStep === 'weather' && (
          <WeatherConditions
            onBack={handleBackToProtocol}
            weatherData={currentWeatherData}
            isLoadingWeather={isLoadingWeather}
            hasInternet={hasInternetConnection()}
            selectedWeatherOptions={selectedWeatherOptions}
            onWeatherOptionToggle={handleWeatherOptionToggle}
            weatherOtherText={weatherOtherText}
            onWeatherOtherTextChange={handleWeatherOtherTextChange}
            observersCount={observersCount}
            onObserversCountChange={handleObserversCountChange}
          />
        )}
        
        {currentStep === 'monitoring' && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0996a8" />
            <Text style={styles.loadingText}>A iniciar monitorização...</Text>
          </View>
        )}
      </ScrollView>

      {/* Fixed buttons at bottom */}
      {currentStep === 'protocol' && (
        <SafeAreaView edges={['bottom']} style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => onCancel?.()}
            activeOpacity={0.7}
          >
            <FontAwesome name="times" size={16} color="#fff" style={{ marginRight: 8 }} />
            <Text style={styles.cancelButtonText}>Cancelar</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.continueButton, !selectedProtocol && styles.continueButtonDisabled]}
            onPress={handleContinueToWeather}
            disabled={!selectedProtocol}
            activeOpacity={0.7}
          >
            <FontAwesome name="arrow-right" size={16} color="#fff" style={{ marginRight: 8 }} />
            <Text style={styles.continueButtonText}>{pt.continue}</Text>
          </TouchableOpacity>
        </SafeAreaView>
      )}

      {currentStep === 'weather' && (
        <SafeAreaView edges={['bottom']} style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => onCancel?.()}
            activeOpacity={0.7}
          >
            <FontAwesome name="times" size={16} color="#fff" style={{ marginRight: 8 }} />
            <Text style={styles.cancelButtonText}>Cancelar</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.continueButton}
            onPress={handleContinueToTrajectory}
            activeOpacity={0.7}
          >
            <FontAwesome name="arrow-right" size={16} color="#fff" style={{ marginRight: 8 }} />
            <Text style={styles.continueButtonText}>{pt.continue}</Text>
          </TouchableOpacity>
        </SafeAreaView>
      )}

      {/* No buttons shown during monitoring step - modal will close automatically */}
      
      {/* Trajectory Selection Modal */}
      <TechnicianTrajectorySelection
        visible={showTrajectorySelection}
        onClose={handleCloseTrajectorySelection}
        onSelectTrajectory={handleTrajectorySelected}
        onContinueWithoutTrajectory={handleContinueWithoutTrajectory}
      />
    </View>
  );
});

export default TecnicosReport; 