import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TextInput, TouchableOpacity, Alert, ActivityIndicator, Image, Platform, Keyboard, ScrollView, KeyboardAvoidingView } from 'react-native';
import { Text } from 'react-native';
import { router, useRootNavigationState } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@/contexts/AuthContext';
import { useNetwork } from '@/contexts/NetworkContext';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import NetworkStatusIndicator from '@/components/NetworkStatusIndicator';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Portuguese translations
const pt = {
  welcomeBack: 'Pro Rola - APP Teste',
  welcomeText: 'Bem-vindo',
  emailPlaceholder: 'Introduza o seu email',
  passwordPlaceholder: 'Introduza a sua password',
  login: 'Efectuar Login',
  register: 'Registar',
  noAccount: 'Não tem conta?',
  forgotPassword: 'Esqueceu a password?',
  errorTitle: 'Erro',
  fillFields: 'Por favor, preencha todos os campos',
  loginFailed: 'Falha no login. Por favor, tente novamente.',
  loading: 'A carregar...',
  noInternetLogin: 'Não é possível efectuar login sem ligação à Internet',
  noInternetRegister: 'Não é possível registar sem ligação à Internet',
  noInternetForgotPassword: 'Não é possível recuperar password sem ligação à Internet',
  offlineMode: 'Modo Offline',
  offlineMessage: 'As funcionalidades de login, registo e recuperação de password requerem ligação à Internet.',
};

export default function LoginScreen() {
  const colorScheme = useColorScheme();
  const rootNavigationState = useRootNavigationState();
  const { login } = useAuth();
  const { hasInternetConnection } = useNetwork();
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true)
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false)
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Check for pending approval message
  useEffect(() => {
    const checkPendingApprovalMessage = async () => {
      try {
        // Add a small delay to ensure all AuthContext state changes have settled
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const message = await AsyncStorage.getItem('pendingApprovalMessage');
        if (message) {
          showAlert({
            type: 'info',
            title: 'Conta Pendente de Aprovação',
            message: message
          });
          // Clear the message after showing it
          await AsyncStorage.removeItem('pendingApprovalMessage');
        }
      } catch (error) {
        console.error('Error checking pending approval message:', error);
      }
    };

    checkPendingApprovalMessage();
  }, []);

  // Wait for the navigation state to be ready before rendering
  if (!rootNavigationState?.key) {
    return null;
  }

  const handleLogin = async () => {
    if (!hasInternetConnection()) {
      showAlert({
        type: 'warning',
        message: pt.noInternetLogin
      });
      return;
    }

    if (!formData.email || !formData.password) {
      showAlert({
        type: 'error',
        message: pt.fillFields
      });
      return;
    }

    setIsLoading(true);
    try {
      await login(formData.email, formData.password);
      // If successful, the AuthContext will handle navigation
    } catch (error: any) {
      // Check if this is an admin access error
      if (error.message && error.message.includes('administrador só podem ser utilizadas')) {
        showAlert({
          type: 'warning',
          title: 'Acesso Restrito',
          message: error.message
        });
      }
      // Check if this is a technician approval error
      else if (error.message && error.message.includes('aguarde a aprovação')) {
        showAlert({
          type: 'info',
          title: 'Conta Pendente de Aprovação',
          message: error.message
        });
      }
      // Check if this is a too-many-requests error
      else if (error.code === 'auth/too-many-requests' || (error.message && error.message.includes('too-many-requests'))) {
        showAlert({
          type: 'info',
          title: 'Muitas Tentativas',
          message: 'Demasiadas tentativas de login. Por favor, aguarde alguns minutos antes de tentar novamente ou redefina a sua palavra-passe.'
        });
      }
      // Check if this is an informational message (better formatted)
      else if (error.message && error.message.startsWith('INFO:')) {
        showAlert({
          type: 'info',
          title: 'Verificar Dados',
          message: error.message.substring(5) // Remove "INFO:" prefix
        });
      } else {
        showAlert({
          type: 'error',
          message: error.message || pt.loginFailed
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
      <View style={styles.mainContainer}>
        <View style={styles.header}>
          <SafeAreaView edges={['top']} style={styles.headerContent}>
            <Image 
              source={require('../../assets/images/header-logo.png')}
              style={styles.logo}
            />
          </SafeAreaView>
        </View>

        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <View style={styles.formContainer}>
            <View style={styles.titleContainer}>
              <Text style={styles.welcomeText}>{pt.welcomeText}</Text>
              <NetworkStatusIndicator />
            </View>

            {!hasInternetConnection() && (
              <View style={styles.offlineCard}>
                <FontAwesome6 
                  name="wifi" 
                  size={24} 
                  color="#ff9500" 
                  style={{ opacity: 0.8 }}
                />
                <Text style={styles.offlineModeText}>{pt.offlineMode}</Text>
                <Text style={styles.offlineMessageText}>{pt.offlineMessage}</Text>
              </View>
            )}

            {hasInternetConnection() && (
              <>
                <View style={styles.inputContainer}>
                  <View style={styles.inputWrapper}>
                    <View style={styles.iconContainer}>
                      <FontAwesome name="envelope" size={20} color="#999" />
                    </View>
                    <TextInput
                      style={[styles.input, styles.inputWithIcon]}
                      placeholder={pt.emailPlaceholder}
                      placeholderTextColor="#999"
                      keyboardType="email-address"
                      autoCapitalize="none"
                      value={formData.email}
                      onChangeText={(text) => setFormData({ ...formData, email: text })}
                      editable={!isLoading}
                      testID="email_input"
                      accessibilityLabel="email_input"
                      nativeID="email_input"
                    />
                  </View>
                </View>

                <View style={styles.inputContainer}>
                  <View style={styles.inputWrapper}>
                    <View style={styles.iconContainer}>
                      <FontAwesome name="lock" size={20} color="#999" />
                    </View>
                    <TextInput
                      style={[styles.input, styles.inputWithIcon, styles.passwordInput]}
                      placeholder={pt.passwordPlaceholder}
                      placeholderTextColor="#999"
                      secureTextEntry={!showPassword}
                      value={formData.password}
                      onChangeText={(text) => setFormData({ ...formData, password: text })}
                      editable={!isLoading}
                      testID="password_input"
                      accessibilityLabel="password_input"
                      nativeID="password_input"
                    />
                    <TouchableOpacity 
                      style={styles.eyeIcon}
                      onPress={() => setShowPassword(!showPassword)}>
                      <FontAwesome 
                        name={showPassword ? "eye" : "eye-slash"} 
                        size={20} 
                        color="#999" 
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              </>
            )}

            {hasInternetConnection() ? (
              <>
                <TouchableOpacity
                  style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
                  onPress={handleLogin}
                  disabled={isLoading}>
                  {isLoading ? (
                    <ActivityIndicator color="#FFFFFF" />
                  ) : (
                    <>
                      <FontAwesome name="sign-in" size={20} color="#FFFFFF" style={styles.buttonIcon} />
                      <Text style={styles.buttonText}>{pt.login}</Text>
                    </>
                  )}
                </TouchableOpacity>

                <TouchableOpacity 
                  style={styles.forgotPasswordButton}
                  onPress={() => router.push('/auth/reset-password')}>
                  <FontAwesome name="key" size={16} color="#666" style={styles.buttonIcon} />
                  <Text style={styles.forgotPasswordText}>{pt.forgotPassword}</Text>
                </TouchableOpacity>

                {!keyboardVisible && (
                  <View style={styles.registerContainer}>
                    <Text style={styles.noAccountText}>{pt.noAccount}</Text>
                    <TouchableOpacity 
                      style={styles.registerButton}
                      onPress={() => router.push('/auth/register-type')} 
                      disabled={isLoading}>
                      <FontAwesome name="user-plus" size={20} color="#FFFFFF" style={styles.buttonIcon} />
                      <Text style={styles.registerButtonText}>{pt.register}</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </>
            ) : (
              <View style={styles.disabledButtonsContainer}>
                <View style={[styles.button, styles.disabledButton]}>
                  <FontAwesome name="sign-in" size={20} color="#999" style={styles.buttonIcon} />
                  <Text style={[styles.buttonText, styles.disabledButtonText]}>{pt.login}</Text>
                </View>

                <View style={[styles.forgotPasswordButton, styles.disabledButton]}>
                  <FontAwesome name="key" size={16} color="#999" style={styles.buttonIcon} />
                  <Text style={[styles.forgotPasswordText, styles.disabledButtonText]}>{pt.forgotPassword}</Text>
                </View>

                {!keyboardVisible && (
                  <View style={styles.registerContainer}>
                    <Text style={[styles.noAccountText, styles.disabledText]}>{pt.noAccount}</Text>
                    <View style={[styles.registerButton, styles.disabledButton]}>
                      <FontAwesome name="user-plus" size={20} color="#999" style={styles.buttonIcon} />
                      <Text style={[styles.registerButtonText, styles.disabledButtonText]}>{pt.register}</Text>
                    </View>
                  </View>
                )}
              </View>
            )}
          </View>
        </ScrollView>

        <SafeAreaView edges={['bottom']} style={styles.footer}>
          <Text style={styles.copyrightText}>
            © {new Date().getFullYear()} Todos os direitos reservados ICNF
          </Text>
        </SafeAreaView>
      </View>

      <CustomAlert
        visible={isVisible}
        type={config.type}
        message={config.message}
        onClose={hideAlert}
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#0996a8',
    height: 80,
    zIndex: 2,
    elevation: 2,
  },
  headerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 20,
  },
  logo: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
    top: 30,
    zIndex: 2,
  },
  scrollView: {
    flex: 1,
    marginTop: 50,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  formContainer: {
    padding: 20,
    paddingTop: 30,
  },
  titleContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  welcomeText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  iconContainer: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: '#666',
  },
  inputWithIcon: {
    paddingRight: 15,
  },
  passwordInput: {
    paddingRight: 50,
  },
  eyeIcon: {
    position: 'absolute',
    right: 15,
    top: 15,
  },
  button: {
    flexDirection: 'row',
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0996a8',
    marginTop: 20,
    alignSelf: 'center',
    paddingHorizontal: 40,
    minWidth: 220,
  },
  buttonIcon: {
    marginRight: 10,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  forgotPasswordButton: {
    marginTop: 15,
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignSelf: 'center',
    flexDirection: 'row',
    minWidth: 220,
  },
  forgotPasswordText: {
    color: '#666',
    fontSize: 14,
  },
  registerContainer: {
    marginTop: 30,
    alignItems: 'center',
    gap: 10,
  },
  noAccountText: {
    color: '#666',
    fontSize: 14,
  },
  registerButton: {
    flexDirection: 'row',
    backgroundColor: '#0996a8',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    alignItems: 'center',
  },
  registerButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    backgroundColor: '#0996a8',
    padding: 10,
    width: '100%',
  },
  copyrightText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
  },
  offlineCard: {
    backgroundColor: '#fef7f0',
    padding: 20,
    borderRadius: 12,
    marginBottom: 30,
    alignItems: 'center',
    borderLeftWidth: 4,
    borderLeftColor: '#ff9500',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  offlineModeText: {
    color: '#e65100',
    fontSize: 18,
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 8,
  },
  offlineMessageText: {
    color: '#bf360c',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 10,
  },
  disabledButtonsContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
    borderWidth: 1,
    opacity: 0.6,
  },
  disabledButtonText: {
    color: '#999',
  },
  disabledText: {
    color: '#999',
  },
}); 