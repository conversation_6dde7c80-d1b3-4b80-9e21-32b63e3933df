import { useState, useEffect } from 'react';
import NetInfo from '@react-native-community/netinfo';
import { t } from '@/config/translations';

export interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: string | null;
  isLoading: boolean;
}

export const useNetworkConnectivity = () => {
  const [networkState, setNetworkState] = useState<NetworkState>({
    isConnected: false,
    isInternetReachable: false,
    type: null,
    isLoading: true,
  });

  useEffect(() => {
    // Get initial network state
    const getInitialState = async () => {
      try {
        const state = await NetInfo.fetch();
        setNetworkState({
          isConnected: state.isConnected ?? false,
          isInternetReachable: state.isInternetReachable ?? false,
          type: state.type,
          isLoading: false,
        });
      } catch (error) {
        console.error('Error fetching initial network state:', error);
        setNetworkState(prev => ({ ...prev, isLoading: false }));
      }
    };

    getInitialState();

    // Subscribe to network state changes
    const unsubscribe = NetInfo.addEventListener(state => {
      setNetworkState({
        isConnected: state.isConnected ?? false,
        isInternetReachable: state.isInternetReachable ?? false,
        type: state.type,
        isLoading: false,
      });
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // Helper function to check if we have a good internet connection
  const hasInternetConnection = () => {
    return networkState.isConnected && networkState.isInternetReachable;
  };

  // Helper function to get connection type description
  const getConnectionType = () => {
    if (!networkState.isConnected) return t('network.disconnected.noConnection');
    
    switch (networkState.type) {
      case 'wifi':
        return t('network.connected.wifi');
      case 'cellular':
        return t('network.connected.cellular');
      case 'ethernet':
        return t('network.connected.ethernet');
      case 'bluetooth':
        return t('network.connected.bluetooth');
      default:
        return t('network.connected.unknown');
    }
  };

  return {
    ...networkState,
    hasInternetConnection,
    getConnectionType,
  };
}; 