import React, { useEffect } from 'react';
import { Platform } from 'react-native';

// Safe import with fallback
let SystemBars: any = null;
let StatusBar: any = null;

try {
  // Try to import the new SystemBars
  const EdgeToEdge = require('react-native-edge-to-edge');
  SystemBars = EdgeToEdge.SystemBars;
} catch (error) {
  console.warn('react-native-edge-to-edge not available, falling back to expo-status-bar');
}

try {
  // Fallback to expo-status-bar
  const ExpoStatusBar = require('expo-status-bar');
  StatusBar = ExpoStatusBar.StatusBar;
} catch (error) {
  console.warn('expo-status-bar not available');
}

export interface SafeSystemBarsProps {
  style?: 'auto' | 'inverted' | 'light' | 'dark';
  hidden?: boolean;
  translucent?: boolean;
  backgroundColor?: string;
  navigationBarColor?: string;
}

/**
 * Safe SystemBars component that gracefully falls back to StatusBar
 * This allows gradual migration without breaking existing functionality
 */
export const SafeSystemBars: React.FC<SafeSystemBarsProps> = ({
  style = 'light',
  hidden = false,
  translucent = false,
  backgroundColor = 'transparent',
  navigationBarColor = '#0996a8'
}) => {
  // Use SystemBars if available (edge-to-edge)
  if (SystemBars && Platform.OS === 'android') {
    return (
      <SystemBars
        style={style}
        hidden={hidden ? { statusBar: hidden, navigationBar: false } : false}
      />
    );
  }
  
  // Fallback to StatusBar for safety - ALWAYS use translucent=true for consistency
  if (StatusBar) {
    // Set navigation bar color using expo-navigation-bar
    useEffect(() => {
      if (Platform.OS === 'android' && navigationBarColor) {
        try {
          const NavigationBar = require('expo-navigation-bar');
          NavigationBar.setBackgroundColorAsync(navigationBarColor);
        } catch (error) {
          console.warn('expo-navigation-bar not available:', error);
        }
      }
    }, [navigationBarColor]);

    return (
      <StatusBar
        style={style}
        hidden={hidden}
        translucent={translucent}
        backgroundColor={backgroundColor}
      />
    );
  }
  
  // Ultimate fallback - render nothing
  console.warn('No status bar component available');
  return null;
};

export default SafeSystemBars; 