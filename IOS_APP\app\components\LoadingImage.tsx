import React, { useState } from 'react';
import { View, Image, ActivityIndicator, StyleSheet, ImageProps, ViewStyle, ImageStyle, TextStyle } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';

interface LoadingImageProps extends Omit<ImageProps, 'onLoadStart' | 'onLoadEnd'> {
  placeholderIcon?: keyof typeof FontAwesome.glyphMap;
  placeholderIconSize?: number;
  placeholderIconColor?: string;
  containerStyle?: ViewStyle;
}

export default function LoadingImage({
  style,
  containerStyle,
  placeholderIcon = 'image',
  placeholderIconSize = 24,
  placeholderIconColor = '#0996a8',
  ...props
}: LoadingImageProps) {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <View style={[styles.container, containerStyle]}>
      <Image
        {...props}
        style={[styles.image, style]}
        onLoadStart={() => setIsLoading(true)}
        onLoadEnd={() => setIsLoading(false)}
      />
      {isLoading && (
        <View style={styles.loadingContainer}>
          <FontAwesome 
            name={placeholderIcon} 
            size={placeholderIconSize} 
            color={placeholderIconColor} 
            style={styles.placeholderIcon}
          />
          <ActivityIndicator 
            size="small" 
            color="#0996a8" 
            style={styles.loadingIndicator} 
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
  } as ViewStyle,
  image: {
    width: '100%',
    height: '100%',
  } as ImageStyle,
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  } as ViewStyle,
  loadingIndicator: {
    marginTop: 8,
  } as ViewStyle,
  placeholderIcon: {
    opacity: 0.5,
  } as TextStyle,
}); 