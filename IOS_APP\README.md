# ProROLA iOS App

This is the iOS-only version of the ProROLA wildlife monitoring application.

## 🚀 Quick Setup

### Prerequisites
- Node.js (v16 or later)
- npm or yarn
- Xcode (for iOS development)
- Expo CLI: `npm install -g @expo/cli`

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Get Firebase iOS Configuration:**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Open the `prorola-a2f66` project
   - Go to **Project Settings** → **General** tab
   - Find the iOS app (bundle ID: `com.prorola.app`)
   - Download `GoogleService-Info.plist`
   - Place it in the root directory of this iOS app

3. **Start development:**
   ```bash
   npm start
   ```

## 📱 Building for iOS

### Development Build (Simulator)
```bash
eas build --platform ios --profile development
```

### Preview Build (TestFlight)
```bash
eas build --platform ios --profile preview
```

### Production Build (App Store)
```bash
eas build --platform ios --profile production
```

## ⚠️ Important Notes

1. **Missing GoogleService-Info.plist:** The app will not build without this Firebase configuration file.

2. **Apple Developer Account:** You need an active Apple Developer Program membership ($99/year) to build for physical devices and submit to the App Store.

3. **Update eas.json:** Before submitting to App Store, update the placeholder values in `eas.json`:
   - Replace `<EMAIL>` with your Apple ID
   - Replace `your-app-store-id` with your App Store Connect App ID  
   - Replace `your-team-id` with your Apple Team ID

## 🔧 Local Development

If you have Xcode installed, you can run locally:
```bash
npm run ios
```

## 📦 What's Included

This iOS app includes:
- ✅ All source code (app/, components/, assets/)
- ✅ iOS-specific configuration (app.json, eas.json)
- ✅ Firebase iOS setup (needs GoogleService-Info.plist)
- ✅ Google Maps integration
- ✅ All required permissions for location, camera, photo library
- ❌ No Android-specific files or dependencies
- ❌ No web admin interfaces

## 🎯 Next Steps

1. Get the `GoogleService-Info.plist` file from Firebase Console
2. Run `npm install`
3. Test with `npm start` and iOS Simulator
4. Build for TestFlight/App Store when ready

The app is fully configured and ready to build once you add the Firebase configuration file!
