import React, { useState, useEffect, useCallback } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ActivityIndicator, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { FontAwesome } from '@expo/vector-icons';
import { useAuth } from '@/contexts/AuthContext';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/config/firebase';

// Portuguese translations
const pt = {
  title: 'Verificar Email',
  message: 'Foi enviado um email de verificação para',
  instruction: 'Por favor, verifique a sua caixa de entrada e clique no link de verificação.',
  checkButton: 'Verificar Estado',
  resendButton: 'Reenviar Email',
  backToLogin: 'Voltar para Login',
  verificationSuccess: 'Email verificado com sucesso!',
  verificationFailed: 'Email ainda não verificado. Por favor, verifique a sua caixa de entrada e spam.',
  resendSuccess: 'Email de verificação reenviado com sucesso.',
  nextCheck: 'Próxima verificação em',
  seconds: 'segundos',
  checking: 'A verificar...',
  spam: 'Verifique também a pasta de spam',
  tooManyRequests: 'Muitas tentativas. Por favor, aguarde alguns minutos antes de tentar novamente.',
};

export default function VerifyEmailScreen() {
  const { user, resendVerificationEmail } = useAuth();
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendCountdown, setResendCountdown] = useState(0);



  // Combined effect for initialization
  useEffect(() => {
    let resendTimer: NodeJS.Timeout;

    const initialize = async () => {
      // Check resend cooldown
      const lastEmailSent = await AsyncStorage.getItem('lastVerificationEmailSent');
      if (lastEmailSent) {
        const timePassed = Date.now() - parseInt(lastEmailSent);
        if (timePassed < 30000) {
          setResendCountdown(Math.ceil((30000 - timePassed) / 1000));
        }
      }

      // Initial verification check
      if (user) {
        await user.reload();
        if (user.emailVerified) {
          // Don't show alert here - let AuthContext handle the flow
          // The AuthContext will detect email verification and redirect appropriately
        }
      }
    };

    initialize();

    // Set up resend countdown timer
    if (resendCountdown > 0) {
      resendTimer = setInterval(() => {
        setResendCountdown(prev => Math.max(0, prev - 1));
      }, 1000);
    }

    return () => {
      clearInterval(resendTimer);
    };
  }, [user, resendCountdown]);

  // Redirect if no user
  useEffect(() => {
    if (!user) {
      router.replace('/auth/login');
    }
  }, [user]);

  const handleCheckVerification = async () => {
    if (isLoading) return;
    
    setIsLoading(true);
    try {
      await user?.reload();
      if (user?.emailVerified) {
        // Manually trigger the document creation process since onAuthStateChanged might not fire
        try {
          // Check if document already exists
          const userDocRef = doc(db, 'users', user.uid);
          const userDoc = await getDoc(userDocRef);
          if (userDoc.exists()) {
            // Document already exists
          } else {
            // Get pending registration data
            const pendingRegistration = await AsyncStorage.getItem('pendingUserRegistration');
            
            let userRole = 'colaborador';
            let foundPendingData = false;

            if (pendingRegistration) {
              try {
                const registrationData = JSON.parse(pendingRegistration);
                if (registrationData.uid === user.uid) {
                  userRole = registrationData.role;
                  foundPendingData = true;
                }
              } catch (error) {
                console.error('Error parsing pending registration:', error);
              }
            }

            // Create the user document
            const userData = {
              email: user.email,
              name: user.email?.split('@')[0] || 'Utilizador',
              role: userRole,
              verified: userRole !== 'tecnico_prorola', // Only technicians need approval
              created_at: serverTimestamp(),
              updated_at: serverTimestamp(),
              uid: user.uid,
              auto_created: !foundPendingData,
              auto_created_at: serverTimestamp()
            };

            await setDoc(userDocRef, userData);

            // Clear pending registration
            if (foundPendingData) {
              await AsyncStorage.removeItem('pendingUserRegistration');
            }
          }

          // Now check if this is a technician that needs approval
          const finalUserDoc = await getDoc(userDocRef);
          if (finalUserDoc.exists()) {
            const userData = finalUserDoc.data();
            
            if (userData.role === 'tecnico_prorola' && !userData.verified) {
              showAlert({
                type: 'info',
                message: 'Email verificado com sucesso! A sua conta de técnico está pendente de aprovação. Será redirecionado para o login.',
              });
              
              // Store message for login screen
              await AsyncStorage.setItem('pendingApprovalMessage', 'A sua conta de técnico ainda não foi aprovada por um responsável ProROLA. Por favor, aguarde a aprovação antes de tentar fazer login.');
              
              // Redirect to login after a short delay
              setTimeout(() => {
                router.replace('/auth/login');
              }, 3000);
              return;
            }
          }
          
          // For non-technicians or approved technicians, normal flow
          showAlert({
            type: 'success',
            message: 'Email verificado com sucesso!',
            onConfirm: () => {
              // Redirect to login after user clicks OK
              router.replace('/auth/login');
            }
          });
          
        } catch (error) {
          console.error('Error during manual document creation:', error);
          showAlert({
            type: 'error',
            message: 'Erro ao processar verificação. Por favor, tente fazer login.',
          });
        }
      } else {
        showAlert({
          type: 'error',
          message: pt.verificationFailed,
        });
      }
    } catch (error) {
      console.error('Error checking verification:', error);
      showAlert({
        type: 'error',
        message: 'Erro ao verificar o estado do email. Tente novamente.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendEmail = async () => {
    if (resendCountdown > 0) {
      showAlert({
        type: 'error',
        message: `Por favor, aguarde ${resendCountdown} segundos antes de reenviar o email.`
      });
      return;
    }

    setIsResending(true);
    try {
      // Clear any existing lastVerificationEmailSent
      await AsyncStorage.removeItem('lastVerificationEmailSent');
      
      await resendVerificationEmail();
      
      showAlert({
        type: 'success',
        message: pt.resendSuccess,
      });
      setResendCountdown(30);
      
      // Store the new timestamp
      await AsyncStorage.setItem('lastVerificationEmailSent', Date.now().toString());
    } catch (error: any) {
      if (error.code === 'auth/too-many-requests') {
        // If we hit the rate limit, set a longer cooldown
        setResendCountdown(300); // 5 minutes
      }
      showAlert({
        type: 'error',
        message: error.message
      });
    } finally {
      setIsResending(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.mainContainer}>
        <View style={styles.header}>
          <SafeAreaView edges={['top']} style={styles.headerContent}>
            <Image 
              source={require('../../assets/images/header-logo.png')}
              style={styles.logo}
            />
          </SafeAreaView>
        </View>

        <View style={styles.content}>
          <View style={styles.titleContainer}>
            <FontAwesome name="envelope-o" size={24} color="#0996a8" style={styles.titleIcon} />
            <Text style={styles.title}>{pt.title}</Text>
          </View>
          
          <View style={styles.messageCard}>
            <Text style={styles.message}>
              Foi enviado um email de verificação para
            </Text>
            <Text style={styles.emailText}>{user?.email}</Text>
            <Text style={styles.instruction}>{pt.instruction}</Text>
            <Text style={styles.spamNote}>{pt.spam}</Text>
          </View>

          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              style={[styles.button, styles.checkButton]}
              onPress={handleCheckVerification}
              disabled={isLoading}>
              {isLoading ? (
                <>
                  <ActivityIndicator color="#FFFFFF" size="small" />
                  <Text style={[styles.buttonText, styles.loadingText]}>{pt.checking}</Text>
                </>
              ) : (
                <>
                  <FontAwesome name="refresh" size={18} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.buttonText}>{pt.checkButton}</Text>
                </>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.button, 
                styles.resendButton, 
                (isResending || resendCountdown > 0) && styles.disabledButton
              ]}
              onPress={handleResendEmail}
              disabled={isResending || resendCountdown > 0}>
              {isResending ? (
                <ActivityIndicator color="#FFFFFF" size="small" />
              ) : (
                <>
                  <FontAwesome name="paper-plane" size={16} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.buttonText}>
                    {resendCountdown > 0 ? `Aguarde ${resendCountdown}s` : pt.resendButton}
                  </Text>
                </>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.backButton]}
              onPress={async () => {
                // Clean up pending registration if user goes back without verifying
                await AsyncStorage.removeItem('pendingUserRegistration');
                router.replace('/auth/login');
              }}>
              <FontAwesome name="arrow-left" size={16} color="#6B7280" style={styles.buttonIcon} />
              <Text style={[styles.buttonText, styles.backButtonText]}>{pt.backToLogin}</Text>
            </TouchableOpacity>
          </View>
        </View>

        <SafeAreaView edges={['bottom']} style={styles.footer}>
          <Text style={styles.copyrightText}>
            © {new Date().getFullYear()} Todos os direitos reservados ICNF
          </Text>
        </SafeAreaView>
      </View>

      <CustomAlert
        key={`verify-email-${config.message || 'default'}`}
        visible={isVisible}
        type={config.type}
        message={config.message}
        title={config.title}
        onClose={hideAlert}
        onConfirm={config.onConfirm}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#0996a8',
    height: 80,
    zIndex: 2,
    elevation: 2,
  },
  headerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 20,
  },
  logo: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
    top: 30,
    zIndex: 2,
  },
  content: {
    flex: 1,
    padding: 24,
    alignItems: 'center',
    marginTop: 60,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 32,
  },
  titleIcon: {
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '500',
    color: '#374151',
  },
  messageCard: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  message: {
    fontSize: 15,
    color: '#6b7280',
    marginBottom: 8,
    textAlign: 'center',
    lineHeight: 22,
  },
  emailText: {
    color: '#0996a8',
    fontWeight: '600',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 12,
  },
  instruction: {
    fontSize: 14,
    color: '#9ca3af',
    marginBottom: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  spamNote: {
    fontSize: 12,
    color: '#9ca3af',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  buttonsContainer: {
    width: '100%',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    height: 52,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  checkButton: {
    backgroundColor: '#0996a8',
  },
  resendButton: {
    backgroundColor: '#10b981',
  },
  backButton: {
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  disabledButton: {
    opacity: 0.6,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  backButtonText: {
    color: '#6b7280',
  },
  loadingText: {
    marginLeft: 8,
  },
  footer: {
    backgroundColor: '#0996a8',
    padding: 16,
    width: '100%',
  },
  copyrightText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.8,
  },
}); 