import { useState, useCallback, useEffect, useRef } from 'react';

interface AlertConfig {
  title: string;
  message?: string;
  buttons?: {
    text: string;
    type?: 'default' | 'cancel' | 'destructive';
    onPress: () => void;
  }[];
  type?: 'success' | 'error' | 'warning' | 'info';
  onConfirm?: () => void;
}

export function useCustomAlert() {
  const [isVisible, setIsVisible] = useState(false);
  const [config, setConfig] = useState<AlertConfig>({
    title: '',
    type: 'info',
  });
  const alertTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clear timeout on unmount
  useEffect(() => {
    return () => {
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }
    };
  }, []);

  const showAlert = useCallback((newConfig: AlertConfig) => {
    console.log('🔄 ALERT_DEBUG: showAlert called with config:', {
      title: newConfig.title,
      type: newConfig.type,
      hasOnConfirm: !!newConfig.onConfirm,
      hasMessage: !!newConfig.message
    });
    
    // Clear any existing timeout
    if (alertTimeoutRef.current) {
      clearTimeout(alertTimeoutRef.current);
    }
    
    setConfig(newConfig);
    setIsVisible(true);
    
    console.log('🔄 ALERT_DEBUG: Alert state set - isVisible: true');
    
    // Auto-hide success alerts after 3 seconds to prevent lingering
    if (newConfig.type === 'success') {
      alertTimeoutRef.current = setTimeout(() => {
        setIsVisible(false);
      }, 3000);
    }
  }, []);

  const hideAlert = useCallback(() => {
    if (alertTimeoutRef.current) {
      clearTimeout(alertTimeoutRef.current);
    }
    setIsVisible(false);
  }, []);

  return {
    isVisible,
    config,
    showAlert,
    hideAlert,
  };
} 