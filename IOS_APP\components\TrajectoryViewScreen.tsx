import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Alert,
  Dimensions,
  Image,
  Modal,
  BackHandler,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { getFirestore, doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import TrajectoryMapView from './TrajectoryMapView';

const { width } = Dimensions.get('window');

interface Contact {
  id: string;
  lat: number;
  lng: number;
  type: string;
  circumstances: string;
  location: string;
  timestamp?: string;
  originalData?: any;
}

interface TrajectoryData {
  id: string;
  name: string;
  distance: string;
  trajetoDate?: string;
  createdAt: any;
  updatedAt: any;
  startingAddress?: string;
  startTime?: string;
  middleTime?: string;
  endTime?: string;
  weatherCondition?: string;
  pointsCount?: number;
  numberOfObservers?: number;
  coordinates?: any[];
  mapPhotos?: any[];
  otherPhotos?: any[];
  contacts?: Contact[];
}

interface TrajectoryViewScreenProps {
  trajectoryId: string;
  onClose: () => void;
}

const TrajectoryViewScreen: React.FC<TrajectoryViewScreenProps> = ({ trajectoryId, onClose }) => {
  const [trajectory, setTrajectory] = useState<TrajectoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isMapFullscreen, setIsMapFullscreen] = useState(false);

  useEffect(() => {
    fetchTrajectoryData();
  }, [trajectoryId]);

  // Handle Android back button
  useEffect(() => {
    const backAction = () => {
      onClose();
      return true; // Prevent default behavior
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove();
  }, [onClose]);

  const fetchTrajectoryData = async () => {
    try {
      console.log('🔄 Fetching trajectory data for ID:', trajectoryId);
      const db = getFirestore();
      
      // Fetch trajectory document
      const docRef = doc(db, 'zonas', trajectoryId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = { id: docSnap.id, ...docSnap.data() } as TrajectoryData;
        console.log('✅ Trajectory data loaded:', data);
        console.log('📍 Route coordinates:', data.coordinates);
        console.log('📊 Route points count:', data.pointsCount);
        
        // Fetch contacts for this trajectory
        console.log('🔄 Fetching contacts for trajectory ID:', trajectoryId);
        const contactsQuery = query(
          collection(db, 'contacts'),
          where('trajectoryId', '==', trajectoryId)
        );
        
        const contactsSnapshot = await getDocs(contactsQuery);
        const contactsData: Contact[] = [];
        
        contactsSnapshot.forEach((doc) => {
          const rawContactData = doc.data();
          
          // Transform contact data to match TrajectoryMapView interface
          const contactData = {
            id: doc.id,
            lat: rawContactData.coordinates?.lat || 0,
            lng: rawContactData.coordinates?.lng || 0,
            type: 'contact', // Default type
            circumstances: rawContactData.circumstance || '', // Note: "circumstance" -> "circumstances"
            location: rawContactData.location || '',
            timestamp: rawContactData.createdAt || rawContactData.time || '',
            // Keep original data for reference
            originalData: rawContactData
          };
          
          contactsData.push(contactData);
        });
        
        console.log('✅ Contacts loaded:', contactsData.length);
        console.log('📋 Transformed contacts data:', contactsData);
        
        // Add contacts to trajectory data
        data.contacts = contactsData;
        
        setTrajectory(data);
      } else {
        console.error('❌ Trajectory document not found');
        setError('Trajeto não encontrado');
      }
    } catch (err) {
      console.error('💥 Error fetching trajectory:', err);
      setError('Erro ao carregar trajeto');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '—';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('pt-PT');
  };

  const getDisplayDate = (trajectory: TrajectoryData) => {
    if (trajectory.trajetoDate) {
      return trajectory.trajetoDate;
    }
    return formatDate(trajectory.createdAt);
  };

  const getCleanTrajectoryName = (name: string) => {
    // Remove "Trajeto" from beginning and time portion from end
    let cleanName = name.replace(/^Trajeto\s+/, ''); // Remove "Trajeto " from start
    cleanName = cleanName.replace(/ - \d{2}\/\d{2}\/\d{4} \d{2}:\d{2}$/, ''); // Remove time from end
    return cleanName;
  };

  const getWeatherIcon = (condition?: string) => {
    if (!condition) return '🌤️';
    const weatherIcons: { [key: string]: string } = {
      'clear': '☀️',
      'sunny': '☀️',
      'cloudy': '☁️',
      'overcast': '☁️',
      'rainy': '🌧️',
      'stormy': '⛈️',
      'foggy': '🌫️',
      'windy': '💨',
    };
    return weatherIcons[condition.toLowerCase()] || '🌤️';
  };

  const getWeatherText = (condition?: string) => {
    if (!condition) return 'Não definido';
    const weatherTexts: { [key: string]: string } = {
      'clear': 'Céu Limpo',
      'sunny': 'Ensolarado',
      'cloudy': 'Nublado',
      'overcast': 'Encoberto',
      'rainy': 'Chuvoso',
      'stormy': 'Tempestade',
      'foggy': 'Nevoeiro',
      'windy': 'Ventoso',
    };
    return weatherTexts[condition.toLowerCase()] || condition;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0996a8" />
        <Text style={styles.loadingText}>A carregar trajeto...</Text>
      </View>
    );
  }

  if (error || !trajectory) {
    return (
      <View style={styles.errorContainer}>
        <FontAwesome name="exclamation-triangle" size={50} color="#FF3B30" />
        <Text style={styles.errorText}>{error || 'Trajeto não encontrado'}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={onClose}>
          <Text style={styles.retryButtonText}>Voltar</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const totalPhotos = (trajectory.mapPhotos?.length || 0) + (trajectory.otherPhotos?.length || 0);

  return (
    <>
      <StatusBar style="dark" />
      <ScrollView 
        style={styles.container}
        showsVerticalScrollIndicator={false}
      >
      {/* Back Button - positioned like zones screen */}
      <View style={styles.backButtonContainer}>
        <TouchableOpacity style={styles.backButton} onPress={onClose}>
          <FontAwesome name="arrow-left" size={20} color="#0996a8" />
          <Text style={styles.backButtonText}>Voltar</Text>
        </TouchableOpacity>
      </View>

      {/* Content Cards - same structure as zones */}
      <View style={styles.cardsContainer}>
        {/* Trajectory Name Card */}
        <View style={styles.trajectoryNameCard}>
          <Text style={styles.trajectoryName}>{getCleanTrajectoryName(trajectory.name)}</Text>
          <View style={styles.trajectoryLabel}>
            <Text style={styles.trajectoryLabelText}>TRAJETO MANUAL</Text>
          </View>
        </View>

        {/* Basic Info Card */}
        <View style={styles.infoCard}>
          <View style={styles.cardHeader}>
            <FontAwesome name="info-circle" size={16} color="#0996a8" />
            <Text style={styles.cardTitle}>Informações</Text>
          </View>
          <View style={styles.cardContent}>
            <View style={styles.infoRowCompact}>
              <View style={styles.infoItemCompact}>
                <FontAwesome name="road" size={14} color="#0996a8" />
                <Text style={styles.infoText}>{trajectory.distance || '—'}</Text>
              </View>
              <View style={styles.infoItemCompact}>
                <FontAwesome name="calendar" size={14} color="#0996a8" />
                <Text style={styles.infoText}>{getDisplayDate(trajectory)}</Text>
              </View>
            </View>
            {trajectory.updatedAt && trajectory.updatedAt !== trajectory.createdAt && (
              <View style={styles.infoRowSingle}>
                <FontAwesome name="edit" size={14} color="#0996a8" />
                <Text style={styles.infoText}>Editado: {formatDate(trajectory.updatedAt)}</Text>
              </View>
            )}
            <View style={styles.infoRowSingle}>
              <FontAwesome name="map-marker" size={14} color="#0996a8" />
              <Text style={styles.infoText}>{trajectory.startingAddress || 'Não definida'}</Text>
            </View>
          </View>
        </View>

        {/* Timeline Card */}
        <View style={styles.infoCard}>
          <View style={styles.cardHeader}>
            <FontAwesome name="history" size={16} color="#0996a8" />
            <Text style={styles.cardTitle}>Timeline</Text>
          </View>
          <View style={styles.cardContent}>
            <View style={styles.timelineContainer}>
              <View style={styles.timelineRowCompact}>
                <View style={styles.timelineItemCompact}>
                  <View style={[styles.timelineDot, styles.timelineDotStart]} />
                  <Text style={styles.timelineLabel}>INÍCIO</Text>
                  <Text style={styles.timelineTime}>{trajectory.startTime || '--:--'}</Text>
                </View>
                <View style={styles.timelineItemCompact}>
                  <View style={[styles.timelineDot, styles.timelineDotEnd]} />
                  <Text style={styles.timelineLabel}>FIM</Text>
                  <Text style={styles.timelineTime}>{trajectory.endTime || '--:--'}</Text>
                </View>
              </View>
              {trajectory.middleTime && (
                <View style={styles.timelineItemSingle}>
                  <View style={[styles.timelineDot, styles.timelineDotMiddle]} />
                  <Text style={styles.timelineLabel}>MEIO</Text>
                  <Text style={styles.timelineTime}>{trajectory.middleTime}</Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* Weather Card - only show if weather data exists */}
        {trajectory.weatherCondition && (
          <View style={styles.infoCard}>
            <View style={styles.cardHeader}>
              <FontAwesome name="cloud" size={16} color="#0996a8" />
              <Text style={styles.cardTitle}>Condições</Text>
            </View>
            <View style={styles.cardContent}>
              <View style={styles.weatherContainer}>
                <Text style={styles.weatherIcon}>{getWeatherIcon(trajectory.weatherCondition)}</Text>
                <Text style={styles.weatherText}>{getWeatherText(trajectory.weatherCondition)}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Statistics Card */}
        <View style={styles.infoCard}>
          <View style={styles.cardHeader}>
            <FontAwesome name="bar-chart" size={16} color="#0996a8" />
            <Text style={styles.cardTitle}>Estatísticas</Text>
          </View>
          <View style={styles.cardContent}>
            <View style={styles.statsGrid}>
                               <View style={styles.statItem}>
                   <View style={styles.statLabel}>
                     <FontAwesome name="map-pin" size={12} color="#0996a8" />
                     <Text style={styles.statLabelText}>Pontos</Text>
                   </View>
                   <Text style={styles.statValue}>{trajectory.pointsCount || 0}</Text>
                 </View>
                 <View style={styles.statItem}>
                   <View style={styles.statLabel}>
                     <FontAwesome name="eye" size={12} color="#0996a8" />
                     <Text style={styles.statLabelText}>Observadores</Text>
                   </View>
                   <Text style={styles.statValue}>{trajectory.numberOfObservers || 0}</Text>
                 </View>
                 <View style={styles.statItem}>
                   <View style={styles.statLabel}>
                     <FontAwesome name="circle" size={12} color="#0996a8" />
                     <Text style={styles.statLabelText}>Contactos</Text>
                   </View>
                   <Text style={styles.statValue}>{trajectory.contacts?.length || 0}</Text>
                 </View>
            </View>
          </View>
        </View>

        {/* Photos Card - only show if photos exist */}
        {totalPhotos > 0 && (
          <View style={styles.infoCard}>
            <View style={styles.cardHeader}>
              <FontAwesome name="image" size={16} color="#0996a8" />
              <Text style={styles.cardTitle}>Fotografias ({totalPhotos})</Text>
            </View>
            <View style={styles.cardContent}>
              <Text style={styles.photoPlaceholder}>
                {trajectory.mapPhotos?.length || 0} fotos do mapa, {trajectory.otherPhotos?.length || 0} outras fotos
              </Text>
              {/* TODO: Implement photo gallery */}
            </View>
          </View>
        )}

        {/* Map Card */}
        <View style={styles.mapCard}>
          <View style={styles.cardHeader}>
            <FontAwesome name="map" size={16} color="#0996a8" />
            <Text style={styles.cardTitle}>Mapa do Trajeto</Text>
            <View style={styles.mapStats}>
              <FontAwesome name="map-pin" size={12} color="#0996a8" />
              <Text style={styles.mapStatsText}>{trajectory.pointsCount || 0} pontos</Text>
            </View>
          </View>
          <View style={styles.mapContainer}>
            {trajectory.coordinates && trajectory.coordinates.length > 0 ? (
              <TrajectoryMapView
                coordinates={trajectory.coordinates}
                contacts={trajectory.contacts || []}
                onMapReady={() => console.log('✅ Map ready')}
                onFullscreenToggle={(fullscreen) => setIsMapFullscreen(fullscreen)}
              />
            ) : (
              <View style={styles.mapPlaceholder}>
                <FontAwesome name="map" size={40} color="#94a3b8" />
                <Text style={styles.mapPlaceholderText}>
                  Nenhum trajeto encontrado para exibir
                </Text>
                <Text style={styles.mapPlaceholderText}>
                  {trajectory.pointsCount || 0} pontos registados
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </ScrollView>

    {/* Fullscreen Map Modal */}
    <Modal
      visible={isMapFullscreen}
      animationType="slide"
      presentationStyle="fullScreen"
      statusBarTranslucent={true}
    >
      <View style={styles.fullscreenContainer}>
        {trajectory?.coordinates && trajectory.coordinates.length > 0 && (
          <TrajectoryMapView
            coordinates={trajectory.coordinates}
            contacts={trajectory.contacts || []}
            onMapReady={() => console.log('✅ Fullscreen map ready')}
            onFullscreenToggle={(fullscreen) => setIsMapFullscreen(fullscreen)}
          />
        )}
      </View>
    </Modal>
  </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    paddingTop: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748b',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: '#0996a8',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  backButtonContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: '#0996a8',
    fontWeight: '500',
  },
  cardsContainer: {
    padding: 16,
    paddingTop: 0,
  },
  trajectoryNameCard: {
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#0996a8',
  },
  trajectoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    textAlign: 'center',
    marginBottom: 8,
  },
  trajectoryLabel: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  trajectoryLabelText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  trajectoryDate: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  infoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    gap: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
  },
  cardContent: {
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
  },
  infoRowCompact: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    marginBottom: 8,
  },
  infoItemCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
  },
  infoRowSingle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
    flex: 1,
  },
  timelineContainer: {
    gap: 8,
  },
  timelineRowCompact: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    marginBottom: 8,
  },
  timelineItemCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
  },
  timelineItemSingle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: 'transparent',
  },
  timelineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  timelineDotStart: {
    backgroundColor: '#22c55e',
  },
  timelineDotMiddle: {
    backgroundColor: '#f59e0b',
  },
  timelineDotEnd: {
    backgroundColor: '#ef4444',
  },
  timelineContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timelineLabel: {
    fontSize: 10,
    color: '#6b7280',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  timelineTime: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '600',
  },
  weatherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    paddingVertical: 12,
  },
  weatherIcon: {
    fontSize: 32,
  },
  weatherText: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '600',
  },
  statsGrid: {
    gap: 8,
  },
  statItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
  },
  statLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statLabelText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  statValue: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '600',
  },
  photoPlaceholder: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    padding: 16,
  },
  mapCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  mapStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginLeft: 'auto',
  },
  mapStatsText: {
    fontSize: 12,
    color: '#6b7280',
  },
  mapContainer: {
    height: 300,
    marginTop: 12,
    borderRadius: 8,
    overflow: 'hidden',
  },
  mapPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    backgroundColor: '#f8fafc',
    borderRadius: 8,
    marginTop: 12,
  },
  mapPlaceholderText: {
    fontSize: 14,
    color: '#94a3b8',
    marginTop: 8,
    textAlign: 'center',
  },
  fullscreenContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
});

export default TrajectoryViewScreen; 