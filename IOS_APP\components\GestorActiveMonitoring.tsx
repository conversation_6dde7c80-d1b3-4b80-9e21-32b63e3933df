import React, { useState, useEffect, useRef, useCallback } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Alert, ActivityIndicator, Platform, Animated, Modal, Dimensions, Image, BackHandler, AppState, AppStateStatus, ScrollView } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import SafeSystemBars from '@/components/SafeSystemBars';
import * as Location from 'expo-location';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@/contexts/AuthContext';
import { getFirestore, collection, addDoc, serverTimestamp, query, where, orderBy, getDocs, Timestamp, deleteDoc, doc, onSnapshot, DocumentData, updateDoc, getDoc, writeBatch } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '@/config/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import MapView, { Marker, MapMarker, PROVIDER_GOOGLE, Callout, Polyline, Region, Camera } from 'react-native-maps';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import { PendingReport } from '@/types/reports';
import { TecnicoProtocol } from '@/types/reports';
import NetworkStatusIndicator from '@/components/NetworkStatusIndicator';
import { useFocusEffect } from '@react-navigation/native';
import { useNetwork } from '@/contexts/NetworkContext';
import Svg, { Path } from 'react-native-svg';
import ContactPlacementModal from '@/components/ContactPlacementModal';
import ContactDetailsModal from '@/components/ContactDetailsModal';
import { monitoringSyncService } from '@/services/monitoringSyncService';
import { useRouter } from 'expo-router';
import KeepAwake from 'react-native-keep-awake';
import { 
  calculateDistance, 
  calculateSpeed, 
  shouldRecordGPSPoint, 
  shouldUpdateBearing, 
  createEnhancedGPSPoint,
  calculateAccuracyGate,
  DEFAULT_FILTER_CONFIG 
} from '@/utils/locationFiltering';
import LoadingImage from '@/components/LoadingImage';

// Portuguese translations
const pt = {
  monitoringActive: 'Monitorização Ativa',
  terminate: 'Terminar',
  confirmTerminate: 'Deseja terminar a criação do trajeto?',
  yes: 'Sim',
  no: 'Não',
  timeElapsed: 'Duração:',
  distanceTraveled: 'DISTÂNCIA:',
  contactsCount: 'Contactos:',
  protocol: 'Protocolo',
  locationError: 'Erro ao obter localização',
  permissionDenied: 'Permissão de localização negada',
  gpsUnavailable: 'GPS indisponível',
  retryLocation: 'Tentar Novamente',
  // Distance validation messages
  insufficientDistance: 'Distância Insuficiente',
  minimumDistanceRequired: 'O trajeto deve ter obrigatoriamente pelo menos 2.5 km para ser enviado.',
  currentDistance: 'Distância atual:',
  missingDistance: 'Faltam ainda:',
  continueWalking: 'Continue a caminhar para completar o trajeto.',
  understood: 'Compreendido',
  endTrajectoryCreation: 'Terminar Criação do Trajeto',
  endTrajectoryQuestion: 'Deseja terminar a criação do trajeto mesmo sem atingir os 2.5 km mínimos?',
  trajectoryNotSent: 'O trajeto NÃO será enviado devido à distância insuficiente.',
  continueMonitoring: 'Continuar Monitorização',
  endAnyway: 'Terminar Mesmo Assim',
  chooseEndOrContinue: 'Prima "SIM" para terminar sem guardar ou "NÃO" para continuar a criação do trajeto.',
  trajectoryCancelled: 'Trajeto Cancelado',
  trajectoryWillBeCancelled: 'O trajeto será cancelado e NÃO será guardado.',
  confirmCancellation: 'Tem a certeza que deseja cancelar?',
  protocolOptions: {
    trajeto: 'Trajeto',
    estacoes_escuta: 'Estações de escuta',
    metodo_mapas: 'Método dos mapas',
    contagens_pontos: 'Contagens em pontos específicos',
    captura_marcacao: 'Captura e marcação de indivíduos',
    acompanhamento_cacadas: 'Acompanhamento das caçadas',
    registos_ocasionais: 'Registos ocasionais',
  },
};

type LocationType = Location.LocationObject | null;

// Default region (Portugal center) - very close zoom for walking
const DEFAULT_REGION = {
  latitude: 39.5,
  longitude: -8.0,
  latitudeDelta: 0.001, // Very close zoom for precise monitoring
  longitudeDelta: 0.001,
};

interface GestorActiveMonitoringProps {
  protocol: TecnicoProtocol;
  startTime: Date;
  weatherData?: any;
  observersCount?: number;
  zoneId: string;
  zoneName: string;
  trajectoryToRedoId?: string | null;
  onTerminate: () => void;
  initialContactMarkers?: Array<{
    id: string;
    coordinate: { latitude: number; longitude: number };
    observerLocation: { latitude: number; longitude: number };
    contactNumber: number;
    timestamp: string;
  }>;
  initialContactsCount?: number;
  isReportMode?: boolean; // Flag to indicate if this is for report creation
}

// Import the dove icon statically
const doveIconPng = require('../assets/images/icons/dove-icon-3x.png');

// Smart Dove Icon Component - FontAwesome6 when online, SVG dove when offline
const DoveIcon = ({ size = 20, color = "#FFFFFF", style }: { size?: number; color?: string; style?: any }) => {
  const { hasInternetConnection } = useNetwork();
  const isOnline = hasInternetConnection();
  const [svgError, setSvgError] = useState(false);
  
  if (isOnline) {
    // Use FontAwesome6 dove when online
    return <FontAwesome6 name="dove" size={size} color={color} style={style} />;
  } else {
    // Use exact FontAwesome SVG dove when offline
    
    if (svgError) {
      // Fallback to FontAwesome if SVG fails
      return <FontAwesome name="send" size={size} color={color} style={style} />;
    }
    
    try {
      // Scale up the SVG to match FontAwesome6 size
      const svgSize = size * 1.2; // Increase by 20% to match FontAwesome6
      
      return (
        <View style={[
          { 
            width: size, 
            height: size, 
            justifyContent: 'center', 
            alignItems: 'center',
          }, 
          style
        ]}>
          <Svg
            width={svgSize}
            height={svgSize}
            viewBox="0 0 576 512"
            style={{ 
              width: svgSize, 
              height: svgSize,
            }}
          >
            <Path 
              d="M160.8 96.5c14 17 31 30.9 49.5 42.2c25.9 15.8 53.7 25.9 77.7 31.6l0-31.5C265.8 108.5 250 71.5 248.6 28c-.4-11.3-7.5-21.5-18.4-24.4c-7.6-2-15.8-.2-21 5.8c-13.3 15.4-32.7 44.6-48.4 87.2zM320 144l0 30.6s0 0 0 0l0 1.3s0 0 0 0l0 32.1c-60.8-5.1-185-43.8-219.3-157.2C97.4 40 87.9 32 76.6 32c-7.9 0-15.3 3.9-18.8 11C46.8 65.9 32 112.1 32 176c0 116.9 80.1 180.5 118.4 202.8L11.8 416.6C6.7 418 2.6 421.8 .9 426.8s-.8 10.6 2.3 14.8C21.7 466.2 77.3 512 160 512c3.6 0 7.2-1.2 10-3.5L245.6 448l74.4 0c88.4 0 160-71.6 160-160l0-160 29.9-44.9c1.3-2 2.1-4.4 2.1-6.8c0-6.8-5.5-12.3-12.3-12.3L400 64c-44.2 0-80 35.8-80 80zm80-16a16 16 0 1 1 0 32 16 16 0 1 1 0-32z" 
              fill={color}
              stroke={color}
              strokeWidth="0"
            />
          </Svg>
        </View>
      );
    } catch (error) {
      console.error('🚨 SVG dove icon error:', error);
      setSvgError(true);
      return <FontAwesome name="send" size={size} color={color} style={style} />;
    }
  }
};

export default function GestorActiveMonitoring({ protocol, startTime, weatherData, observersCount, zoneId, zoneName, trajectoryToRedoId, onTerminate, initialContactMarkers, initialContactsCount, isReportMode }: GestorActiveMonitoringProps) {
  const colorScheme = useColorScheme();
  const { user, userRole } = useAuth();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const mapRef = useRef<MapView>(null);
  const markerRef = useRef<MapMarker>(null);
  const [location, setLocation] = useState<LocationType>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [mapReady, setMapReady] = useState(false);
  const [showMap, setShowMap] = useState(false);
  const [mapFullyLoaded, setMapFullyLoaded] = useState(false);
  const [hasInitialAnimation, setHasInitialAnimation] = useState(false);
  const [currentRegion, setCurrentRegion] = useState({
    latitudeDelta: 0.0005, // Maximum zoom for precise contact placement
    longitudeDelta: 0.0005
  });
  const { showAlert, isVisible, config, hideAlert: originalHideAlert } = useCustomAlert();
  
  // Wrap hideAlert to add logging
  const hideAlert = () => {
    console.log('🔄 DISTANCE_VALIDATION: hideAlert called - user might have clicked Cancelar');
    originalHideAlert();
  };
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const [mapType, setMapType] = useState<'standard' | 'satellite'>('standard');
  const [currentZoomLevel, setCurrentZoomLevel] = useState<number>(20);
  const [is3DView, setIs3DView] = useState(false); // Start with 2D view
  const [compassTracking, setCompassTracking] = useState(false); // Compass tracking mode
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Time counter state
  const [elapsedTime, setElapsedTime] = useState(0);
  const [showTerminateModal, setShowTerminateModal] = useState(false);
  const [finalElapsedTime, setFinalElapsedTime] = useState(0);
  const [sessionEndTime, setSessionEndTime] = useState<Date | null>(null);

  // Add missing state declarations
  const [showSummaryModal, setShowSummaryModal] = useState(false);
  const [isSendingData, setIsSendingData] = useState(false);

  // Distance tracking state
  const [totalDistance, setTotalDistance] = useState(0);
  const [currentSpeed, setCurrentSpeed] = useState(0); // Current speed in m/s // in meters
  const [pathCoordinates, setPathCoordinates] = useState<{latitude: number, longitude: number}[]>([]);

  // Contacts tracking state
  const [contactsCount, setContactsCount] = useState(initialContactsCount || 0);

  // Contact markers state
  const [contactMarkers, setContactMarkers] = useState<Array<{
    id: string;
    coordinate: { latitude: number; longitude: number };
    observerLocation: { latitude: number; longitude: number };
    contactNumber: number;
    timestamp: string;
  }>>(initialContactMarkers || []);

  // Contact placement modal state
  const [showContactModal, setShowContactModal] = useState(false);
  const [currentContactData, setCurrentContactData] = useState<{
    observerLocation: {latitude: number, longitude: number};
    contactLocation: {latitude: number, longitude: number};
    distance: number;
    bearing: number;
  } | null>(null);
  
  // Contact details modal state
  const [showContactDetailsModal, setShowContactDetailsModal] = useState(false);

  // Contact upload progress
  const [isUploadingContactImages, setIsUploadingContactImages] = useState(false);
  const [contactUploadProgress, setContactUploadProgress] = useState(0);
  const [uploadMessage, setUploadMessage] = useState('');

  // Emergency force-close function
  const forceCloseAllModals = useCallback(() => {
    console.log('🚨 EMERGENCY: Force closing all modals');
    setIsUploadingContactImages(false);
  }, []);

  // Emergency reset on component mount to clear any stuck state
  useEffect(() => {
    console.log('🚨 Component mounted - clearing any stuck modal state');
    forceCloseAllModals();
  }, [forceCloseAllModals]);

  // Contact images state for summary modal
  const [contactImages, setContactImages] = useState<string[]>([]);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Offline monitoring data storage
  const [monitoringSessionId] = useState(() => `gestor_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  // Network monitoring effect - sync data when internet becomes available
  useEffect(() => {
    // Set up progress callback for image sync
    monitoringSyncService.setProgressCallback((progress: number, message: string) => {
      if (progress > 0) {
        setIsUploadingContactImages(true);
        setContactUploadProgress(progress);
        setUploadMessage(message);
      } else {
        setIsUploadingContactImages(false);
        setContactUploadProgress(0);
        setUploadMessage('');
      }
    });

    const unsubscribe = NetInfo.addEventListener(async (state) => {
      // Log network changes but don't auto-sync during active monitoring to prevent stuck modal
      if (state.isConnected && state.isInternetReachable) {
        console.log('🌐 Internet connection restored - sync will happen after monitoring ends');
      }
    });

    return () => {
      unsubscribe();
      monitoringSyncService.setProgressCallback(null);
    };
  }, []);

  // Load persisted monitoring data on component mount
  useEffect(() => {
    const loadMonitoringData = async () => {
      try {
        // Try to load regular monitoring data first
        const savedData = await AsyncStorage.getItem('monitoringData');
        if (savedData) {
          const data = JSON.parse(savedData);
          
          setTotalDistance(data.totalDistance || 0);
          setContactsCount(data.contactsCount || 0);
          setPathCoordinates(data.pathCoordinates || []);
        }

        // Check for backup data as fallback (in case of app crash recovery)
        const backupData = await AsyncStorage.getItem('gestorMonitoringBackup');
        if (backupData) {
          const backup = JSON.parse(backupData);
          const backupTime = new Date(backup.lastBackup);
          const now = new Date();
          const timeDiff = now.getTime() - backupTime.getTime();
          
          // If backup is recent (less than 5 minutes old) and has more data, use it
          if (timeDiff < 300000 && (
            backup.totalDistance > (savedData ? JSON.parse(savedData).totalDistance || 0 : 0) ||
            backup.contactsCount > (savedData ? JSON.parse(savedData).contactsCount || 0 : 0)
          )) {
            console.log('🔄 Recovering from backup data due to potential crash');
            setTotalDistance(backup.totalDistance || 0);
            setContactsCount(backup.contactsCount || 0);
            setPathCoordinates(backup.pathCoordinates || []);
          }
        }

        // Load existing contact events for this session
        await loadContactMarkers();
      } catch (error) {
        console.error('Error loading monitoring data:', error);
      }
    };

    loadMonitoringData();
  }, []);

  // Load contact markers for current session
  const loadContactMarkers = async () => {
    try {
      const contactEvents = await AsyncStorage.getItem('gestorOfflineContactEvents');
      if (contactEvents) {
        const events = JSON.parse(contactEvents);
        const sessionEvents = events.filter((event: any) => event.sessionId === monitoringSessionId);
        
        const markers = sessionEvents.map((event: any) => ({
          id: `${event.sessionId}_${event.contactNumber}`,
          coordinate: event.contactLocation,
          observerLocation: event.observerLocation,
          contactNumber: event.contactNumber,
          timestamp: event.timestamp,
        }));
        
        setContactMarkers(markers);
        setContactsCount(sessionEvents.length);
        console.log(`📍 Loaded ${markers.length} contact markers for gestor session`);
      }
    } catch (error) {
      console.error('Error loading contact markers:', error);
    }
  };

  // Create current monitoring session for contact-details screen
  useEffect(() => {
    const createCurrentMonitoringSession = async () => {
      try {
        const currentSession = {
          sessionId: monitoringSessionId,
          protocol,
          startTime: startTime.toISOString(),
          userId: user?.uid,
          deviceInfo: {
            platform: Platform.OS,
          }
        };
        
        await AsyncStorage.setItem('currentMonitoringSession', JSON.stringify(currentSession));
        console.log('✅ Current monitoring session created for contact-details');
      } catch (error) {
        console.error('Error creating current monitoring session:', error);
      }
    };

    createCurrentMonitoringSession();
  }, [monitoringSessionId, protocol, startTime, user?.uid]);

  // Save comprehensive monitoring data whenever it changes (debounced)
  useEffect(() => {
    // Debounce saving to reduce memory pressure
    const timeoutId = setTimeout(async () => {
      const saveMonitoringData = async () => {
        try {
          const dataToSave = {
            sessionId: monitoringSessionId,
            protocol,
            startTime: startTime.toISOString(),
            totalDistance,
            contactsCount,
            pathCoordinates, // Save ALL GPS points, not just last 100
            lastUpdated: new Date().toISOString(),
            userId: user?.uid,
            status: 'active', // Add required status field
            deviceInfo: {
              platform: Platform.OS,
            }
          };
          
          await AsyncStorage.setItem('monitoringData', JSON.stringify(dataToSave));
          
          // Also save to a separate offline monitoring sessions array for later sync
          await saveOfflineMonitoringSession(dataToSave);
        } catch (error) {
          console.error('Error saving monitoring data:', error);
        }
      };

      // Only save if we have some data to avoid saving initial empty state
      if (totalDistance > 0 || contactsCount > 0 || pathCoordinates.length > 0) {
        saveMonitoringData();
      }
    }, 2000); // Debounce by 2 seconds

    return () => clearTimeout(timeoutId);
  }, [totalDistance, contactsCount, pathCoordinates, protocol, startTime, user?.uid, monitoringSessionId]);

  // Periodic backup save every 30 seconds for extra safety
  useEffect(() => {
    const backupInterval = setInterval(async () => {
      if (totalDistance > 0 || contactsCount > 0 || pathCoordinates.length > 0) {
        try {
          const backupData = {
            sessionId: monitoringSessionId,
            protocol,
            startTime: startTime.toISOString(),
            totalDistance,
            contactsCount,
            pathCoordinates, // Save ALL GPS points, not just last 100
            lastBackup: new Date().toISOString(),
            userId: user?.uid,
            status: 'active', // Add required status field
            zoneId,
            zoneName,
            trajectoryToRedoId,
            isRedoOperation: !!trajectoryToRedoId,
            deviceInfo: {
              platform: Platform.OS,
            }
          };
          
          await AsyncStorage.setItem('gestorMonitoringBackup', JSON.stringify(backupData));
          console.log('💾 Periodic backup saved');
        } catch (error) {
          console.error('Error saving periodic backup:', error);
        }
      }
    }, 30000); // Every 30 seconds

    return () => clearInterval(backupInterval);
  }, [totalDistance, contactsCount, pathCoordinates, protocol, startTime, user?.uid, monitoringSessionId, zoneId, zoneName, trajectoryToRedoId]);

  // Save offline monitoring session for later sync
  const saveOfflineMonitoringSession = async (sessionData: any) => {
    try {
      const offlineSessions = await AsyncStorage.getItem('gestorOfflineMonitoringSessions');
      const sessions = offlineSessions ? JSON.parse(offlineSessions) : [];
      
      // Update existing session or add new one
      const existingIndex = sessions.findIndex((s: any) => s.sessionId === sessionData.sessionId);
      if (existingIndex >= 0) {
        sessions[existingIndex] = sessionData;
      } else {
        sessions.push(sessionData);
      }
      
              await AsyncStorage.setItem('gestorOfflineMonitoringSessions', JSON.stringify(sessions));
    } catch (error) {
      console.error('Error saving offline monitoring session:', error);
    }
  };

  // Save individual GPS points for detailed tracking
  const saveGPSPoint = async (location: Location.LocationObject, distance?: number) => {
    try {
      const gpsPoint = {
        sessionId: monitoringSessionId,
        timestamp: new Date().toISOString(),
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy,
        altitude: location.coords.altitude,
        heading: location.coords.heading,
        speed: location.coords.speed,
        distance: distance || 0,
      };

      // Save to GPS points array
      const gpsPoints = await AsyncStorage.getItem('gestorOfflineGPSPoints');
      const points = gpsPoints ? JSON.parse(gpsPoints) : [];
      points.push(gpsPoint);
      
      // Keep only last 500 points to reduce memory usage
      if (points.length > 500) {
        points.splice(0, points.length - 500);
      }
      
      await AsyncStorage.setItem('gestorOfflineGPSPoints', JSON.stringify(points));
    } catch (error) {
      console.error('Error saving GPS point:', error);
    }
  };

  // Save contact events
  const saveContactEvent = async (observerLocation: {latitude: number, longitude: number}, contactLocation: {latitude: number, longitude: number}, distance: number) => {
    try {
      const contactEvent = {
        sessionId: monitoringSessionId,
        timestamp: new Date().toISOString(),
        observerLocation: observerLocation,
        contactLocation: contactLocation,
        distance: distance,
        contactNumber: contactsCount + 1,
        isReportMode: isReportMode, // Pass the isReportMode flag
      };

      const contactEvents = await AsyncStorage.getItem('gestorOfflineContactEvents');
      const events = contactEvents ? JSON.parse(contactEvents) : [];
      events.push(contactEvent);
      
      await AsyncStorage.setItem('gestorOfflineContactEvents', JSON.stringify(events));
    } catch (error) {
      console.error('Error saving contact event:', error);
    }
  };

  // New state for location retry
  const [isRetryingLocation, setIsRetryingLocation] = useState(false);

  // Screen dimensions for responsive header
  const [screenData, setScreenData] = useState(Dimensions.get('window'));
  const isLandscape = screenData.width > screenData.height;

  // Heading smoothing for better rotation
  const currentHeading = useRef<number>(0);
  const lastUpdateTime = useRef<number>(0);
  const lastPosition = useRef<{lat: number, lng: number} | null>(null);
  const lastAccuracy = useRef<number | null>(null); // Track previous accuracy for gating
  const stationaryCount = useRef<number>(0);
  const userInteracting = useRef<boolean>(false);
  const interactionTimeout = useRef<NodeJS.Timeout | null>(null);
  const regionChangeTimeout = useRef<NodeJS.Timeout | null>(null);
  const locationUpdating = useRef<boolean>(false);
  const currentCamera = useRef<{
    center: { latitude: number; longitude: number };
    zoom: number;
    pitch: number;
    heading: number;
  } | null>(null);
  const lastLoggedHeading = useRef<number>(-1);

  // Calculate optimal zoom level based on current speed
  const getOptimalZoomLevel = (speedMs: number): number => {
    const speedKmh = speedMs * 3.6; // Convert m/s to km/h
    
    if (speedKmh <= 2) {
      // Walking speed (0-2 km/h) - Maximum detail for precise contact placement
      return 20;
    } else if (speedKmh <= 6) {
      // Fast walking/jogging (2-6 km/h) - High detail
      return 19;
    } else if (speedKmh <= 15) {
      // Cycling speed (6-15 km/h) - Medium detail
      return 18;
    } else if (speedKmh <= 30) {
      // Fast cycling/slow car (15-30 km/h) - Lower detail
      return 17;
    } else if (speedKmh <= 60) {
      // Car speed (30-60 km/h) - Wide view
      return 16;
    } else {
      // High speed (60+ km/h) - Very wide view
      return 15;
    }
  };

  // Calculate optimal region delta based on speed
  const getOptimalRegionDelta = (speedMs: number): number => {
    const speedKmh = speedMs * 3.6;
    
    if (speedKmh <= 2) {
      return 0.0005; // Maximum precision for walking
    } else if (speedKmh <= 6) {
      return 0.0008; // High precision for fast walking
    } else if (speedKmh <= 15) {
      return 0.002; // Medium precision for cycling
    } else if (speedKmh <= 30) {
      return 0.005; // Lower precision for fast cycling
    } else if (speedKmh <= 60) {
      return 0.01; // Wide view for car
    } else {
      return 0.02; // Very wide view for high speed
    }
  };
  const lastLoggedZoom = useRef<number>(-1);
  const locationSubscription = useRef<Location.LocationSubscription | undefined>(undefined);
  const sensorsStarted = useRef<boolean>(false);
  const lastBearing = useRef<number | null>(null);
  const stationaryTime = useRef<number>(0);
  // Enhanced movement detection using adaptive filtering
  const lastRecordedTime = useRef<number>(0);

  // Screen wake state tracking
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);
  const [isScreenAwake, setIsScreenAwake] = useState(true);
  const lastGoodAccuracy = useRef<number | null>(null);
  const screenWakeTime = useRef<number>(Date.now());

  // Keep screen awake during monitoring
  useEffect(() => {
    try {
      KeepAwake.activate();
      console.log('📱 Gestor: Screen timeout prevention activated');
    } catch (error) {
      console.error('❌ Gestor: Failed to activate keep awake:', error);
    }

    // Cleanup on unmount
    return () => {
      try {
        KeepAwake.deactivate();
        console.log('📱 Gestor: Screen timeout prevention deactivated');
      } catch (error) {
        console.error('❌ Gestor: Failed to deactivate keep awake:', error);
      }
    };
  }, []);



  // Monitor app state changes to detect screen wake/sleep
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log('📱 Gestor: App state changed:', appState, '->', nextAppState);
      
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        // Screen woke up - GPS might need time to re-acquire accuracy
        console.log('📱 Gestor: Screen woke up - GPS re-acquisition period started');
        screenWakeTime.current = Date.now();
        setIsScreenAwake(true);
        

      } else if (nextAppState.match(/inactive|background/)) {
        // Screen went to sleep
        console.log('📱 Gestor: Screen went to sleep');
        setIsScreenAwake(false);
      }
      
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [appState]);

  useEffect(() => {
    const onChange = (result: { window: any }) => {
      setScreenData(result.window);
    };
    
    const subscription = Dimensions.addEventListener('change', onChange);
    return () => subscription?.remove();
  }, []);

  // Time counter effect
  useEffect(() => {
    const interval = setInterval(() => {
      // Stop counting if session has been terminated
      if (sessionEndTime) return;
      
      const now = new Date();
      const elapsed = Math.floor((now.getTime() - startTime.getTime()) / 1000);
      setElapsedTime(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime, sessionEndTime]);

  // Calculate bearing between two points
  const calculateBearing = (startLat: number, startLng: number, destLat: number, destLng: number): number => {
    const dLng = (destLng - startLng) * (Math.PI / 180);
    const startLatRad = startLat * (Math.PI / 180);
    const destLatRad = destLat * (Math.PI / 180);
    
    const y = Math.sin(dLng) * Math.cos(destLatRad);
    const x = Math.cos(startLatRad) * Math.sin(destLatRad) - 
              Math.sin(startLatRad) * Math.cos(destLatRad) * Math.cos(dLng);
    
    let bearing = Math.atan2(y, x) * (180 / Math.PI);
    return (bearing + 360) % 360; // Normalize to 0-360
  };

  // Using calculateDistance from utils/locationFiltering.ts

  // Smooth bearing changes to avoid jittery rotation
  const smoothBearing = (newBearing: number, lastBearing: number | null): number => {
    if (lastBearing === null) return newBearing;
    
    // Handle the 0-360 wrap around
    let diff = newBearing - lastBearing;
    if (diff > 180) diff -= 360;
    if (diff < -180) diff += 360;
    
    // Only apply significant bearing changes to avoid jitter
    if (Math.abs(diff) < 10) return lastBearing;
    
    // Smooth the bearing change
    const smoothed = lastBearing + (diff * 0.3); // 30% of the change
    return (smoothed + 360) % 360;
  };

  // Location and bearing tracking effect
  useEffect(() => {
    const startLocationTracking = async () => {
      if (sensorsStarted.current) {
        return;
      }

      try {
        sensorsStarted.current = true;
        

        
        const { status: locationStatus } = await Location.getForegroundPermissionsAsync();
        if (locationStatus === 'granted') {
          locationSubscription.current = await Location.watchPositionAsync(
            {
              accuracy: Location.Accuracy.BestForNavigation,
              timeInterval: 1000, // Update every 1 second for compass tracking
              distanceInterval: 2, // Update every 2 meters
              mayShowUserSettingsDialog: true,
            },
            (newLocation) => {
              const currentTime = Date.now();
              setLocation(newLocation);
              setErrorMsg(null);
              
              // Debug: Log heading data
              if (compassTracking) {
                console.log('📍 Gestor Location with heading:', {
                  heading: newLocation.coords.heading,
                  hasHeading: newLocation.coords.heading !== null,
                  compassTracking: compassTracking
                });
              }
              
              // Add current location to path
              const newCoordinate = {
                latitude: newLocation.coords.latitude,
                longitude: newLocation.coords.longitude
              };
              
              if (lastPosition.current) {
                const distance = calculateDistance(
                  lastPosition.current.lat,
                  lastPosition.current.lng,
                  newLocation.coords.latitude,
                  newLocation.coords.longitude
                );
                
                const timeDiff = (currentTime - lastUpdateTime.current) / 1000; // seconds
                const speed = calculateSpeed(distance, timeDiff * 1000); // m/s
                
                // Use advanced accuracy-gating to prevent false movement detection
                // Apply the same filtering for both report mode and zone mode
                const shouldRecord = shouldRecordGPSPoint(
                  distance,
                  speed,
                  newLocation.coords.accuracy,
                  timeDiff * 1000,
                  adjustedFilterConfig,
                  lastAccuracy.current // Pass previous accuracy for gating
                );
                
                if (shouldRecord) {
                  // Record significant movement
                  console.log(`🚶 DISTANCE: Adding ${distance.toFixed(2)}m to total distance ${isReportMode ? '(Report Mode)' : '(Zone Mode)'}`);
                  setTotalDistance(prevTotal => {
                    const newTotal = prevTotal + distance;
                    console.log(`🚶 DISTANCE: Total distance updated: ${prevTotal.toFixed(2)}m → ${newTotal.toFixed(2)}m`);
                    return newTotal;
                  });
                  
                  // Add GPS point to complete trajectory path
                  setPathCoordinates(prevPath => [...prevPath, newCoordinate]);
                  
                  // Save enhanced GPS point with filtering metadata
                  const enhancedGPSPoint = createEnhancedGPSPoint(
                    monitoringSessionId,
                    newLocation,
                    distance,
                    speed,
                    true
                  );
                  saveGPSPoint(newLocation, distance);
                  
                  // Update last recorded time
                  lastRecordedTime.current = currentTime;
                } else {
                  // Save GPS point for debugging but don't add to path
                  console.log(`🚫 DISTANCE: Skipping ${distance.toFixed(2)}m movement (speed: ${speed.toFixed(2)}m/s, accuracy: ${newLocation.coords.accuracy?.toFixed(1)}m) ${isReportMode ? '(Report Mode)' : '(Zone Mode)'}`);
                  const enhancedGPSPoint = createEnhancedGPSPoint(
                    monitoringSessionId,
                    newLocation,
                    distance,
                    speed,
                    false
                  );
                  saveGPSPoint(newLocation, 0);
                }
                
                // Only update bearing if moving with sufficient speed and distance
                if (shouldUpdateBearing(distance, speed)) {
                  const bearing = calculateBearing(
                    lastPosition.current.lat,
                    lastPosition.current.lng,
                    newLocation.coords.latitude,
                    newLocation.coords.longitude
                  );
                  
                  const smoothedBearing = smoothBearing(bearing, lastBearing.current);
                  lastBearing.current = smoothedBearing;
                  stationaryTime.current = 0;
                  
                  // Update map rotation only if user is not manually interacting and map is ready
                  if (!userInteracting.current && mapRef.current && mapReady) {
                    locationUpdating.current = true;
                    
                    const cameraCenter = currentCamera.current ? 
                      currentCamera.current.center : 
                      { latitude: newLocation.coords.latitude, longitude: newLocation.coords.longitude };
                    
                    const cameraZoom = currentCamera.current ? 
                      currentCamera.current.zoom : 17;
                    
                    // Use device heading for compass tracking, otherwise use movement-based bearing
                    const rotationHeading = compassTracking && newLocation.coords.heading !== null ? 
                      newLocation.coords.heading : smoothedBearing;
                    
                    mapRef.current.animateCamera({
                      center: {
                        latitude: newLocation.coords.latitude,
                        longitude: newLocation.coords.longitude
                      },
                      pitch: is3DView ? 45 : 0,
                      heading: rotationHeading,
                      altitude: 500,
                      zoom: cameraZoom,
                    }, { duration: 500 });
                    
                    // Update camera state and zoom level
                    currentCamera.current = {
                      center: {
                        latitude: newLocation.coords.latitude,
                        longitude: newLocation.coords.longitude
                      },
                      zoom: cameraZoom,
                      pitch: is3DView ? 45 : 0,
                      heading: rotationHeading
                    };
                    
                    // Update zoom level state to keep it in sync
                    setCurrentZoomLevel(cameraZoom);
                  }
                } else {
                  stationaryTime.current += timeDiff;
                  
                  // Update position but keep current heading
                  if (!userInteracting.current && mapRef.current && mapReady) {
                    // Use device heading for compass tracking, otherwise use stored heading
                    const currentHeading = compassTracking && newLocation.coords.heading !== null ? 
                      newLocation.coords.heading : (currentCamera.current?.heading || lastBearing.current || 0);
                    
                    // Calculate optimal zoom based on current speed
                    const optimalZoom = getOptimalZoomLevel(speed);
                    const currentZoom = currentCamera.current?.zoom || optimalZoom;
                    
                    mapRef.current.animateCamera({
                      center: {
                        latitude: newLocation.coords.latitude,
                        longitude: newLocation.coords.longitude
                      },
                      pitch: is3DView ? 45 : 0,
                      heading: currentHeading,
                      altitude: 500,
                      zoom: currentZoom,
                    }, { duration: 300 });
                    
                    // Update zoom level state
                    setCurrentZoomLevel(currentZoom);
                  }
                }
              } else {
                // First location - add to path
                setPathCoordinates([newCoordinate]);
              }
              
              lastPosition.current = { 
                lat: newLocation.coords.latitude, 
                lng: newLocation.coords.longitude 
              };
              lastUpdateTime.current = currentTime;
              lastAccuracy.current = newLocation.coords.accuracy;
            }
          );
          
        }

      } catch (error) {
        sensorsStarted.current = false;
      }
    };

    // Only start tracking once when map is ready
    if (mapReady && !sensorsStarted.current) {
      startLocationTracking();
    }

    return () => {
      if (locationSubscription.current) {
        locationSubscription.current.remove();
        locationSubscription.current = undefined;
      }
      
      // Clean up timers and references
      if (interactionTimeout.current) {
        clearTimeout(interactionTimeout.current);
      }
      if (regionChangeTimeout.current) {
        clearTimeout(regionChangeTimeout.current);
      }
      
      // Reset memory-intensive state
      setPathCoordinates([]);
      
      sensorsStarted.current = false;
    };
  }, [mapReady]);

  // Format elapsed time as HH:MM:SS
  const formatElapsedTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Format distance in appropriate units
  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    } else {
      return `${(meters / 1000).toFixed(2)}km`;
    }
  };

  // Add maximum loading timeout
  useEffect(() => {
    const maxLoadingTimeout = setTimeout(() => {
      if (!showMap && !location) {
        setShowMap(true);
        setErrorMsg(pt.locationError);
      }
    }, 10000);

    return () => clearTimeout(maxLoadingTimeout);
  }, [showMap, location]);

  // Separate initial location fetch from continuous updates
  useEffect(() => {
    let isMounted = true;
    let locationTimeout: NodeJS.Timeout;

    const getInitialLocation = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const currentStatus = await Location.getForegroundPermissionsAsync();
        let status = currentStatus.status;
        
        if (status !== 'granted') {
          const permissionResult = await Location.requestForegroundPermissionsAsync();
          status = permissionResult.status;
        }
        
        if (status !== 'granted') {
          if (isMounted) {
            setErrorMsg(pt.permissionDenied);
            setShowMap(true);
          }
          return;
        }

        const lastKnownLocation = await Location.getLastKnownPositionAsync({
          maxAge: 300000,
        }) as Location.LocationObject | null;

        if (lastKnownLocation && isMounted) {
          setLocation(lastKnownLocation);
          setShowMap(true);
        }

        const timeoutPromise = new Promise((_, reject) => {
          locationTimeout = setTimeout(() => reject(new Error('Timeout')), 8000);
        });

        const locationPromises = [
          Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Low,
          }),
          Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Balanced,
          })
        ];

        const freshLocation = await Promise.race([
          Promise.any(locationPromises),
          timeoutPromise
        ]).catch(error => {
          if (lastKnownLocation) {
            return lastKnownLocation;
          }
          throw error;
        }) as Location.LocationObject | null;

        if (freshLocation && isMounted) {
          setLocation(freshLocation);
          setShowMap(true);
          setErrorMsg(null);
        }

      } catch (error) {
        if (isMounted) {
          setErrorMsg(pt.locationError);
          setShowMap(true);
        }
      }
    };

    getInitialLocation();

    return () => {
      isMounted = false;
      if (locationTimeout) {
        clearTimeout(locationTimeout);
      }
    };
  }, []);

  // Pulse animation for loading
  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  }, []);

  const retryLocation = async () => {
    setIsRetryingLocation(true);
    setErrorMsg(null);
    
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setErrorMsg(pt.permissionDenied);
        setIsRetryingLocation(false);
        return;
      }

      const newLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });
      
      setLocation(newLocation);
      setErrorMsg(null);
    } catch (error) {
      setErrorMsg(pt.locationError);
    } finally {
      setIsRetryingLocation(false);
    }
  };

  const handleMapReady = () => {
    setMapReady(true);
    
    // Initialize camera state with tilted perspective
    if (location) {
      currentCamera.current = {
        center: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude
        },
        zoom: 17,
        pitch: is3DView ? 45 : 0,
        heading: 0 // Start with north orientation
      };
      
      // Ensure the map starts with the tilted perspective
      setTimeout(() => {
        if (mapRef.current) {
          mapRef.current.animateCamera({
            center: {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            },
            pitch: is3DView ? 45 : 0,
            heading: 0,
            altitude: 500,
            zoom: 17,
          }, { duration: 500 });
        }
      }, 100);
    }
    
    setTimeout(() => {
      setMapFullyLoaded(true);
    }, 1000);
  };

  // Handle user map interactions
  const handleUserInteraction = () => {
    userInteracting.current = true;
    
    // Clear existing timeout
    if (interactionTimeout.current) {
      clearTimeout(interactionTimeout.current);
    }
    
    // Resume automatic rotation after 5 seconds of no interaction
    interactionTimeout.current = setTimeout(() => {
      userInteracting.current = false;
    }, 5000);
  };

  const handleRegionChangeComplete = (region: any) => {
    // Ignore region changes caused by location updates
    if (locationUpdating.current) {
      locationUpdating.current = false;
      return;
    }
    
    // Debounce region changes to prevent excessive updates
    if (regionChangeTimeout.current) {
      clearTimeout(regionChangeTimeout.current);
    }
    
    regionChangeTimeout.current = setTimeout(() => {
      const newZoom = Math.log2(360 / region.latitudeDelta);
      
      setCurrentRegion({
        latitudeDelta: region.latitudeDelta,
        longitudeDelta: region.longitudeDelta
      });
      
      // Update current zoom level state
      setCurrentZoomLevel(newZoom);
      
      // Update current camera state to preserve user's view
      const currentHeading = region.heading !== undefined ? region.heading : (currentCamera.current?.heading || lastBearing.current || 0);
      
      currentCamera.current = {
        center: {
          latitude: region.latitude,
          longitude: region.longitude
        },
        zoom: newZoom,
        pitch: is3DView ? 45 : 0,
        heading: currentHeading
      };
      
      // Update lastBearing if we have a heading from the region
      if (region.heading !== undefined) {
        lastBearing.current = region.heading;
      }
      
      handleUserInteraction();
    }, 100);
  };

  const handleRegionChange = (region: any) => {
    // Also handle region change start (when user starts dragging/zooming)
    if (!locationUpdating.current) {
      handleUserInteraction();
      
      // Update zoom level in real-time during user interaction
      const newZoom = Math.log2(360 / region.latitudeDelta);
      setCurrentZoomLevel(newZoom);
    }
  };

  const animateToUserLocation = () => {
    if (location && mapRef.current) {
      handleUserInteraction(); // Mark as user interaction
      
      mapRef.current.animateCamera({
        center: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        },
        pitch: is3DView ? 45 : 0,
        heading: lastBearing.current || 0, // Use last known bearing or north
        altitude: 500,
        zoom: 17,
      }, { duration: 1000 });
    }
  };

  const toggleMapType = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      setMapType(prev => prev === 'standard' ? 'satellite' : 'standard');
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    });
  };

  const toggle3DView = async () => {
    if (location && mapRef.current) {
      try {
        // Get the actual current camera state from the map
        const actualCamera = await mapRef.current.getCamera();
        const newPitch = is3DView ? 0 : 45; // Toggle between flat (0) and tilted (45)
        
        // Use actual camera values instead of stored ones
        const currentHeading = actualCamera.heading || lastBearing.current || 0;
        const currentZoom = actualCamera.zoom || currentZoomLevel || 17;
        
        // Animate to new camera position with actual current values
        mapRef.current.animateCamera({
          center: actualCamera.center || {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          pitch: newPitch,
          heading: currentHeading,
          altitude: 500,
          zoom: currentZoom, // Use actual current zoom
        }, { duration: 500 });
        
        // Update camera state with actual values
        currentCamera.current = {
          center: actualCamera.center || {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          zoom: currentZoom,
          pitch: newPitch,
          heading: currentHeading
        };
        
        setIs3DView(!is3DView);
        handleUserInteraction(); // Mark as user interaction
      } catch (error) {
        console.error('Error getting camera state for 3D toggle:', error);
        // Fallback to original method if getCamera fails
        const newPitch = is3DView ? 0 : 45;
        mapRef.current.animateCamera({
          center: {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          pitch: newPitch,
          heading: lastBearing.current || 0,
          altitude: 500,
          zoom: currentZoomLevel || 17,
        }, { duration: 500 });
        
        setIs3DView(!is3DView);
        handleUserInteraction();
      }
    }
  };

  const toggleCompassTracking = () => {
    const newState = !compassTracking;
    setCompassTracking(newState);
    handleUserInteraction(); // Mark as user interaction
    
    // Debug: Show alert with current state and location heading
    console.log('🧭 Gestor Compass tracking toggled:', {
      newState,
      currentHeading: location?.coords.heading,
      hasHeading: location?.coords.heading !== null
    });
    
    // Force immediate camera update if we have location
    if (newState && location && mapRef.current) {
      const deviceHeading = location.coords.heading;
      if (deviceHeading !== null) {
        mapRef.current.animateCamera({
          center: {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          pitch: is3DView ? 45 : 0,
          heading: deviceHeading,
          altitude: 500,
          zoom: currentZoomLevel || 17,
        }, { duration: 1000 });
      }
    }
  };

  const handleTerminate = () => {
    console.log('🔄 DISTANCE_VALIDATION: handleTerminate called - showing terminate modal');
    setShowTerminateModal(true);
  };



  // Load contact images for current session
  const loadContactImages = async () => {
    try {
      const contactEvents = await AsyncStorage.getItem('gestorOfflineContactEvents');
      if (contactEvents) {
        const events = JSON.parse(contactEvents);
        const sessionEvents = events.filter((event: any) => event.sessionId === monitoringSessionId);
        
        // Collect all images from all contacts in this session
        const allImages: string[] = [];
        sessionEvents.forEach((event: any) => {
          if (event.images && Array.isArray(event.images)) {
            allImages.push(...event.images);
          }
        });
        
        setContactImages(allImages);
        console.log(`📸 Loaded ${allImages.length} contact images for session`);
      }
    } catch (error) {
      console.error('Error loading contact images:', error);
    }
  };

  const confirmTerminate = async () => {
    console.log('🔄 DISTANCE_VALIDATION: confirmTerminate called');
    
    // Calculate final session data immediately
    const endTime = new Date();
    const finalElapsed = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
    
    // Distance validation - minimum 2.5 km required (skip for reports)
    if (!isReportMode) {
    const minimumDistance = 2.5; // km
    const currentDistanceKm = totalDistance / 1000; // convert meters to km
    
    console.log('📊 Distance validation:', {
      totalDistanceMeters: totalDistance,
      currentDistanceKm: currentDistanceKm.toFixed(2),
      minimumRequired: minimumDistance,
      isValid: currentDistanceKm >= minimumDistance
    });
    
    if (currentDistanceKm < minimumDistance) {
      const missingDistance = (minimumDistance - currentDistanceKm).toFixed(2);
      
      console.log('❌ Distance validation failed:', {
        current: currentDistanceKm.toFixed(2) + ' km',
        required: minimumDistance + ' km',
        missing: missingDistance + ' km'
      });
      
      // Close the terminate modal first
      console.log('🔄 DISTANCE_VALIDATION: Closing terminate modal');
      setShowTerminateModal(false);
      
      // Add console log to track when alert is closed without confirmation
      console.log('🔄 DISTANCE_VALIDATION: About to show first alert');
      console.log('🔄 DISTANCE_VALIDATION: Alert config will be:', {
        type: 'info',
        title: pt.insufficientDistance,
        hasOnConfirm: true
      });
      
      // Show distance validation alert with option to end trajectory creation
      showAlert({
        type: 'info',
        title: pt.insufficientDistance,
        message: `${pt.minimumDistanceRequired}\n\n${pt.currentDistance} ${currentDistanceKm.toFixed(2)} km\n${pt.missingDistance} ${missingDistance} km\n\n${pt.endTrajectoryQuestion}\n\nPrima "CONFIRMAR" se deseja terminar sem guardar.\nPrima "CANCELAR" para continuar a criação do trajeto.`,
        onConfirm: async () => {
          console.log('🔄 DISTANCE_VALIDATION: User clicked CONFIRMAR - wants to end trajectory creation');
          console.log('🔄 DISTANCE_VALIDATION: Starting trajectory cancellation process...');
          
          // Cancel the trajectory without sending to database
          console.log('🚫 User chose to cancel trajectory despite insufficient distance');
          console.log('🗑️ Trajectory cancelled by user - no data will be saved');
          
          // Clear any stored monitoring data without saving
          try {
            console.log('🔄 DISTANCE_VALIDATION: Starting data cleanup...');
            
            // Clear general monitoring data
            await AsyncStorage.removeItem('monitoringData');
            console.log('🗑️ Removed monitoringData');
            
            await AsyncStorage.removeItem('currentMonitoringSession');
            console.log('🗑️ Removed currentMonitoringSession');
            
            // Clear gestor-specific offline data for this session
            const sessionId = monitoringSessionId;
            console.log('🔄 DISTANCE_VALIDATION: Clearing data for session:', sessionId);
            
            // Remove GPS points for this session
            const gpsPoints = await AsyncStorage.getItem('gestorOfflineGPSPoints');
            if (gpsPoints) {
              const points = JSON.parse(gpsPoints);
              const filteredPoints = points.filter((point: any) => point.sessionId !== sessionId);
              await AsyncStorage.setItem('gestorOfflineGPSPoints', JSON.stringify(filteredPoints));
              console.log('🗑️ Filtered GPS points');
            }
            
            // Remove contact events for this session
            const contactEvents = await AsyncStorage.getItem('gestorOfflineContactEvents');
            if (contactEvents) {
              const events = JSON.parse(contactEvents);
              const filteredEvents = events.filter((event: any) => event.sessionId !== sessionId);
              await AsyncStorage.setItem('gestorOfflineContactEvents', JSON.stringify(filteredEvents));
              console.log('🗑️ Filtered contact events');
            }
            
            // Remove monitoring session data
            const monitoringSessions = await AsyncStorage.getItem('gestorOfflineMonitoringSessions');
            if (monitoringSessions) {
              const sessions = JSON.parse(monitoringSessions);
              const filteredSessions = sessions.filter((session: any) => session.sessionId !== sessionId);
              await AsyncStorage.setItem('gestorOfflineMonitoringSessions', JSON.stringify(filteredSessions));
              console.log('🗑️ Filtered monitoring sessions');
            }
            
            console.log('🗑️ All monitoring data cleared without saving for session:', sessionId);
          } catch (error) {
            console.error('❌ Error clearing monitoring data:', error);
          }
          
          // Directly terminate without showing another alert
          console.log('🔄 DISTANCE_VALIDATION: Data cleanup completed, terminating immediately');
          console.log('🔄 DISTANCE_VALIDATION: About to call onTerminate() to return to trajectory creation');
          console.log('🔄 DISTANCE_VALIDATION: onTerminate function:', typeof onTerminate);
          
          // Add a small delay to ensure the current alert closes first
          setTimeout(() => {
            console.log('🔄 DISTANCE_VALIDATION: Calling onTerminate() after delay');
            onTerminate();
            console.log('🔄 DISTANCE_VALIDATION: onTerminate() call completed');
          }, 100);
        }
      });
      
      return; // Don't proceed with normal termination
    }
    
    console.log('✅ Distance validation passed:', currentDistanceKm.toFixed(2) + ' km >= ' + minimumDistance + ' km');
    }
    
    setSessionEndTime(endTime);
    setFinalElapsedTime(finalElapsed);
    setShowTerminateModal(false);
    
    // Load contact images before showing summary modal
    await loadContactImages();
    setShowSummaryModal(true);
  };

  const handleSendToDatabase = async () => {
    setIsSendingData(true);
    
    try {
      console.log('🎯 Starting handleSendToDatabase process...');
      
      // Check if there are contact images that need uploading
      if (contactImages.length > 0) {
        console.log(`📸 Found ${contactImages.length} contact images, showing upload progress...`);
        setIsUploadingContactImages(true);
        setContactUploadProgress(0);
      }
      
      // Mark session as completed before clearing
      const finalSessionData = {
        sessionId: monitoringSessionId,
        protocol,
        startTime: startTime.toISOString(),
        endTime: sessionEndTime?.toISOString() || new Date().toISOString(),
        totalDistance,
        contactsCount,
        pathCoordinates,
        userId: user?.uid,
        status: 'completed',
        deviceInfo: {
          platform: Platform.OS,
        }
      };
      
      console.log('📊 Final session data:', {
        sessionId: finalSessionData.sessionId,
        protocol: finalSessionData.protocol,
        duration: finalSessionData.endTime ? 
          Math.floor((new Date(finalSessionData.endTime).getTime() - new Date(finalSessionData.startTime).getTime()) / 1000) : 
          'unknown',
        totalDistance: finalSessionData.totalDistance,
        contactsCount: finalSessionData.contactsCount,
        pathPoints: finalSessionData.pathCoordinates.length,
        userId: finalSessionData.userId,
        status: finalSessionData.status
      });
      
      // Save final session data for sync
      await saveOfflineMonitoringSession(finalSessionData);
      console.log('✅ Session data saved to offline storage');
      
      // Update progress if showing upload modal
      if (contactImages.length > 0) {
        setContactUploadProgress(25);
        console.log('📸 Upload progress: 25%');
      }
      
      // Check what's stored in offline storage
      const allSessions = await AsyncStorage.getItem('gestorOfflineMonitoringSessions');
      const allContacts = await AsyncStorage.getItem('gestorOfflineContactEvents');
      const allGPS = await AsyncStorage.getItem('gestorOfflineGPSPoints');
      
      console.log('📱 Offline storage summary:', {
        sessions: allSessions ? JSON.parse(allSessions).length : 0,
        contacts: allContacts ? JSON.parse(allContacts).length : 0,
        gpsPoints: allGPS ? JSON.parse(allGPS).length : 0
      });
      
      // Create a proper report for the database (whether online or offline)
      const reportData = {
        sessionId: monitoringSessionId,
        userId: user?.uid,
        userName: user?.displayName || user?.email || 'Utilizador',
        userEmail: user?.email || '',
        userRole: 'gestor_zona_caca',
        type: 'gestor_monitoring_report',
        zoneId: zoneId,
        zoneName: zoneName,
        protocol,
        startTime: startTime.toISOString(),
        endTime: sessionEndTime?.toISOString() || new Date().toISOString(),
        sessionDuration: finalElapsedTime,
        totalDistance,
        contactsCount,
        pathCoordinates,
        location: location ? {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        } : null,
        weather: weatherData || null,
        deviceInfo: {
          platform: Platform.OS,
        },
        createdAt: new Date().toISOString(),
        status: 'completed',
        observersCount: observersCount || 1,
        source: 'mobile', // Mark as GPS/mobile created
      };
      
      // Check internet connectivity and save accordingly
      const netInfo = await NetInfo.fetch();
      console.log('🌐 Network status:', {
        isConnected: netInfo.isConnected,
        type: netInfo.type,
        isInternetReachable: netInfo.isInternetReachable
      });
      
      // Update progress if showing upload modal
      if (contactImages.length > 0) {
        setContactUploadProgress(50);
        console.log('📸 Upload progress: 50%');
      }
      
      // Check if this report has already been saved to avoid duplicates
      const existingSyncId = `monitoring_${user?.uid}_${monitoringSessionId}`;
      const pendingReports = await AsyncStorage.getItem('pendingReports');
      const existingReports = pendingReports ? JSON.parse(pendingReports) : [];
      const alreadySaved = existingReports.some((report: any) => 
        report.sessionId === monitoringSessionId || 
        report.syncId === existingSyncId
      );
      
      if (alreadySaved) {
        console.log('⚠️ Report already saved, skipping to avoid duplicates');
        setShowSummaryModal(false);
        onTerminate();
        return;
      }
      
      // IMPROVED: Always save offline first for reliability, then sync in background
      console.log('💾 Saving data offline first for reliability...');
      
      // Store as pending report for sync (works with or without internet)
      const pendingReport = {
        syncId: existingSyncId,
        ...reportData,
        type: 'gestor_monitoring_report',
        localImageUris: [],
        images: [],
        isRedoOperation: !!trajectoryToRedoId,
        trajectoryToRedoId: trajectoryToRedoId,
        // Store cleanup info for sync process
        needsCleanup: !!trajectoryToRedoId,
        cleanupTrajectoryId: trajectoryToRedoId,
      };
      
      existingReports.push(pendingReport);
      await AsyncStorage.setItem('pendingReports', JSON.stringify(existingReports));
      console.log('✅ Monitoring report saved offline successfully');
      
      // Update progress if showing upload modal
      if (contactImages.length > 0) {
        setContactUploadProgress(50);
        console.log('📸 Upload progress: 50% (data saved offline)');
      }
      
      // Now try to sync in background if internet is available
      if (netInfo.isConnected) {
        console.log('🌐 Internet available, starting background sync...');
        
        try {
          // Save directly to Firestore
          const db = getFirestore();
          
          if (trajectoryToRedoId) {
            // CLEANUP OLD CONTACTS AND IMAGES BEFORE UPDATING TRAJECTORY
            console.log('🔄 Updating existing trajectory:', trajectoryToRedoId);
            console.log('🧹 Cleaning up old contacts and images for redo operation...');
            
            try {
              // Step 1: Get the old trajectory to find its sessionId (if online)
              // If offline, we'll store cleanup info for later sync
              const oldTrajectoryDoc = await getDoc(doc(db, 'gestorMobile_trajetos', trajectoryToRedoId));
              if (oldTrajectoryDoc.exists()) {
                const oldTrajectoryData = oldTrajectoryDoc.data();
                const oldSessionId = oldTrajectoryData.sessionId;
                console.log('📋 Old trajectory data:', {
                  id: trajectoryToRedoId,
                  sessionId: oldSessionId,
                  createdAt: oldTrajectoryData.createdAt,
                  updatedAt: oldTrajectoryData.updatedAt
                });
                
                if (oldSessionId) {
                  console.log('🔍 Found old sessionId:', oldSessionId);
                  console.log('🔍 Searching for contacts with sessionId:', oldSessionId);
                  
                  // Step 2: Get all contacts for this trajectory (by multiple criteria)
                  // First try by sessionId
                  const oldContactsQuery = query(
                    collection(db, 'gestorMobile_contacts'),
                    where('sessionId', '==', oldSessionId)
                  );
                  const oldContactsSnapshot = await getDocs(oldContactsQuery);
                  
                  // SAFETY LAYER 2: Additional search with strict filters for this specific trajectory
                  // Only search for contacts that belong to THIS USER and THIS ZONE
                  const alternativeContactsQuery = query(
                    collection(db, 'gestorMobile_contacts'),
                    where('userId', '==', user?.uid), // SAFETY: Only this user's contacts
                    where('protocol', '==', 'trajeto') // SAFETY: Only trajectory contacts
                  );
                  const alternativeContactsSnapshot = await getDocs(alternativeContactsQuery);
                  
                  // SAFETY LAYER 3: Multi-criteria filtering for bulletproof matching
                  const alternativeContacts = alternativeContactsSnapshot.docs.filter(doc => {
                    const contactData = doc.data();
                    
                    // SAFETY CHECK 1: Must be same user
                    if (contactData.userId !== user?.uid) {
                      return false;
                    }
                    
                    // SAFETY CHECK 2: Must be trajectory protocol
                    if (contactData.protocol !== 'trajeto') {
                      return false;
                    }
                    
                    // SAFETY CHECK 3: Time proximity check (within 2 hours of trajectory creation)
                    const contactTime = contactData.timestamp?.toDate?.() || new Date(contactData.timestamp);
                    const trajectoryTime = oldTrajectoryData.createdAt?.toDate?.() || new Date(oldTrajectoryData.createdAt);
                    const timeDiff = Math.abs(contactTime.getTime() - trajectoryTime.getTime());
                    const timeMatch = timeDiff < 2 * 60 * 60 * 1000; // 2 hours window
                    
                    // SAFETY CHECK 4: Not already found by sessionId
                    const notDuplicate = !oldContactsSnapshot.docs.find(oldDoc => oldDoc.id === doc.id);
                    
                    // SAFETY CHECK 5: Additional zone safety - if we have zone info in contact, verify it matches
                    const zoneMatch = !contactData.zoneId || contactData.zoneId === zoneId;
                    
                    console.log(`🔍 Contact ${doc.id} safety checks:`, {
                      userId: contactData.userId === user?.uid,
                      protocol: contactData.protocol === 'trajeto',
                      timeMatch,
                      notDuplicate,
                      zoneMatch,
                      timeDiff: `${Math.round(timeDiff / 1000 / 60)} minutes`
                    });
                    
                    return timeMatch && notDuplicate && zoneMatch;
                  });
                  
                  // Combine both sets of contacts
                  const allContactsToDelete = [...oldContactsSnapshot.docs, ...alternativeContacts];
                  
                  console.log(`📞 Found ${oldContactsSnapshot.size} contacts by sessionId`);
                  console.log(`📞 Found ${alternativeContacts.length} additional contacts by time/user`);
                  console.log(`📞 Total ${allContactsToDelete.length} contacts to delete`);
                  
                  // SAFETY LAYER 4: Final verification before deletion
                  console.log('🛡️ FINAL SAFETY CHECK before deletion:');
                  console.log(`- Current user: ${user?.uid}`);
                  console.log(`- Target zone: ${zoneId}`);
                  console.log(`- Trajectory being redone: ${trajectoryToRedoId}`);
                  
                  // Final safety filter - absolutely no deletion if any contact doesn't belong to current user
                  const finalSafeContacts = allContactsToDelete.filter(doc => {
                    const contactData = doc.data();
                    const isCurrentUser = contactData.userId === user?.uid;
                    const isTrajetoProtocol = contactData.protocol === 'trajeto';
                    
                    if (!isCurrentUser) {
                      console.error(`🚨 SAFETY VIOLATION: Contact ${doc.id} belongs to different user: ${contactData.userId}`);
                      return false;
                    }
                    
                    if (!isTrajetoProtocol) {
                      console.error(`🚨 SAFETY VIOLATION: Contact ${doc.id} has wrong protocol: ${contactData.protocol}`);
                      return false;
                    }
                    
                    console.log(`✅ Contact ${doc.id} passed final safety check`);
                    return true;
                  });
                  
                  if (finalSafeContacts.length !== allContactsToDelete.length) {
                    console.error('🚨 SAFETY ABORT: Some contacts failed final safety check');
                    throw new Error('Safety check failed - aborting cleanup to prevent data loss');
                  }
                  
                  console.log(`🛡️ All ${finalSafeContacts.length} contacts passed final safety verification`);
                  
                  // Step 3: Delete contact images from Storage
                  let deletedImagesCount = 0;
                  for (const contactDoc of finalSafeContacts) {
                    const contactData = contactDoc.data();
                    if (contactData.images && contactData.images.length > 0) {
                      for (const imageUrl of contactData.images) {
                                                 try {
                           // Extract path from Firebase Storage URL
                           if (imageUrl.includes('gestorMobileContacts_images') || imageUrl.includes('gestoresReports_images')) {
                             // Extract the storage path from the Firebase URL
                             // Firebase URLs format: https://firebasestorage.googleapis.com/v0/b/bucket/o/path?alt=media&token=...
                             const urlParts = imageUrl.split('/o/')[1];
                             if (urlParts) {
                               const storagePath = decodeURIComponent(urlParts.split('?')[0]);
                               const imageRef = ref(storage, storagePath);
                               await deleteObject(imageRef);
                               deletedImagesCount++;
                               console.log('🗑️ Deleted contact image from Storage:', storagePath);
                             }
                           }
                         } catch (imageError) {
                          console.error('❌ Error deleting contact image:', imageError);
                          // Continue with other images even if one fails
                        }
                      }
                    }
                  }
                  
                  // Step 4: Delete contact documents from Firestore
                  const contactDeleteBatch = writeBatch(db);
                  finalSafeContacts.forEach((contactDoc) => {
                    contactDeleteBatch.delete(contactDoc.ref);
                  });
                  
                  if (finalSafeContacts.length > 0) {
                    await contactDeleteBatch.commit();
                    console.log(`✅ Deleted ${finalSafeContacts.length} old contacts from Firestore`);
                  }
                  
                  console.log(`✅ Cleanup completed: ${finalSafeContacts.length} contacts and ${deletedImagesCount} images deleted`);
                } else {
                  console.log('⚠️ Old trajectory has no sessionId, skipping contact cleanup');
                }
              } else {
                console.log('⚠️ Old trajectory document not found, skipping cleanup');
              }
            } catch (cleanupError: any) {
              console.error('❌ Error during cleanup:', cleanupError);
              // If cleanup fails due to network issues, we'll handle it during sync
              // Store cleanup info for later processing
              console.log('⚠️ Cleanup failed, will handle during sync');
            }
            
            // Step 5: Update existing trajectory with new data
            await updateDoc(doc(db, 'gestorMobile_trajetos', trajectoryToRedoId), {
              ...reportData,
              updatedAt: serverTimestamp(),
            });
            console.log('✅ Existing trajectory updated successfully');
          } else {
            // Create new trajectory
            console.log('➕ Creating new trajectory');
            await addDoc(collection(db, 'gestorMobile_trajetos'), {
              ...reportData,
              createdAt: serverTimestamp(),
            });
            console.log('✅ New trajectory created successfully');
          }
          console.log('✅ Monitoring report saved to database successfully');

          // Save report based on mode
          if (isReportMode) {
            // For report mode, save directly to gestoresReports collection
            try {
              const gestorReport = {
                userId: user?.uid,
                userName: user?.displayName || 'Gestor',
                userEmail: user?.email,
                userRole: 'gestor_caca',
                type: 'gestor_report',
                protocol: reportData.protocol,
                sessionId: monitoringSessionId, // Use the same sessionId as contacts
                comment: '', // Reports don't have comments in the current structure
                location: reportData.location,
                zoneId: reportData.zoneId,
                zoneName: reportData.zoneName,
                deviceInfo: reportData.deviceInfo,
                weather: reportData.weather,
                observersCount: reportData.observersCount,
                startTime: reportData.startTime,
                endTime: reportData.endTime,
                sessionDuration: reportData.sessionDuration,
                totalDistance: reportData.totalDistance,
                contactEventsCount: contactsCount,
                pathCoordinates: reportData.pathCoordinates,
                images: contactImages, // contactImages is already an array of URLs
                createdAt: serverTimestamp(),
              };

              console.log('🔄 GESTOR_REPORT: Saving report with sessionId:', monitoringSessionId);
              await addDoc(collection(db, 'gestoresReports'), gestorReport);
              console.log('✅ Gestor report saved to gestoresReports collection');
            } catch (reportError) {
              console.error('❌ Error saving gestor report:', reportError);
              throw reportError; // Fail the operation if report save fails
            }
          } else {
            // For zone mode, also save a summary report to gestoresReports collection for the reports screen
            try {
              const gestorReport = {
                userId: user?.uid,
                userName: user?.displayName || 'Gestor',
                userEmail: user?.email,
                userRole: 'gestor_caca',
                type: 'gestor_monitoring_report',
                protocol: reportData.protocol,
                sessionId: monitoringSessionId, // Use the same sessionId as contacts
                comment: '', // Gestor monitoring doesn't have comments in the current structure
                location: reportData.location,
                zoneId: reportData.zoneId,
                zoneName: reportData.zoneName,
                deviceInfo: reportData.deviceInfo,
                weather: reportData.weather,
                observersCount: reportData.observersCount,
                startTime: reportData.startTime,
                endTime: reportData.endTime,
                sessionDuration: reportData.sessionDuration,
                totalDistance: reportData.totalDistance,
                contactEventsCount: contactsCount,
                pathCoordinates: reportData.pathCoordinates,
                images: contactImages, // contactImages is already an array of URLs
                createdAt: serverTimestamp(),
              };

              console.log('🔄 GESTOR_MONITORING: Saving report with sessionId:', monitoringSessionId);
              await addDoc(collection(db, 'gestoresReports'), gestorReport);
              console.log('✅ Gestor report summary saved to gestoresReports collection');
            } catch (reportError) {
              console.error('❌ Error saving gestor report summary:', reportError);
              // Don't fail the whole operation if just the summary fails
            }
          }
          
          // Update progress if showing upload modal
          if (contactImages.length > 0) {
            setContactUploadProgress(75);
            console.log('📸 Upload progress: 75%');
          }
          
          // Also trigger immediate sync for any other offline data
          console.log('🔄 SYNC: About to trigger immediate sync of offline data...');
          await monitoringSyncService.syncOfflineData();
          console.log('✅ SYNC: Additional offline data sync completed');
        } catch (syncError) {
          console.error('⚠️ Background sync failed (data is safe offline):', syncError);
          
          // Update progress if showing upload modal
          if (contactImages.length > 0) {
            setContactUploadProgress(75);
            console.log('📸 Upload failed, but data is saved offline: 75%');
          }
          
          // Data is already saved offline, so this is not critical
          console.log('📱 Data is safely stored offline and will sync when connection improves');
        }
      } else {
        console.log('📡 No internet - data saved offline and will sync when connection is available');
      }
      
      // Clear active monitoring data after successful save
      await AsyncStorage.removeItem('monitoringData');
      console.log('🗑️ Active monitoring data cleared');
      
      // Clear current monitoring session for contact-details
      await AsyncStorage.removeItem('currentMonitoringSession');
      console.log('🗑️ Current monitoring session cleared');
      
      // Clear backup data since trajectory was successfully saved
      await AsyncStorage.removeItem('gestorMonitoringBackup');
      console.log('🗑️ Monitoring backup data cleared');
      
      // Update progress to 100% if we were showing upload progress
      if (contactImages.length > 0) {
        setContactUploadProgress(100);
        console.log('📸 Upload progress completed: 100%');
        // Small delay to show completion
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      setShowSummaryModal(false);
      
      // Terminate the monitoring session and let parent handle everything
      onTerminate();
      
    } catch (error) {
      console.error('❌ Error saving monitoring data:', error);
      showAlert({
        type: 'error',
        message: 'Erro ao guardar os dados da sessão. Tente novamente.'
      });
    } finally {
      setIsSendingData(false);
      // Hide upload progress modal
      setIsUploadingContactImages(false);
      setContactUploadProgress(0);
    }
  };

  const cancelTerminate = () => {
    setShowTerminateModal(false);
  };

  const cancelSendToDatabase = () => {
    setShowSummaryModal(false);
  };

  // Image viewer functions
  const handleViewImages = () => {
    if (contactImages.length > 0) {
      setCurrentImageIndex(0);
      setShowImageViewer(true);
    }
  };

  const closeImageViewer = () => {
    setShowImageViewer(false);
    setCurrentImageIndex(0);
  };

  const nextImage = () => {
    if (currentImageIndex < contactImages.length - 1) {
      setCurrentImageIndex(currentImageIndex + 1);
    }
  };

  const prevImage = () => {
    if (currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
    }
  };

  // Handle Android back button to prevent accidental exit
  useFocusEffect(
    useCallback(() => {
      // Reload contact markers when screen comes back into focus
      loadContactMarkers();
      
      const onBackPress = () => {
        // Show terminate confirmation instead of allowing back navigation
        setShowTerminateModal(true);
        return true; // Prevent default back action
      };

      if (Platform.OS === 'android') {
        const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
        return () => subscription.remove();
      }
    }, [])
  );

  // Contact modal handlers
  const handleContactButtonPress = async () => {
    if (location) {
      // Get current camera state directly from map
      try {
        if (mapRef.current) {
          const currentCameraState = await mapRef.current.getCamera();
          console.log('Current camera state from map:', currentCameraState);
          
          // Update our camera reference with actual current state
          currentCamera.current = {
            center: currentCameraState.center,
            zoom: currentCameraState.zoom || 17,
            pitch: currentCameraState.pitch || 45,
            heading: currentCameraState.heading || 0
          };
          
          // Update states with actual values
          setCurrentZoomLevel(currentCameraState.zoom || 17);
          lastBearing.current = currentCameraState.heading || 0;
        }
      } catch (error) {
        console.log('Error getting camera state:', error);
      }
      
      // Debug: log current zoom level when opening modal
      console.log('Opening contact modal with currentZoomLevel:', currentZoomLevel);
      console.log('Camera zoom:', currentCamera.current?.zoom);
      console.log('Camera heading/bearing:', currentCamera.current?.heading);
      console.log('lastBearing.current:', lastBearing.current);
      setShowContactModal(true);
    }
  };

  const handleContactModalClose = () => {
    setShowContactModal(false);
    setCurrentContactData(null);
  };

  const handleContactDetailsModalClose = () => {
    console.log('🔄 GESTOR_ACTIVE: Contact details modal closed');
    setShowContactDetailsModal(false);
    setCurrentContactData(null);
    // Reload contact markers to show the new contact
    loadContactMarkers();
  };

  const handleContactDetailsSubmit = async (contactData: any) => {
    console.log('🔄 GESTOR_ACTIVE: Contact details submitted', contactData);
    
    if (!currentContactData) return;
    
    try {
      // Create complete contact event similar to contact-details screen
      const completeContactEvent = {
        sessionId: monitoringSessionId,
        timestamp: new Date().toISOString(),
        observerLocation: currentContactData.observerLocation,
        contactLocation: currentContactData.contactLocation,
        distance: currentContactData.distance,
        bearing: currentContactData.bearing,
        contactNumber: contactsCount + 1,
        circumstances: contactData.circumstances,
        contactLocationDetails: {
          ...contactData.contactLocation,
          latitude: currentContactData.contactLocation.latitude,
          longitude: currentContactData.contactLocation.longitude,
        },
        images: contactData.images || [],
        userId: user?.uid || 'unknown_user',
        userName: user?.displayName || user?.email || 'Unknown User',
        userRole: userRole || 'gestor_caca',
        protocol: protocol,
        needsImageSync: false, // Handle image sync separately if needed
        isReportMode: isReportMode, // Pass the isReportMode flag
      };

      // Save to gestor offline contact events
      const contactEvents = await AsyncStorage.getItem('gestorOfflineContactEvents');
      const events = contactEvents ? JSON.parse(contactEvents) : [];
      events.push(completeContactEvent);
      await AsyncStorage.setItem('gestorOfflineContactEvents', JSON.stringify(events));
      
      console.log('🔄 GESTOR_ACTIVE: Contact saved successfully to gestorOfflineContactEvents');
      console.log('🔄 GESTOR_ACTIVE: Contact sessionId:', monitoringSessionId, 'isReportMode:', isReportMode);
      console.log('🔄 GESTOR_ACTIVE: Saved contact event:', completeContactEvent);
      console.log('🔄 GESTOR_ACTIVE: Total events in storage:', events.length);
      
      // Update contact count and markers
      setContactsCount(prev => prev + 1);
      
      // Close modal and reload markers
      setShowContactDetailsModal(false);
      setCurrentContactData(null);
      loadContactMarkers();
      
    } catch (error) {
      console.error('Error saving contact:', error);
      // Show error alert if available
      showAlert({
        type: 'error',
        message: 'Erro ao guardar contacto. Tente novamente.',
      });
    }
  };

  // Handle location placement confirmation - show contact details modal instead of navigating
  const handleLocationPlacementConfirm = async (observerLocation: {latitude: number, longitude: number}, contactLocation: {latitude: number, longitude: number}, distance: number) => {
    // Calculate bearing from observer to contact location
    const bearing = calculateBearing(
      observerLocation.latitude,
      observerLocation.longitude,
      contactLocation.latitude,
      contactLocation.longitude
    );
    
    // Store the contact data for the modal
    setCurrentContactData({
      observerLocation,
      contactLocation,
      distance,
      bearing
    });
    
    // Close location modal and show contact details modal (no screen navigation)
    setShowContactModal(false);
    setShowContactDetailsModal(true);
    console.log('🔄 GESTOR_ACTIVE: Showing contact details modal instead of navigating');
  };



  return (
    <SafeAreaView style={styles.container}>
      <SafeSystemBars 
        style="light" 
        translucent={true} 
        backgroundColor="#0996a8"
        navigationBarColor="#0996a8"
      />
      
      {/* Simple header - NO edge-to-edge, NO complex layout */}
      <View style={{
        backgroundColor: '#0996a8',
        paddingVertical: 15,
        alignItems: 'center',
        justifyContent: 'center',
      }}>
        <Text style={{
          fontSize: 18,
          color: '#ffffff',
          fontWeight: '600',
        }}>
            {isReportMode ? 'Criação de Relatório' : pt.monitoringActive}
          </Text>
        <Text style={{
          fontSize: 12,
          color: '#ffffff',
          opacity: 0.8,
          marginTop: 2,
        }}>
            {pt.protocolOptions[protocol]}
          </Text>
      </View>

      {/* NetworkStatusIndicator positioned over the map */}
      {/* Commented out during monitoring to avoid distraction
      <View style={{
        position: 'absolute',
        top: 115,
        left: 0,
        right: 0,
        alignItems: 'center',
        zIndex: 1000,
      }}>
        <NetworkStatusIndicator style={{
          transform: [{ scale: 0.8 }],
        }} />
      </View>
      */}

      {/* Header Logo - positioned on top of everything */}
      <View style={{
        position: 'absolute',
        left: 12,
        top: isLandscape ? 20 : 15,
        zIndex: 99999,
      }}>
        <Image 
          source={require('../assets/images/header-logo.png')}
          style={{
            width: isLandscape ? 60 : 90,
            height: isLandscape ? 60 : 90,
            resizeMode: 'contain',
          }}
        />
      </View>

      {/* Map Container */}
      <View style={styles.mapContainer}>
        {Platform.OS === 'web' ? (
          <View style={[styles.noMapContainer, styles.webContainer]}>
            <Text style={styles.noMapText}>O mapa não está disponível na versão web.</Text>
          </View>
        ) : (
          <>
            {showMap && location ? (
              <>
                <Animated.View style={[styles.map, { opacity: fadeAnim }]}>
                  <MapView
                    ref={mapRef}
                    style={styles.map}
                    provider={PROVIDER_GOOGLE}
                    mapType={mapType}
                    initialCamera={{
                      center: {
                        latitude: location.coords.latitude,
                        longitude: location.coords.longitude,
                      },
                      pitch: is3DView ? 45 : 0, // Start tilted for 3D navigation view or flat for 2D
                      heading: 0, // Start facing north
                      altitude: 500,
                      zoom: 20,
                    }}
                    showsUserLocation={true}
                    showsMyLocationButton={false}
                    followsUserLocation={compassTracking}
                    showsCompass={true}
                    rotateEnabled={true}
                    pitchEnabled={true}
                    scrollEnabled={true}
                    zoomEnabled={true}
                    onMapReady={handleMapReady}
                    onRegionChange={handleRegionChange}
                    onRegionChangeComplete={handleRegionChangeComplete}
                    onPress={handleRegionChange}
                    loadingEnabled={true}
                    loadingIndicatorColor="#0996a8"
                    loadingBackgroundColor="#ffffff"
                    customMapStyle={[
                      {
                        featureType: "poi",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.business",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.medical",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.park",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.place_of_worship",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.school",
                        stylers: [{ visibility: "off" }]
                      },
                      {
                        featureType: "poi.sports_complex",
                        stylers: [{ visibility: "off" }]
                      }
                    ]}
                  >
                    {/* Removed marker since we have user location dot */}
                    
                    {/* Sighting lines and contact markers */}
                    {contactMarkers.map((marker) => (
                      <React.Fragment key={`sighting-${marker.id}`}>
                        {/* Dashed sighting line from observer to contact */}
                        <Polyline
                          coordinates={[marker.observerLocation, marker.coordinate]}
                          strokeColor="#10B981"
                          strokeWidth={2}
                          lineDashPattern={[10, 5]}
                        />
                        
                        {/* Observer position marker (small blue dot) */}
                        <Marker
                          coordinate={marker.observerLocation}
                          anchor={{ x: 0.5, y: 0.5 }}
                          title={`Observador ${marker.contactNumber}`}
                        >
                          <View style={styles.observerMarker}>
                            <Text style={styles.observerNumber}>{marker.contactNumber}</Text>
                          </View>
                        </Marker>
                        
                        {/* Contact position marker (green dove) */}
                        <Marker
                          coordinate={marker.coordinate}
                          anchor={{ x: 0.5, y: 0.5 }}
                          title={`Contacto ${marker.contactNumber}`}
                          description={new Date(marker.timestamp).toLocaleTimeString('pt-PT', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        >
                          <View style={styles.contactMarker}>
                            <DoveIcon size={20} color="#FFFFFF" />
                          </View>
                        </Marker>
                      </React.Fragment>
                    ))}
                    
                    {/* Path traveled - show as polyline */}
                    {pathCoordinates.length > 1 && (
                      <Polyline
                        coordinates={pathCoordinates}
                        strokeColor="#0996a8"
                        strokeWidth={4}
                      />
                    )}
                  </MapView>
                </Animated.View>



                {/* Map Controls */}
                <View style={styles.mapControlsContainer}>
                  <TouchableOpacity
                    style={styles.mapControlButton}
                    onPress={animateToUserLocation}
                  >
                    <FontAwesome name="crosshairs" size={20} color="#0996a8" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.mapControlButton, { marginTop: 8 }]}
                    onPress={toggleMapType}
                  >
                    <FontAwesome 
                      name={mapType === 'standard' ? 'globe' : 'map'} 
                      size={20} 
                      color="#0996a8" 
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.mapControlButton, { marginTop: 8 }]}
                    onPress={toggle3DView}
                  >
                    <FontAwesome 
                      name={is3DView ? 'cube' : 'square-o'} 
                      size={20} 
                      color="#0996a8" 
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.mapControlButton, { marginTop: 8, backgroundColor: compassTracking ? 'rgba(9, 150, 168, 0.2)' : 'rgba(255, 255, 255, 0.7)' }]}
                    onPress={toggleCompassTracking}
                  >
                    <FontAwesome 
                      name="compass" 
                      size={20} 
                      color={compassTracking ? "#0996a8" : "#0996a8"} 
                    />
                  </TouchableOpacity>
                </View>

                {/* Location Status */}
                {errorMsg && (
                  <View style={styles.locationStatusContainer}>
                    <View style={styles.locationStatusCard}>
                      <FontAwesome name="exclamation-triangle" size={16} color="#ff9500" />
                      <Text style={styles.locationStatusText}>{pt.gpsUnavailable}</Text>
                      <TouchableOpacity onPress={retryLocation} disabled={isRetryingLocation}>
                        {isRetryingLocation ? (
                          <ActivityIndicator size="small" color="#0996a8" />
                        ) : (
                          <FontAwesome name="refresh" size={14} color="#0996a8" />
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </>
            ) : (
              <View style={styles.noMapContainer}>
                <View style={styles.loadingCard}>
                  <View style={styles.logoContainer}>
                    <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                      <FontAwesome name="map-marker" size={35} color="#0996a8" />
                    </Animated.View>
                  </View>
                  <Text style={styles.noMapText}>A carregar mapa...</Text>
                  <View style={styles.loadingIndicatorContainer}>
                    <ActivityIndicator size="small" color="#0996a8" />
                  </View>
                </View>
              </View>
            )}

            {!mapFullyLoaded && showMap && (
              <View style={styles.mapLoadingContainer}>
                <View style={styles.loadingCard}>
                  <View style={styles.logoContainer}>
                    <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                      <FontAwesome name="map-marker" size={35} color="#0996a8" />
                    </Animated.View>
                  </View>
                  <Text style={styles.noMapText}>A carregar mapa...</Text>
                  <View style={styles.loadingIndicatorContainer}>
                    <ActivityIndicator size="small" color="#0996a8" />
                  </View>
                </View>
              </View>
            )}
          </>
        )}

        {/* Bottom Controls */}
        <View style={[styles.bottomControls, { paddingBottom: 10 }]}>
          <View style={styles.statsContainer}>
            <View style={styles.statRow}>
              <FontAwesome name="clock-o" size={12} color="#fff" style={styles.statIcon} />
              <Text style={styles.statLabel}>{pt.timeElapsed}</Text>
              <Text style={styles.statValue}>{formatElapsedTime(elapsedTime)}</Text>
            </View>
            
            <View style={styles.statRow}>
              <FontAwesome name="male" size={12} color="#fff" style={styles.statIcon} />
              <Text style={styles.statLabel}>{pt.distanceTraveled}</Text>
              <Text style={styles.statValue}>{formatDistance(totalDistance)}</Text>
              {/* Distance validation label - only show for zone trajectories, not reports */}
              {!isReportMode && (() => {
                const minimumDistance = 2.5; // km
                const currentDistanceKm = totalDistance / 1000; // convert meters to km
                
                if (currentDistanceKm < minimumDistance) {
                  const missingDistance = (minimumDistance - currentDistanceKm).toFixed(2);
                  return (
                    <View style={styles.distanceWarning}>
                      <FontAwesome name="minus-circle" size={8} color="#dc2626" />
                      <Text style={styles.distanceWarningText}>{missingDistance} km</Text>
                    </View>
                  );
                } else {
                  return (
                    <View style={styles.distanceSuccess}>
                      <FontAwesome name="check-circle" size={8} color="#16a34a" />
                      <Text style={styles.distanceSuccessText}>Válida</Text>
                    </View>
                  );
                }
              })()}
            </View>

            <View style={styles.statRow}>
              <DoveIcon size={12} color="#fff" style={styles.statIcon} />
              <Text style={styles.statLabel}>{pt.contactsCount}</Text>
              <Text style={styles.statValue}>{contactsCount}</Text>
            </View>

          </View>
          
          <TouchableOpacity
            style={styles.terminateButton}
            onPress={handleTerminate}
            activeOpacity={0.7}
          >
            <FontAwesome name="stop" size={20} color="#fff" style={styles.terminateIcon} />
            <Text style={styles.terminateText}>{pt.terminate}</Text>
          </TouchableOpacity>
        </View>

        {/* Floating Report Button */}
        <View style={[styles.buttonContainer, { bottom: insets.bottom + 100 }]}>
          <TouchableOpacity
            style={[styles.button, styles.reportButton]}
            onPress={handleContactButtonPress}
            activeOpacity={0.8}
          >
            <DoveIcon size={20} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.buttonText}>Contacto</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Terminate Confirmation Modal */}
      <Modal
        visible={showTerminateModal}
        transparent={true}
        animationType="fade"
        onRequestClose={cancelTerminate}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <FontAwesome name="question-circle" size={48} color="#0996a8" />
              <Text style={styles.modalTitle}>{pt.confirmTerminate}</Text>
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={cancelTerminate}
                activeOpacity={0.7}
              >
                <FontAwesome name="times" size={16} color="#fff" style={styles.buttonIcon} />
                <Text style={styles.cancelButtonText}>{pt.no}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={confirmTerminate}
                activeOpacity={0.7}
              >
                <FontAwesome name="check" size={16} color="#fff" style={styles.buttonIcon} />
                <Text style={styles.confirmButtonText}>{pt.yes}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Session Summary Modal */}
      <Modal
        visible={showSummaryModal}
        transparent={true}
        animationType="fade"
        onRequestClose={cancelSendToDatabase}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.summaryModalContainer}>
            <View style={styles.modalHeader}>
              <FontAwesome name="file-text-o" size={48} color="#0996a8" />
              <Text style={styles.modalTitle}>Resumo do Trajeto</Text>
            </View>
            
            <View style={styles.summaryContent}>
              <View style={styles.summaryRow}>
                <FontAwesome name="clock-o" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Duração:</Text>
                <Text style={styles.summaryValue}>{formatElapsedTime(finalElapsedTime)}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <FontAwesome name="male" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>DISTÂNCIA:</Text>
                <Text style={styles.summaryValue}>{formatDistance(totalDistance)}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <DoveIcon size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Contactos:</Text>
                <Text style={styles.summaryValue}>{contactsCount}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <FontAwesome name="users" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Observadores:</Text>
                <Text style={styles.summaryValue}>{observersCount || 1}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <FontAwesome name="calendar" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Início:</Text>
                <Text style={styles.summaryValue}>{startTime.toLocaleTimeString('pt-PT', { hour: '2-digit', minute: '2-digit' })}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <FontAwesome name="calendar" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Fim:</Text>
                <Text style={styles.summaryValue}>{sessionEndTime?.toLocaleTimeString('pt-PT', { hour: '2-digit', minute: '2-digit' }) || '--:--'}</Text>
              </View>
              
              {/* Images row with clickable button if images exist */}
              <View style={styles.summaryRow}>
                <FontAwesome name="camera" size={16} color="#666" style={styles.summaryIcon} />
                <Text style={styles.summaryLabel}>Fotos:</Text>
                {contactImages.length > 0 ? (
                  <TouchableOpacity onPress={handleViewImages} style={styles.imageButton}>
                    <Text style={styles.imageButtonText}>{contactImages.length} foto{contactImages.length !== 1 ? 's' : ''}</Text>
                    <FontAwesome name="eye" size={14} color="#0996a8" style={{ marginLeft: 4 }} />
                  </TouchableOpacity>
                ) : (
                  <Text style={styles.summaryValue}>0</Text>
                )}
              </View>
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton, { alignSelf: 'center', minWidth: 120 }]}
                onPress={handleSendToDatabase}
                activeOpacity={0.7}
                disabled={isSendingData}
              >
                {isSendingData ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <>
                    <FontAwesome name="check" size={16} color="#fff" style={styles.buttonIcon} />
                    <Text style={styles.confirmButtonText}>OK</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      <CustomAlert
        visible={isVisible}
        type={config.type}
        title={config.title}
        message={config.message}
        onClose={hideAlert}
        onConfirm={config.onConfirm}

      />

      <ContactPlacementModal
        visible={showContactModal}
        onClose={handleContactModalClose}
        onConfirm={handleLocationPlacementConfirm}
        userLocation={location}
        currentBearing={currentCamera.current?.heading || lastBearing.current || 0}
        currentZoom={currentZoomLevel}
        currentMapType={mapType}
      />

      {/* Contact Details Modal - handle contact details within modal context */}
      {currentContactData && (
        <ContactDetailsModal
          visible={showContactDetailsModal}
          onClose={handleContactDetailsModalClose}
          onSubmit={handleContactDetailsSubmit}
          distance={currentContactData.distance}
          bearing={currentContactData.bearing}
          showAlert={showAlert}
        />
      )}

      {/* Upload Progress Modal for Contact Images - RE-ENABLED WITH PROPER CONDITIONS */}
      <Modal
        visible={isUploadingContactImages}
        transparent={true}
        animationType="fade"
        onRequestClose={() => {}}
      >
        <View style={styles.progressModalContainer}>
          <View style={styles.progressModalContent}>
            <View style={styles.progressModalHeader}>
              <FontAwesome name="cloud-upload" size={32} color="#0996a8" />
              <Text style={styles.progressModalTitle}>
                {uploadMessage || 'A carregar fotos...'}
              </Text>
            </View>
            
            <View style={styles.progressBarContainer}>
              <View style={[styles.progressBar, { width: `${contactUploadProgress}%` }]} />
            </View>
            
            <Text style={styles.progressText}>
              {Math.round(contactUploadProgress)}%
            </Text>
            
            <ActivityIndicator 
              size="large" 
              color="#0996a8" 
              style={styles.progressSpinner}
            />
          </View>
        </View>
      </Modal>

      {/* Image Viewer Modal */}
      <Modal
        visible={showImageViewer}
        transparent={true}
        animationType="fade"
        onRequestClose={closeImageViewer}
      >
        <View style={styles.imageViewerContainer}>
          <View style={styles.imageViewerHeader}>
            <TouchableOpacity
              style={styles.imageViewerCloseButton}
              onPress={closeImageViewer}
            >
              <FontAwesome name="times" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
          
          {contactImages.length > 1 && (
            <>
              <TouchableOpacity 
                style={styles.imageViewerNavLeft}
                onPress={prevImage}
                disabled={currentImageIndex === 0}
              >
                <FontAwesome name="chevron-left" size={24} color={currentImageIndex === 0 ? "#666" : "#FFFFFF"} />
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.imageViewerNavRight}
                onPress={nextImage}
                disabled={currentImageIndex === contactImages.length - 1}
              >
                <FontAwesome name="chevron-right" size={24} color={currentImageIndex === contactImages.length - 1 ? "#666" : "#FFFFFF"} />
              </TouchableOpacity>
            </>
          )}
          
          <View style={styles.imageViewerImageContainer}>
            {contactImages.length > 0 && (
              <LoadingImage
                source={{ uri: contactImages[currentImageIndex] }}
                style={styles.imageViewerImage}
                placeholderIcon="image"
                placeholderIconSize={48}
                placeholderIconColor="#ffffff"
                containerStyle={{ backgroundColor: 'transparent' }}
              />
            )}
          </View>
          
          {contactImages.length > 1 && (
            <View style={styles.imageViewerCounter}>
              <Text style={styles.imageViewerCounterText}>
                {currentImageIndex + 1} de {contactImages.length}
              </Text>
            </View>
          )}
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0996a8',
  },
  header: {
    backgroundColor: '#0996a8',
  },
  titleBar: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    height: 45,
    paddingTop: 0,
    position: 'relative',
  },
  titleText: {
    fontSize: 15,
    color: '#666',
    fontWeight: '500',
  },
  subtitleText: {
    fontSize: 10,
    color: '#999',
    marginTop: 1,
  },
  mapContainer: {
    flex: 1,
    backgroundColor: '#0996a8',
  },
  map: {
    flex: 1,
    marginTop: 0,
  },
  mapLoadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    zIndex: 1000,
    elevation: 1000,
  },
  noMapContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0996a8',
  },
  loadingCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    width: '85%',
    maxWidth: 340,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  logoContainer: {
    marginBottom: 16,
  },
  noMapText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
    textAlign: 'center',
  },
  loadingIndicatorContainer: {
    marginTop: 16,
  },
  webContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapControlsContainer: {
    position: 'absolute',
    right: 10,
    top: 20,
    backgroundColor: 'transparent',
    alignItems: 'center',
    zIndex: 999,
  },
  mapControlButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    width: 40,
    height: 40,
    borderRadius: 2,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 1,
    shadowRadius: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  locationStatusContainer: {
    position: 'absolute',
    top: 20,
    left: 10,
    right: 10,
    zIndex: 999,
  },
  locationStatusCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderLeftWidth: 4,
    borderLeftColor: '#ff9500',
  },
  locationStatusText: {
    color: '#ff9500',
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
    marginLeft: 8,
  },
  bottomControls: {
    backgroundColor: '#0996a8',
    paddingTop: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  statsContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    flex: 1,
    marginRight: 20,
  },
  statRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 0,
  },
  statIcon: {
    marginRight: 8,
    color: '#fff',
    width: 14,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: 9,
    color: 'rgba(255, 255, 255, 0.9)',
    marginRight: 0,
    minWidth: 45,
    fontWeight: '400',
  },
  statValue: {
    fontSize: 11,
    fontWeight: '500',
    color: '#fff',
  },
  terminateButton: {
    backgroundColor: '#dc3545',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  terminateIcon: {
    marginRight: 8,
  },
  terminateText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    width: '85%',
    maxWidth: 340,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 12,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#6B7280',
  },
  confirmButton: {
    backgroundColor: '#0996a8',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    alignItems: 'center',
    gap: 10,
  },
  button: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  reportButton: {
    backgroundColor: '#0996a8',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  summaryModalContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    width: '90%',
    maxWidth: 380,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  summaryContent: {
    marginBottom: 24,
  },
  summaryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 8,
  },
  summaryIcon: {
    marginRight: 12,
    width: 20,
    textAlign: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    flex: 1,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#0996a8',
    textAlign: 'right',
  },
  progressModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressModalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    width: '85%',
    maxWidth: 340,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  progressModalHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  progressModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 12,
    textAlign: 'center',
  },
  progressBarContainer: {
    backgroundColor: '#E0E0E0',
    borderRadius: 8,
    height: 20,
    marginBottom: 12,
  },
  progressBar: {
    backgroundColor: '#0996a8',
    borderRadius: 8,
    height: '100%',
  },
  progressText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  progressSpinner: {
    marginTop: 16,
  },
  observerMarker: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#3B82F6', // Blue color similar to webadmin
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  observerNumber: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  contactMarker: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#10B981', // Green color similar to webadmin
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  // Distance validation styles
  distanceWarning: {
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#f87171',
    paddingHorizontal: 2,
    paddingVertical: 0,
    borderRadius: 3,
    marginLeft: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  distanceWarningText: {
    color: '#dc2626',
    fontSize: 10,
    fontWeight: '500',
  },
  distanceSuccess: {
    backgroundColor: '#f0fdf4',
    borderWidth: 1,
    borderColor: '#86efac',
    paddingHorizontal: 2,
    paddingVertical: 0,
    borderRadius: 3,
    marginLeft: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  distanceSuccessText: {
    color: '#16a34a',
    fontSize: 10,
    fontWeight: '500',
  },
  // Image button styles
  imageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#0ea5e9',
  },
  imageButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#0996a8',
  },
  // Image viewer styles
  imageViewerContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageViewerHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
    padding: 20,
    paddingTop: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  imageViewerCloseButton: {
    alignSelf: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageViewerNavLeft: {
    position: 'absolute',
    left: 20,
    top: '50%',
    marginTop: -22,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  imageViewerNavRight: {
    position: 'absolute',
    right: 20,
    top: '50%',
    marginTop: -22,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  imageViewerImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  imageViewerImage: {
    width: Dimensions.get('window').width - 40,
    height: Dimensions.get('window').height - 100,
    resizeMode: 'contain',
  },
  imageViewerCounter: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  imageViewerCounterText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
}); 