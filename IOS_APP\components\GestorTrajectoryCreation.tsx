import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, ActivityIndicator, Image, TextInput, Platform, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TecnicoProtocol } from '@/types/reports';
import { WeatherData, weatherService } from '@/services/weatherService';
import { FontAwesome } from '@expo/vector-icons';
import { useNetwork } from '@/contexts/NetworkContext';
import * as Location from 'expo-location';
import { useAuth } from '@/contexts/AuthContext';
// Firebase imports removed - documents are only created when monitoring completes
import GestorActiveMonitoring from './GestorActiveMonitoring';
import ContactPlacementModal from './ContactPlacementModal';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';

// Portuguese translations for gestor trajectory creation
const pt = {
  iniciar: 'Iniciar',
  weatherConditions: 'Indique as condições meteorológicas',
  fetchingWeather: 'A obter dados meteorológicos...',
  weatherError: 'Erro ao obter dados meteorológicos',
  currentWeather: 'Condições Atuais',
  manualWeatherSelection: 'Seleção Manual das Condições',
  temperature: 'Temperatura',
  humidity: 'Humidade',
  windSpeed: 'Vento',
  pressure: 'Pressão',
  visibility: 'Visibilidade',
  cloudiness: 'Nebulosidade',
  observersCount: 'Indique o número de observadores',
  observersCountPlaceholder: '',
  monitoringMessage: 'Está em condições de iniciar a monitorização e reportar?',
  weatherOptions: {
    ceuLimpo: 'Céu limpo',
    nublado: 'Nublado',
    chuva: 'Chuva',
    ventoModeradoForte: 'Vento moderado a forte',
    outroQual: 'Outro. Qual?',
  },
  createTrajectory: 'Criar Trajeto',
  cancel: 'Cancelar',
};

interface GestorTrajectoryCreationProps {
  visible: boolean;
  zoneId: string;
  zoneName: string;
  trajectoryToRedoId?: string | null;
  onClose: () => void;
}

const GestorTrajectoryCreation: React.FC<GestorTrajectoryCreationProps> = ({
  visible,
  zoneId,
  zoneName,
  trajectoryToRedoId,
  onClose
}) => {
  const { user } = useAuth();
  const { hasInternetConnection } = useNetwork();
  const scrollViewRef = useRef<ScrollView>(null);

  // Step management - only weather and monitoring for gestores
  const [currentStep, setCurrentStep] = useState<'weather' | 'monitoring'>('weather');
  
  // Protocol is always 'trajeto' for gestores
  const selectedProtocol: TecnicoProtocol = 'trajeto';
  
  // Track if this is a redo operation
  const isRedoOperation = trajectoryToRedoId !== null && trajectoryToRedoId !== undefined;
  
  // Weather data
  const [currentWeatherData, setCurrentWeatherData] = useState<WeatherData | null>(null);
  const [isLoadingWeather, setIsLoadingWeather] = useState(false);
  const [selectedWeatherOptions, setSelectedWeatherOptions] = useState<Record<string, boolean>>({
    ceuLimpo: false,
    nublado: false,
    chuva: false,
    ventoModeradoForte: false,
    outroQual: false,
  });
  const [weatherOtherText, setWeatherOtherText] = useState('');
  
  // Observers count
  const [observersCount, setObserversCount] = useState('1');

  // Monitoring data
  const [monitoringStartTime, setMonitoringStartTime] = useState<Date | null>(null);

  // Contact functionality - reuse existing contact system
  const router = useRouter();
  const [showContactModal, setShowContactModal] = useState(false);
  const [currentContactData, setCurrentContactData] = useState<{
    observerLocation: {latitude: number, longitude: number};
    contactLocation: {latitude: number, longitude: number};
    distance: number;
    bearing: number;
  } | null>(null);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [currentMapType, setCurrentMapType] = useState<'standard' | 'satellite'>('standard');
  const [currentZoomLevel, setCurrentZoomLevel] = useState(17);
  const [currentBearing, setCurrentBearing] = useState(0);
  
  // Contact markers state - for displaying contacts on map
  const [contactMarkers, setContactMarkers] = useState<Array<{
    id: string;
    coordinate: { latitude: number; longitude: number };
    observerLocation: { latitude: number; longitude: number };
    contactNumber: number;
    timestamp: string;
  }>>([]);
  const [contactsCount, setContactsCount] = useState(0);
  const [monitoringSessionId] = useState(() => `gestor_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  // Reset state when modal opens (only when first opening, not when returning from navigation)
  useEffect(() => {
    if (visible && currentStep === 'weather' && !monitoringStartTime) {
      // Only reset if we're in the initial weather step and haven't started monitoring yet
      setCurrentWeatherData(null);
      setSelectedWeatherOptions({
        ceuLimpo: false,
        nublado: false,
        chuva: false,
        ventoModeradoForte: false,
        outroQual: false,
      });
      setWeatherOtherText('');
      setObserversCount('1');
      
      // Reset contact-related state only if not monitoring
      setShowContactModal(false);
      setCurrentContactData(null);
      setUserLocation(null);
      setCurrentMapType('standard');
      setCurrentZoomLevel(17);
      setCurrentBearing(0);
      
      // Fetch weather data if we have internet
      if (hasInternetConnection()) {
        fetchWeatherData();
      }
    }
  }, [visible, hasInternetConnection, currentStep, monitoringStartTime]);

  // Reset to weather step only when modal is completely closed and reopened
  useEffect(() => {
    if (!visible) {
      console.log('🔄 GESTOR_TRAJECTORY: Modal became invisible - resetting to weather step');
      // When modal closes, reset to weather step for next time
      setCurrentStep('weather');
      setMonitoringStartTime(null);
    } else {
      console.log('🔄 GESTOR_TRAJECTORY: Modal became visible - currentStep:', currentStep);
    }
  }, [visible]);

  // Track user location for contact placement
  useEffect(() => {
    let locationSubscription: Location.LocationSubscription | undefined;

    const startLocationTracking = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          console.log('Location permission denied');
          return;
        }

        // Get initial location
        const initialLocation = await Location.getCurrentPositionAsync({});
        setUserLocation(initialLocation);

        // Start watching location changes
        locationSubscription = await Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.High,
            timeInterval: 2000,
            distanceInterval: 5,
          },
          (location) => {
            setUserLocation(location);
          }
        );
      } catch (error) {
        console.error('Error starting location tracking:', error);
      }
    };

    if (visible && currentStep === 'monitoring') {
      startLocationTracking();
    }

    return () => {
      if (locationSubscription) {
        locationSubscription.remove();
      }
    };
  }, [visible, currentStep]);

  const fetchWeatherData = async () => {
    setIsLoadingWeather(true);
    try {
      // Get user's location first
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission denied');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const weather = await weatherService.getCurrentWeather(
        location.coords.latitude,
        location.coords.longitude
      );
      
      if (weather) {
        setCurrentWeatherData(weather);
      }
    } catch (error) {
      console.error('Error fetching weather:', error);
    } finally {
      setIsLoadingWeather(false);
    }
  };

  const handleStartMonitoring = async () => {
    if (!user) return;

    // Validate observers count
    if (!observersCount || observersCount.trim() === '' || observersCount === '0') {
      alert('Por favor, indique o número de observadores antes de iniciar.');
      return;
    }

    // Validate weather conditions when offline
    if (!hasInternetConnection()) {
      const hasSelectedWeatherOption = Object.values(selectedWeatherOptions).some(value => value === true);
      if (!hasSelectedWeatherOption) {
        alert('Por favor, selecione as condições meteorológicas antes de iniciar.');
        return;
      }
      
      if (selectedWeatherOptions.outroQual && (!weatherOtherText || weatherOtherText.trim() === '')) {
        alert('Por favor, especifique as condições meteorológicas.');
        return;
      }
    }

    // Just start monitoring - don't create document until completion
    try {
      const startTime = new Date();
      setMonitoringStartTime(startTime);
      setCurrentStep('monitoring');
    } catch (error) {
      console.error('Error starting monitoring:', error);
      alert('Erro ao iniciar monitorização. Tente novamente.');
    }
  };

  const handleWeatherOptionToggle = (option: string) => {
    if (!hasInternetConnection()) {
      // For offline mode, implement single selection
      const wasNothingSelected = !Object.values(selectedWeatherOptions).some(value => value === true);
      const isCurrentlySelected = selectedWeatherOptions[option];
      
      if (wasNothingSelected && !isCurrentlySelected) {
        const resetOptions = Object.keys(selectedWeatherOptions).reduce((acc, key) => {
          acc[key] = false;
          return acc;
        }, {} as Record<string, boolean>);
        
        setSelectedWeatherOptions({
          ...resetOptions,
          [option]: true
        });
      } else if (isCurrentlySelected) {
        const resetOptions = Object.keys(selectedWeatherOptions).reduce((acc, key) => {
          acc[key] = false;
          return acc;
        }, {} as Record<string, boolean>);
        
        setSelectedWeatherOptions(resetOptions);
        
        if (option === 'outroQual') {
          setWeatherOtherText('');
        }
      } else {
        const resetOptions = Object.keys(selectedWeatherOptions).reduce((acc, key) => {
          acc[key] = false;
          return acc;
        }, {} as Record<string, boolean>);
        
        setSelectedWeatherOptions({
          ...resetOptions,
          [option]: true
        });
        
        if (option !== 'outroQual') {
          setWeatherOtherText('');
        }
      }
    } else {
      // For online mode, keep the original multi-selection behavior
      setSelectedWeatherOptions(prev => ({
        ...prev,
        [option]: !prev[option]
      }));
      
      if (option !== 'outroQual') {
        setWeatherOtherText('');
      }
    }
  };

  const handleWeatherOtherTextChange = (text: string) => {
    setWeatherOtherText(text);
  };

  const handleObserversCountChange = (text: string) => {
    setObserversCount(text);
  };

  const handleMonitoringTerminate = () => {
    console.log('🔄 GESTOR_TRAJECTORY: Monitoring terminated, closing modal');
    // When monitoring terminates, close the modal
    onClose();
  };

  // Contact functionality handlers
  const handleContactButtonPress = () => {
    console.log('🔄 GESTOR_TRAJECTORY: Contact button pressed');
    console.log('🔄 GESTOR_TRAJECTORY: User location:', userLocation ? 'Available' : 'Not available');
    if (userLocation) {
      console.log('🔄 GESTOR_TRAJECTORY: Opening contact modal');
      setShowContactModal(true);
    } else {
      console.log('🔄 GESTOR_TRAJECTORY: Cannot open contact modal - no user location');
    }
  };

  const handleContactModalClose = () => {
    console.log('🔄 GESTOR_TRAJECTORY: Contact modal closed');
    setShowContactModal(false);
    setCurrentContactData(null);
  };

  // Calculate bearing (direction) from observer to contact in degrees
  const calculateBearing = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δλ = (lng2-lng1) * Math.PI/180;

    const x = Math.sin(Δλ) * Math.cos(φ2);
    const y = Math.cos(φ1) * Math.sin(φ2) - Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);

    const θ = Math.atan2(x, y);
    
    // Convert to degrees and normalize to 0-360
    const bearing = (θ * 180/Math.PI + 360) % 360;
    
    return Math.round(bearing);
  };

  // Handle location placement confirmation - move to details step
  const handleLocationPlacementConfirm = async (observerLocation: {latitude: number, longitude: number}, contactLocation: {latitude: number, longitude: number}, distance: number) => {
    // Calculate bearing from observer to contact location
    const bearing = calculateBearing(
      observerLocation.latitude,
      observerLocation.longitude,
      contactLocation.latitude,
      contactLocation.longitude
    );
    
    // Store the contact data
    setCurrentContactData({
      observerLocation,
      contactLocation,
      distance,
      bearing
    });
    
    // Create current monitoring session for contact-details screen
    try {
      const currentSession = {
        sessionId: monitoringSessionId,
        protocol: selectedProtocol,
        startTime: monitoringStartTime?.toISOString(),
        userId: user?.uid,
        deviceInfo: {
          platform: Platform.OS,
        }
      };
      
      await AsyncStorage.setItem('currentMonitoringSession', JSON.stringify(currentSession));
      console.log('✅ Current monitoring session created for contact-details');
    } catch (error) {
      console.error('Error creating current monitoring session:', error);
    }
    
    // Close location modal and navigate to details screen
    setShowContactModal(false);
    console.log('🔄 GESTOR_TRAJECTORY: Navigating to contact-details with source: gestor_monitoring');
    console.log('🔄 GESTOR_TRAJECTORY: Current step:', currentStep);
    console.log('🔄 GESTOR_TRAJECTORY: Modal visible:', visible);
    console.log('🔄 GESTOR_TRAJECTORY: About to call router.push...');
    
    try {
      router.push({
        pathname: '/contact-details',
        params: {
          distance: distance.toString(),
          bearing: bearing.toString(),
          observerLat: observerLocation.latitude.toString(),
          observerLng: observerLocation.longitude.toString(),
          contactLat: contactLocation.latitude.toString(),
          contactLng: contactLocation.longitude.toString(),
          source: 'gestor_monitoring', // Add source parameter
        }
      });
      console.log('🔄 GESTOR_TRAJECTORY: router.push completed successfully');
    } catch (error) {
      console.error('🔄 GESTOR_TRAJECTORY: Error in router.push:', error);
    }
  };

  // Load contact markers for current session
  const loadContactMarkers = async () => {
    try {
      const contactEvents = await AsyncStorage.getItem('gestorOfflineContactEvents');
      if (contactEvents) {
        const events = JSON.parse(contactEvents);
        const sessionEvents = events.filter((event: any) => event.sessionId === monitoringSessionId);
        
        const markers = sessionEvents.map((event: any) => ({
          id: `${event.sessionId}_${event.contactNumber}`,
          coordinate: event.contactLocation,
          observerLocation: event.observerLocation,
          contactNumber: event.contactNumber,
          timestamp: event.timestamp,
        }));
        
        setContactMarkers(markers);
        setContactsCount(sessionEvents.length);
        console.log(`📍 Loaded ${markers.length} contact markers for gestor session`);
      }
    } catch (error) {
      console.error('Error loading contact markers:', error);
    }
  };

  // Handle focus effect to reload contacts when returning from contact-details
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 GESTOR_TRAJECTORY: useFocusEffect triggered');
      console.log('🔄 GESTOR_TRAJECTORY: visible:', visible, 'currentStep:', currentStep);
      
      if (visible && currentStep === 'monitoring') {
        console.log('🔄 GESTOR_TRAJECTORY: Reloading contact markers');
        // Reload contact markers when modal is visible and in monitoring mode
        loadContactMarkers();
      }
    }, [visible, currentStep, monitoringSessionId])
  );

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      statusBarTranslucent={true}
    >
      <View style={styles.container}>
        <StatusBar style="light" />
        {currentStep === 'monitoring' && monitoringStartTime ? (
          <GestorActiveMonitoring
            protocol={selectedProtocol}
            startTime={monitoringStartTime}
            weatherData={currentWeatherData}
            observersCount={parseInt(observersCount)}
            zoneId={zoneId}
            zoneName={zoneName}
            trajectoryToRedoId={trajectoryToRedoId}
            onTerminate={handleMonitoringTerminate}
            initialContactMarkers={contactMarkers}
            initialContactsCount={contactsCount}
          />
        ) : (
          <>
            {/* Header - new edge-to-edge design */}
            <View style={styles.header}>
              <SafeAreaView edges={['top']} style={styles.headerSafeArea}>
                <View style={styles.headerContent}>
                  <Image 
                    source={require('../assets/images/header-logo.png')} 
                    style={styles.logo}
                    resizeMode="contain"
                  />
                  <Text style={styles.title}>
                    {isRedoOperation ? 'Refazer Trajeto GPS' : 'Criar Trajeto'}
                  </Text>
                </View>
              </SafeAreaView>
            </View>

            {/* Content wrapper */}
            <View style={styles.contentWrapper}>
              <ScrollView 
                ref={scrollViewRef}
                style={styles.scrollContent}
                contentContainerStyle={styles.scrollContentContainer}
                showsVerticalScrollIndicator={false}
                bounces={true}
                keyboardShouldPersistTaps="handled"
              >
                <WeatherConditions
                  weatherData={currentWeatherData}
                  isLoadingWeather={isLoadingWeather}
                  hasInternet={hasInternetConnection()}
                  selectedWeatherOptions={selectedWeatherOptions}
                  onWeatherOptionToggle={handleWeatherOptionToggle}
                  weatherOtherText={weatherOtherText}
                  onWeatherOtherTextChange={handleWeatherOtherTextChange}
                  observersCount={observersCount}
                  onObserversCountChange={handleObserversCountChange}
                />
              </ScrollView>
            </View>

            {/* Fixed buttons at bottom */}
            <SafeAreaView edges={['bottom']} style={styles.buttonSafeArea}>
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={onClose}
                  activeOpacity={0.7}
                >
                  <FontAwesome name="times" size={16} color="#fff" style={{ marginRight: 8 }} />
                  <Text style={styles.cancelButtonText}>{pt.cancel}</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.continueButton}
                  onPress={handleStartMonitoring}
                  activeOpacity={0.7}
                >
                  <FontAwesome name="play" size={16} color="#fff" style={{ marginRight: 8 }} />
                  <Text style={styles.continueButtonText}>
                    {isRedoOperation ? 'Refazer Trajeto' : pt.iniciar}
                  </Text>
                </TouchableOpacity>
              </View>
            </SafeAreaView>
          </>
        )}
      </View>

      {/* Contact Placement Modal */}
      <ContactPlacementModal
        visible={showContactModal}
        onClose={handleContactModalClose}
        onConfirm={handleLocationPlacementConfirm}
        userLocation={userLocation}
        currentBearing={currentBearing}
        currentZoom={currentZoomLevel}
        currentMapType={currentMapType}
      />
    </Modal>
  );
};

// Weather Conditions Component - copied exactly from TecnicosReport
const WeatherConditions: React.FC<{
  weatherData: WeatherData | null;
  isLoadingWeather: boolean;
  hasInternet: boolean;
  selectedWeatherOptions: Record<string, boolean>;
  onWeatherOptionToggle: (option: string) => void;
  weatherOtherText: string;
  onWeatherOtherTextChange: (text: string) => void;
  observersCount: string;
  onObserversCountChange: (text: string) => void;
}> = ({ 
  weatherData, 
  isLoadingWeather, 
  hasInternet, 
  selectedWeatherOptions, 
  onWeatherOptionToggle,
  weatherOtherText,
  onWeatherOtherTextChange,
  observersCount,
  onObserversCountChange
}) => {
  const weatherOptions = [
    { key: 'ceuLimpo', label: pt.weatherOptions.ceuLimpo },
    { key: 'nublado', label: pt.weatherOptions.nublado },
    { key: 'chuva', label: pt.weatherOptions.chuva },
    { key: 'ventoModeradoForte', label: pt.weatherOptions.ventoModeradoForte },
    { key: 'outroQual', label: pt.weatherOptions.outroQual },
  ];

  // Helper function to get selected weather option
  const getSelectedWeatherOption = () => {
    return Object.entries(selectedWeatherOptions).find(([key, value]) => value === true)?.[0];
  };

  return (
    <View style={styles.weatherContainer}>
      {/* Title outside the card - matching Tecnicos layout */}
      <Text style={styles.sectionTitle}>
        Condições Meteorológicas
      </Text>

      {hasInternet && isLoadingWeather && (
        <View style={styles.weatherCard}>
          <View style={styles.weatherHeader}>
            <View style={styles.weatherIconContainer}>
              <ActivityIndicator size="small" color="rgba(255, 255, 255, 0.8)" />
            </View>
            <View style={styles.weatherMainInfo}>
              <View style={styles.loadingPlaceholder}>
                <ActivityIndicator size="small" color="rgba(255, 255, 255, 0.8)" />
                <Text style={styles.loadingPlaceholderText}>{pt.fetchingWeather}</Text>
              </View>
            </View>
          </View>

          <View style={styles.weatherDetailsGrid}>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.humidity}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.windSpeed}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.pressure}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.visibility}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
          </View>
        </View>
      )}

      {hasInternet && !isLoadingWeather && weatherData && (
        <View style={styles.weatherCard}>
          <View style={styles.weatherHeader}>
            <View style={styles.weatherIconContainer}>
              <Image
                source={{ uri: weatherService.getWeatherIconUrl(weatherData.icon) }}
                style={styles.weatherIconImage}
                resizeMode="contain"
              />
            </View>
            <View style={styles.weatherMainInfo}>
              <Text style={styles.weatherTemperature}>{weatherData.temperature}°C</Text>
              <Text style={styles.weatherDescription}>{weatherData.description}</Text>
            </View>
          </View>
        </View>
      )}

      {/* Weather details grid outside the main card - matching Tecnicos layout */}
      {hasInternet && !isLoadingWeather && weatherData && (
        <View style={styles.weatherDetailsGrid}>
          <View style={styles.weatherDetailItem}>
            <FontAwesome name="tint" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
            <Text style={styles.weatherDetailLabel}>{pt.humidity}</Text>
            <Text style={styles.weatherDetailValue}>{weatherData.humidity}%</Text>
          </View>
          <View style={styles.weatherDetailItem}>
            <FontAwesome name="flag" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
            <Text style={styles.weatherDetailLabel}>{pt.windSpeed}</Text>
            <Text style={styles.weatherDetailValue}>{weatherData.windSpeed} km/h</Text>
          </View>
          <View style={styles.weatherDetailItem}>
            <FontAwesome name="tachometer" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
            <Text style={styles.weatherDetailLabel}>{pt.pressure}</Text>
            <Text style={styles.weatherDetailValue}>{weatherData.pressure} hPa</Text>
          </View>
          <View style={styles.weatherDetailItem}>
            <FontAwesome name="eye" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
            <Text style={styles.weatherDetailLabel}>{pt.visibility}</Text>
            <Text style={styles.weatherDetailValue}>{weatherData.visibility} km</Text>
          </View>
        </View>
      )}

      {/* Manual weather selection when offline */}
      {!hasInternet && (
        <View>

          <View style={styles.manualWeatherContainer}>
            {(() => {
              const selectedOption = getSelectedWeatherOption();
              
              if (selectedOption) {
                // Show only the selected option
                const selectedWeatherOption = weatherOptions.find(option => option.key === selectedOption);
                if (!selectedWeatherOption) return null;
                
                return (
                  <View key={selectedOption}>
                    <TouchableOpacity
                      style={[styles.weatherOptionRow, styles.weatherOptionSelected]}
                      onPress={() => onWeatherOptionToggle(selectedOption)}
                      activeOpacity={0.7}
                    >
                      <View style={[styles.weatherCheckbox, styles.weatherCheckboxChecked]}>
                        <FontAwesome name="check" size={14} color="#fff" />
                      </View>
                      <Text style={[styles.weatherOptionLabel, styles.weatherOptionLabelSelected]}>
                        {selectedWeatherOption.label}
                      </Text>
                    </TouchableOpacity>
                    
                    {selectedOption === 'outroQual' && (
                      <View style={styles.textInputContainer}>
                        <TextInput
                          style={styles.weatherTextInput}
                          placeholder="Especifique as condições meteorológicas..."
                          placeholderTextColor="#999"
                          value={weatherOtherText}
                          onChangeText={onWeatherOtherTextChange}
                          multiline
                          numberOfLines={3}
                        />
                      </View>
                    )}
                  </View>
                );
              }
              
              // Show all options when none are selected
              return weatherOptions.map(({ key, label }) => (
                <View key={key}>
                  <TouchableOpacity
                    style={[
                      styles.weatherOptionRow,
                      selectedWeatherOptions[key] && styles.weatherOptionSelected
                    ]}
                    onPress={() => onWeatherOptionToggle(key)}
                    activeOpacity={0.7}
                  >
                    <View style={[
                      styles.weatherCheckbox,
                      selectedWeatherOptions[key] && styles.weatherCheckboxChecked
                    ]}>
                      {selectedWeatherOptions[key] && <FontAwesome name="check" size={14} color="#fff" />}
                    </View>
                    <Text style={[
                      styles.weatherOptionLabel,
                      selectedWeatherOptions[key] && styles.weatherOptionLabelSelected
                    ]}>
                      {label}
                    </Text>
                  </TouchableOpacity>
                  
                  {key === 'outroQual' && selectedWeatherOptions[key] && (
                    <View style={styles.textInputContainer}>
                      <TextInput
                        style={styles.weatherTextInput}
                        placeholder="Especifique as condições meteorológicas..."
                        placeholderTextColor="#999"
                        value={weatherOtherText}
                        onChangeText={onWeatherOtherTextChange}
                        multiline
                        numberOfLines={3}
                      />
                    </View>
                  )}
                </View>
              ));
            })()}
          </View>
        </View>
      )}

      {/* Observers Count Section - Always show after weather information */}
      <View style={styles.observersSection}>
        <View style={styles.observersInputContainer}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginBottom: 12 }}>
            <FontAwesome name="users" size={18} style={styles.observersLabelIcon} />
            <Text style={styles.observersLabel}>{pt.observersCount}</Text>
          </View>
          <TextInput
            style={styles.observersInput}
            value={observersCount}
            onChangeText={(text) => {
              // Only allow numbers
              const numericText = text.replace(/[^0-9]/g, '');
              onObserversCountChange(numericText);
            }}
            keyboardType="numeric"
            maxLength={3}
            placeholder="0"
            placeholderTextColor="#999"
          />
        </View>
        
 
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e8f4f8',
  },
  header: {
    backgroundColor: '#0996a8',
  },
  headerSafeArea: {
    backgroundColor: '#0996a8',
    paddingBottom: 10,
    paddingHorizontal: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  logo: {
    width: 80,
    height: 80,
    marginRight: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
    paddingHorizontal: 13,
  },
  contentWrapper: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: '#fff',
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingTop: 15,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 15,
    marginTop: 5,
    textAlign: 'center',
  },
  weatherContainer: {
    marginBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  weatherCard: {
    backgroundColor: '#0996a8',
    borderRadius: 12,
    padding: 10,
    paddingBottom: 20,
    marginBottom: 10,
  },
  weatherHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 0,
  },
  weatherTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginLeft: 8,
  },
  weatherIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  weatherIconImage: {
    width: 28,
    height: 28,
  },
  weatherMainInfo: {
    flex: 1,
  },
  weatherTemperature: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  loadingPlaceholder: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingPlaceholderText: {
    marginLeft: 8,
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  temperatureSection: {
    alignItems: 'center',
    marginBottom: 20,
  },
  temperature: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#fff',
  },
  weatherDescription: {
    fontSize: 16,
    color: '#fff',
    textTransform: 'capitalize',
    opacity: 0.9,
  },
  weatherDetailsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginTop: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  weatherDetailItem: {
    alignItems: 'center',
    flex: 1,
  },
  weatherDetailLabel: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 2,
    textAlign: 'center',
    fontWeight: '500',
  },
  weatherDetailValue: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    textAlign: 'center',
  },
  weatherDetailIcon: {
    marginBottom: 4,
    width: 14,
    height: 14,
    textAlign: 'center',
  },
  weatherError: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
    padding: 20,
  },
  manualWeatherContainer: {
    marginTop: 10,
  },
  weatherOptionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 1,
    elevation: 1,
  },
  weatherCheckbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#d1d5db',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: '#fff',
  },
  weatherCheckboxChecked: {
    backgroundColor: '#0996a8',
    borderColor: '#0996a8',
  },
  weatherOptionLabel: {
    flex: 1,
    fontSize: 15,
    color: '#374151',
    lineHeight: 20,
    fontWeight: '500',
  },
  weatherOptionLabelSelected: {
    fontWeight: '600',
    color: '#0996a8',
  },
  textInputContainer: {
    marginTop: 10,
  },
  weatherTextInput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    minHeight: 72,
    textAlignVertical: 'top',
    backgroundColor: '#fff',
    color: '#374151',
    fontSize: 14,
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 1,
    elevation: 1,
    fontWeight: '500',
  },
  manualWeatherTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 15,
    textAlign: 'center',
  },
  weatherOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 8,
  },
  weatherOptionSelected: {
    backgroundColor: '#f8fafc',
    borderColor: '#0996a8',
  },
  weatherRadio: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#d1d5db',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  weatherRadioSelected: {
    borderColor: '#0996a8',
  },
  weatherRadioDot: {
    width: 10,
    height: 10,
    backgroundColor: '#0996a8',
    borderRadius: 5,
  },
  weatherLabel: {
    fontSize: 15,
    color: '#374151',
    flex: 1,
  },
  weatherLabelSelected: {
    color: '#0996a8',
    fontWeight: '600',
  },
  weatherOtherInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 12,
    fontSize: 15,
    backgroundColor: '#fff',
    marginTop: 8,
    textAlignVertical: 'top',
  },
  observersSection: {
    marginTop: 5,
    marginBottom: 15,
  },
  observersInputContainer: {
    marginBottom: 16,
    backgroundColor: '#ffffff',
    borderRadius: 10,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  observersLabel: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 0,
    fontWeight: '600',
    textAlign: 'center',
    marginLeft: 8,
  },
  observersLabelIcon: {
    marginRight: 8,
    color: '#0996a8',
  },
  observersInput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#f8fafc',
    color: '#374151',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  observersHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  observersTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
  },
  monitoringMessage: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    textAlign: 'left',
    backgroundColor: '#f0f9ff',
    padding: 16,
    borderRadius: 10,
    borderLeftWidth: 3,
    borderLeftColor: '#0996a8',
    fontWeight: '500',
    marginTop: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 1,
    elevation: 1,
  },
  monitoringTextWrapper: {
    alignItems: 'center',
    width: '100%',
  },
  monitoringTextContent: {
    textAlign: 'left',
    maxWidth: '90%',
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    fontWeight: '500',
  },
  monitoringMessageContainer: {
    backgroundColor: '#f0f9ff',
    borderWidth: 1,
    borderColor: '#0996a8',
    borderRadius: 8,
    padding: 16,
    marginTop: 10,
  },
  buttonSafeArea: {
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#fff',
    gap: 15,
    flexShrink: 0,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#6B7280',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  continueButton: {
    flex: 1,
    backgroundColor: '#0996a8',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default GestorTrajectoryCreation; 