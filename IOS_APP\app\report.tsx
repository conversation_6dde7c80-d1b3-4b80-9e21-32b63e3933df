import React, { useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator, Image, Modal, Platform, KeyboardAvoidingView } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { FontAwesome } from '@expo/vector-icons';
import { router } from 'expo-router';
import { CommonActions } from '@react-navigation/native';
import { useNavigation } from '@react-navigation/native';
import ColaboradorReport from '@/components/reports/ColaboradorReport';
import TecnicosReport from '@/components/reports/TecnicosReport';
import ActiveMonitoring from '@/components/ActiveMonitoring';
import { useAuth } from '@/contexts/AuthContext';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import CustomAlert from '@/components/CustomAlert';
import { ColaboradorReportData, TecnicoReportData, ReportCircumstances, ReportContactLocation, TecnicoProtocol } from '@/types/reports';
import { ReportRef } from '@/components/reports/ReportModal';
import { getFirestore, collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from '@/config/firebase';
import * as Location from 'expo-location';
import { useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import SafeSystemBars from '@/components/SafeSystemBars';
import { weatherService, WeatherData, WeatherService } from '@/services/weatherService';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { PendingReport } from '@/types/reports';
import NetworkStatusIndicator from '@/components/NetworkStatusIndicator';
import { useFocusEffect } from '@react-navigation/native';

// Trajectory data interface
interface TrajectoryData {
  id: string;
  name: string;
  description?: string;
  coordinates: Array<{ lat: number; lng: number }>;
  distance?: string;
  pointsCount?: number;
  createdAt: string;
  source: 'kmz' | 'manual';
  originalFileName?: string;
}

// Portuguese translations
const pt = {
  cancel: 'Cancelar',
  submit: 'Continuar',
  iniciar: 'Iniciar',
  newReport: 'Novo relatório',
  reportSubmitted: 'Relatório enviado com sucesso!',
  reportSavedOffline: 'Relatório guardado localmente. Será enviado quando houver conexão à internet.',
  reportError: 'Erro ao enviar relatório',
  locationError: 'Erro ao obter localização',
  gpsRequired: 'GPS é necessário para criar um relatório',
  uploadingImages: 'A enviar fotos...',
  uploadingProgress: 'Carregando foto {current} de {total}',
  offlineMode: 'Modo offline',
};

// Helper function to determine if data is TecnicoReportData
const isTecnicoReportData = (data: ColaboradorReportData | TecnicoReportData): data is TecnicoReportData => {
  return 'protocol' in data;
};

export default function ReportScreen() {
  const { user, userRole } = useAuth();
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const reportRef = useRef<ReportRef>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingImages, setIsUploadingImages] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [shouldNavigateAfterAlert, setShouldNavigateAfterAlert] = useState(false);
  const [selectedProtocol, setSelectedProtocol] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<'protocol' | 'weather' | 'trajectory' | 'monitoring'>('protocol');
  const [isInitializing, setIsInitializing] = useState(true);
  
  // Monitoring state
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [monitoringProtocol, setMonitoringProtocol] = useState<TecnicoProtocol | null>(null);
  const [monitoringStartTime, setMonitoringStartTime] = useState<Date | null>(null);
  const [monitoringWeatherData, setMonitoringWeatherData] = useState<any>(null);
  const [monitoringObserversCount, setMonitoringObserversCount] = useState<number>(1);
  const [selectedTrajectory, setSelectedTrajectory] = useState<TrajectoryData | null>(null);

  // Clean navigation function to prevent layout interference
  const navigateToLocationTab = () => {
    // Use Navigation API to completely reset the stack to tabs
    console.log('🔄 Resetting navigation to location tab...');
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: '(tabs)' }],
      })
    );
  };

  // Check for active monitoring session on component mount
  useEffect(() => {
    const checkActiveMonitoring = async () => {
      try {
        const activeSession = await AsyncStorage.getItem('activeMonitoringSession');
        if (activeSession) {
          const session = JSON.parse(activeSession);
          
          setMonitoringProtocol(session.protocol);
          setMonitoringStartTime(new Date(session.startTime));
          setMonitoringWeatherData(session.weatherData || null);
          setMonitoringObserversCount(session.observersCount || 1);
      
          setSelectedTrajectory(session.selectedTrajectory || null);
          setIsMonitoring(true);
          setCurrentStep('monitoring');
        }
      } catch (error) {
        console.error('Error checking active monitoring session:', error);
      } finally {
        setIsInitializing(false);
      }
    };

    // Wait for user and userRole to be available
    if (user && userRole !== undefined) {
      checkActiveMonitoring();
    } else if (user && userRole === undefined) {
      // User is loaded but role is still undefined, wait a bit more
      const timeout = setTimeout(() => {
        setIsInitializing(false);
      }, 1000);
      return () => clearTimeout(timeout);
    }
  }, [user, userRole]);

  // Add focus effect to ensure proper isolation from tab navigation
  useFocusEffect(
    React.useCallback(() => {
      // Log when report screen becomes focused
      console.log('🔍 Report screen focused');
      
      return () => {
        // Log when report screen loses focus
        console.log('🔍 Report screen unfocused');
      };
    }, [])
  );

  // Function to store report locally when offline
  const storeReportLocally = async (reportData: PendingReport) => {
    try {
      const pendingReports = await AsyncStorage.getItem('pendingReports');
      const reports = pendingReports ? JSON.parse(pendingReports) : [];
      reports.push(reportData);
      await AsyncStorage.setItem('pendingReports', JSON.stringify(reports));
      console.log('Report stored locally for later sync');
    } catch (error) {
      console.error('Error storing report locally:', error);
      throw error;
    }
  };

  const uploadImageToStorage = async (imageUri: string, index: number, total: number): Promise<string> => {
    try {
      setUploadProgress((index / total) * 100);
      
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      // Include user ID in the path to match Firebase Storage rules
      const filename = `report_images/${user?.uid}/${Date.now()}_${index}.jpg`;
      const imageRef = ref(storage, filename);
      
      const snapshot = await uploadBytes(imageRef, blob);
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      return downloadURL;
    } catch (error) {
      console.error(`Error uploading image ${index}:`, error);
      throw error;
    }
  };

  const handleSubmit = async (data: ColaboradorReportData | TecnicoReportData) => {
    if (!user) {
      showAlert({
        type: 'error',
        message: 'Utilizador não autenticado',
      });
      return;
    }

    try {
      setIsLoading(true);

      // Validate data structure before proceeding
      if (!isTecnicoReportData(data)) {
        // Validate colaborador report data
        if (!data.circumstances || !data.contactLocation) {
          console.error('Invalid colaborador report data:', data);
          showAlert({
            type: 'error',
            message: 'Dados do relatório inválidos. Por favor, tente novamente.',
          });
          setIsLoading(false);
          return;
        }

        // Ensure all required fields are present and not undefined
        const requiredCircumstanceFields = [
          'rolaAdultaCantando', 'rolaEmVoo', 'adultoPousado', 'adultoEmDisplay',
          'ninhoVazio', 'nichoOcupado', 'ovos', 'adultoAIncubar', 'crias', 
          'juvenile', 'outraQual', 'outraQualText'
        ];

        const requiredContactLocationFields = [
          'arvore', 'arbusto', 'pontoDeAgua', 'clareira', 'parcelaAgricola',
          'outraQual', 'outraQualText'
        ];

        // Check for undefined values in circumstances
        for (const field of requiredCircumstanceFields) {
          if (data.circumstances[field as keyof ReportCircumstances] === undefined) {
            console.error(`Undefined field in circumstances: ${field}`);
            showAlert({
              type: 'error',
              message: 'Dados do relatório incompletos. Por favor, tente novamente.',
            });
            setIsLoading(false);
            return;
          }
        }

        // Check for undefined values in contactLocation
        for (const field of requiredContactLocationFields) {
          if (data.contactLocation[field as keyof ReportContactLocation] === undefined) {
            console.error(`Undefined field in contactLocation: ${field}`);
            showAlert({
              type: 'error',
              message: 'Dados do relatório incompletos. Por favor, tente novamente.',
            });
            setIsLoading(false);
            return;
          }
        }
      }

      // Get current location
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        showAlert({
          type: 'error',
          message: pt.gpsRequired,
        });
        setIsLoading(false);
        return;
      }

      const location = await Location.getCurrentPositionAsync({});

      // Get weather data for the current location
      let currentWeatherData: WeatherData | null = null;
      try {
        currentWeatherData = await weatherService.getCurrentWeather(
          location.coords.latitude,
          location.coords.longitude
        );
        setWeatherData(currentWeatherData);
      } catch (weatherError) {
        console.warn('Weather data not available:', weatherError);
        // Continue without weather data
      }

      // Check network status
      const netInfo = await NetInfo.fetch();
      
      if (!netInfo.isConnected) {
        // Store locally when offline
        const pendingReportData: PendingReport = {
          syncId: `${user.uid}_${Date.now()}`,
          userId: user.uid,
          userName: user.displayName || user.email || 'Utilizador',
          userEmail: user.email || '',
          type: isTecnicoReportData(data) ? 'tecnico_report' : 'colaborador_report',
          location: {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          comment: '',
          images: data.images,
          localImageUris: data.images,
          createdAt: new Date().toISOString(),
          deviceInfo: {
            platform: Platform.OS,
          }
        };

        // Add type-specific fields
        if (isTecnicoReportData(data)) {
          pendingReportData.tecnicoProtocol = data.protocol;
          pendingReportData.tecnicoHabitat = data.habitat;
          pendingReportData.tecnicoMetrics = data.metrics;
        } else {
          // Create clean copies to avoid any reference issues
          pendingReportData.circumstances = { ...data.circumstances };
          pendingReportData.contactLocation = { ...data.contactLocation };
        }

        if (currentWeatherData) {
          const sanitizedWeather = WeatherService.sanitizeWeatherForFirestore(currentWeatherData);
          if (sanitizedWeather) {
            pendingReportData.weather = sanitizedWeather;
          }
        }

        await storeReportLocally(pendingReportData);

        showAlert({
          type: 'info',
          message: pt.reportSavedOffline,
          onConfirm: () => {
            navigateToLocationTab();
          }
        });

        setIsLoading(false);
        return;
      }

      // Upload images if any
      let imageUrls: string[] = [];
      if (data.images && data.images.length > 0) {
        setIsUploadingImages(true);
        
        for (let i = 0; i < data.images.length; i++) {
          const imageUrl = await uploadImageToStorage(data.images[i], i + 1, data.images.length);
          imageUrls.push(imageUrl);
        }
        
        setIsUploadingImages(false);
        setUploadProgress(0);
      }

      // Prepare report data based on type
      const reportData: any = {
        userId: user.uid,
        userName: user.displayName || user.email || 'Utilizador',
        userEmail: user.email || '',
        userRole: userRole || 'colaborador',
        images: imageUrls,
        location: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        },
        deviceInfo: {
          platform: Platform.OS,
        },
        createdAt: serverTimestamp(),
      };

      // Add type-specific fields
      if (isTecnicoReportData(data)) {
        reportData.type = 'tecnico_report';
        reportData.reportName = data.reportName;
        reportData.protocol = data.protocol;
        reportData.habitat = data.habitat;
        reportData.metrics = data.metrics;
      } else {
        reportData.type = 'colaborador_report';
        // Create clean copies to avoid any reference issues
        reportData.circumstances = { ...data.circumstances };
        reportData.contactLocation = { ...data.contactLocation };
      }

      // Only include weather data if it exists and is valid
      if (currentWeatherData) {
        const sanitizedWeather = WeatherService.sanitizeWeatherForFirestore(currentWeatherData);
        if (sanitizedWeather) {
          reportData.weather = sanitizedWeather;
        }
      }

      // Save to Firestore
      const db = getFirestore();
      await addDoc(collection(db, 'reports'), reportData);

      showAlert({
        type: 'success',
        message: pt.reportSubmitted,
        onConfirm: () => {
          navigateToLocationTab();
        }
      });

    } catch (error) {
      console.error('Report submission error:', error);
      showAlert({
        type: 'error',
        message: pt.reportError,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleModalSubmit = () => {
    if (reportRef.current) {
      reportRef.current.submit();
    }
  };

  // Custom hideAlert that handles navigation
  const handleHideAlert = () => {
    hideAlert();
    if (shouldNavigateAfterAlert) {
      setShouldNavigateAfterAlert(false);
      navigateToLocationTab();
    }
  };

  const handleStartMonitoring = async (protocol: TecnicoProtocol, startTime: Date, weatherData?: any, observersCount?: number, reportName?: string, selectedTrajectory?: TrajectoryData | null) => {

    // Set monitoring state IMMEDIATELY (synchronously)
    setMonitoringProtocol(protocol);
    setMonitoringStartTime(startTime);
    setMonitoringWeatherData(weatherData || null);
    setMonitoringObserversCount(observersCount || 1);
    setSelectedTrajectory(selectedTrajectory || null);
    
    setIsMonitoring(true);
    
    // Save to AsyncStorage asynchronously (but don't block the UI transition)
    try {
      const sessionData = {
        protocol,
        startTime: startTime.toISOString(),
        userId: user?.uid,
        weatherData: weatherData || null,
        observersCount: observersCount || 1,
        reportName: reportName || null,
        selectedTrajectory: selectedTrajectory || null,
      };
      
      await AsyncStorage.setItem('activeMonitoringSession', JSON.stringify(sessionData));
    } catch (error) {
      console.error('Error saving active monitoring session:', error);
    }
  };

  const handleTerminateMonitoring = async () => {
    try {
      // Clear all monitoring-related data
      await AsyncStorage.removeItem('activeMonitoringSession');
      console.log('🗑️ Active monitoring session cleared');
      
      await AsyncStorage.removeItem('currentMonitoringSession');
      console.log('🗑️ Current monitoring session cleared');
      
      await AsyncStorage.removeItem('monitoringData');
      console.log('🗑️ Monitoring data cleared');
      
      // Clear offline monitoring data if any
      await AsyncStorage.removeItem('offlineMonitoringSessions');
      await AsyncStorage.removeItem('offlineContactEvents');
      await AsyncStorage.removeItem('offlineGPSPoints');
      console.log('🗑️ Offline monitoring data cleared');
      
    } catch (error) {
      console.error('Error clearing monitoring session:', error);
    }
    
    setIsMonitoring(false);
    setMonitoringProtocol(null);
    setMonitoringStartTime(null);
    setMonitoringWeatherData(null);
    setMonitoringObserversCount(1);
    setSelectedTrajectory(null);
    setCurrentStep('weather');
    
    // Reset status bar to default
    console.log('🔄 Report: Resetting status bar after monitoring termination');
    
    // Navigate to index.tsx
    router.replace('/');
    
    // Show success message after navigation
    setTimeout(() => {
      showAlert({
        type: 'success',
        message: 'Monitorização terminada com sucesso.',
      });
    }, 500);
  };

  const handleStepChange = async (step: 'protocol' | 'weather' | 'trajectory' | 'monitoring') => {
    setCurrentStep(step);
    
    // Note: TecnicosReport handles starting monitoring via onStartMonitoring prop
    // No need to call handleStartMonitoring here as it creates duplicate calls
  };

  // If monitoring is active, render ActiveMonitoring as full screen
  if (isMonitoring && monitoringProtocol && monitoringStartTime) {
    
    return (
      <ActiveMonitoring
        protocol={monitoringProtocol}
        startTime={monitoringStartTime}
        weatherData={monitoringWeatherData}
        observersCount={monitoringObserversCount}
        selectedTrajectory={selectedTrajectory}
        onTerminate={handleTerminateMonitoring}
      />
    );
  }

  // Determine which report component to show based on user role
  const renderReportComponent = () => {
    // Show loading while initializing
    if (isInitializing) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0996a8" />
          <Text style={styles.loadingText}>A carregar...</Text>
        </View>
      );
    }

    // Ensure we have a valid user role before rendering
    if (!userRole) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0996a8" />
          <Text style={styles.loadingText}>A verificar permissões...</Text>
        </View>
      );
    }

    if (userRole === 'tecnico_prorola') {
      return (
        <TecnicosReport
          ref={reportRef}
          onSubmit={handleSubmit}
          isLoading={isLoading}
          isUploadingImages={isUploadingImages}
          uploadProgress={uploadProgress}
          weatherData={weatherData}
          showAlert={showAlert}
          onProtocolChange={setSelectedProtocol}
          onStepChange={handleStepChange}
          onStartMonitoring={handleStartMonitoring}
          onCancel={() => navigateToLocationTab()}
        />
      );
    }
    
    // Default to ColaboradorReport for all other roles
    return (
      <ColaboradorReport
        ref={reportRef}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        isUploadingImages={isUploadingImages}
        uploadProgress={uploadProgress}
        weatherData={weatherData}
        showAlert={showAlert}
      />
    );
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.container}>
        <SafeSystemBars 
          style="light" 
          translucent={true} 
          backgroundColor="#0996a8"
          navigationBarColor="#0996a8"
        />
        
        {/* Header structure matching tab layout exactly */}
        <View style={styles.header}>
          <SafeAreaView edges={['top']} style={styles.headerSafeArea}>
            <View style={styles.headerSpacing} />
          </SafeAreaView>
          
          {/* Header Logo - positioned exactly like tab layout */}
          <View style={styles.headerLogo}>
            <Image 
              source={require('../assets/images/header-logo.png')}
              style={styles.logoImage}
            />
          </View>
          
          {/* Title Bar - matching tab layout structure */}
          <View style={styles.headerContent}>
            <Text style={styles.titleText}>{pt.newReport}</Text>
            <NetworkStatusIndicator style={{
              marginTop: 2,
              transform: [{ scale: 0.8 }],
            }} />
          </View>
        </View>

        {/* Scrollable content area */}
        <View style={styles.contentWrapper}>
          {renderReportComponent()}
        </View>

        {/* Fixed buttons at bottom - show for colaborador reports or when on weather step for tecnico reports */}
        {(userRole !== 'tecnico_prorola' || currentStep === 'weather') && (
          <SafeAreaView edges={['bottom']} style={styles.buttonsFixed}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={() => navigateToLocationTab()}
              disabled={isLoading || isUploadingImages}>
              <FontAwesome name="times" size={16} color="#FFFFFF" style={styles.buttonIcon} />
              <Text style={[styles.buttonText, styles.cancelButtonText]}>{pt.cancel}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.submitButton]}
              onPress={handleModalSubmit}
              disabled={isLoading || isUploadingImages}>
              {(isLoading || isUploadingImages) ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator color="#FFFFFF" />
                </View>
              ) : (
                <>
                  <FontAwesome 
                    name={userRole === 'tecnico_prorola' && currentStep === 'weather' ? 'play' : 'check'} 
                    size={16} 
                    color="#FFFFFF" 
                    style={styles.buttonIcon} 
                  />
                  <Text style={[styles.buttonText, styles.submitButtonText]}>
                    {userRole === 'tecnico_prorola' && currentStep === 'weather' ? pt.iniciar : pt.submit}
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </SafeAreaView>
        )}
      </View>

      <CustomAlert
        visible={isVisible}
        type={config.type}
        title={config.title}
        message={config.message}
        onClose={handleHideAlert}
        onConfirm={config.onConfirm}
      />

      {/* Upload Progress Modal */}
      <Modal
        visible={isUploadingImages}
        transparent={true}
        animationType="fade">
        <View style={styles.progressModalContainer}>
          <View style={styles.progressModalContent}>
            <View style={styles.progressModalHeader}>
              <FontAwesome name="cloud-upload" size={32} color="#0996a8" />
              <Text style={styles.progressModalTitle}>{pt.uploadingImages}</Text>
            </View>
            
            <View style={styles.progressBarContainer}>
              <View style={[styles.progressBar, { width: `${uploadProgress}%` }]} />
            </View>
            
            <Text style={styles.progressText}>
              {Math.round(uploadProgress)}%
            </Text>
            
            <ActivityIndicator 
              size="large" 
              color="#0996a8" 
              style={styles.progressSpinner}
            />
          </View>
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    backgroundColor: '#0996a8',
  },
  headerSafeArea: {
    backgroundColor: '#0996a8',
  },
  headerSpacing: {
    height: 40, // Match tab layout spacing
  },
  headerLogo: {
    position: 'absolute',
    left: 12,
    top: 2,
    zIndex: 1,
  },
  logoImage: {
    width: 90,
    height: 90,
    resizeMode: 'contain',
  },
  headerContent: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    height: 45,
    paddingTop: 0,
    position: 'relative',
  },
  titleText: {
    fontSize: 15,
    color: '#666',
    fontWeight: '500',
  },
  contentWrapper: {
    flex: 1,
    paddingHorizontal: 20,
  },
  buttonsFixed: {
    flexDirection: 'row',
    gap: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    backgroundColor: '#FFFFFF',
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 15,
    borderRadius: 10,
  },
  cancelButton: {
    backgroundColor: '#757575',
  },
  submitButton: {
    backgroundColor: '#0996a8',
  },
  buttonIcon: {
    marginRight: 10,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButtonText: {
    color: '#fff',
  },
  submitButtonText: {
    color: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0996a8',
    marginTop: 20,
  },
  progressModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  progressModalContent: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxWidth: 400,
  },
  progressModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    marginBottom: 20,
  },
  progressModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0996a8',
  },
  progressBarContainer: {
    height: 20,
    backgroundColor: '#E0E0E0',
    borderRadius: 10,
    marginBottom: 10,
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#0996a8',
    borderRadius: 10,
  },
  progressText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0996a8',
    marginBottom: 10,
  },
  progressSpinner: {
    marginTop: 20,
  },
}); 