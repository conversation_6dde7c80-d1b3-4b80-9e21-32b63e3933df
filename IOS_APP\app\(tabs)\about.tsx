import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import SafeSystemBars from '@/components/SafeSystemBars';
import { router } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

export default function AboutScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <SafeSystemBars style="light" />
      {/* Original: <StatusBar style="light" /> */}

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >

        {/* Main Title */}
        <Text style={styles.title}>
          Plano de Recuperação e Conservação da Rola-Comum
        </Text>

        {/* About Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sobre a Aplicação ProROLA</Text>
          <Text style={styles.description}>
            A aplicação ProROLA é uma ferramenta científica móvel desenvolvida para apoiar a monitorização 
            e conservação da rola-comum (Streptopelia turtur) em Portugal. Esta aplicação permite aos 
            técnicos de campo, gestores de zonas de caça e colaboradores registar observações, criar 
            trajetos de monitorização, capturar fotografias geolocalizadas e recolher dados científicos 
            essenciais para a conservação desta espécie em declínio.
          </Text>
          <Text style={[styles.description, { marginTop: 15 }]}>
            A rola-comum é o único columbiforme migrador obrigatório de longo curso transahariano que 
            nidifica na região do Paleárctico ocidental. A conservação desta espécie constitui um enorme 
            desafio devido à escala espacial da sua distribuição estival (mais de sete milhões de 
            quilómetros quadrados) e à interação de múltiplos factores. Este projeto visa implementar 
            medidas para a conservação e recuperação desta espécie através de tecnologia móvel e 
            recolha de dados científicos.
          </Text>
        </View>

        {/* App Features Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Funcionalidades da Aplicação</Text>
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <FontAwesome name="map-marker" size={16} color="#0996a8" style={styles.featureIcon} />
              <Text style={styles.featureText}>
                Criação e gravação de trajetos de monitorização com GPS
              </Text>
            </View>
            <View style={styles.featureItem}>
              <FontAwesome name="camera" size={16} color="#0996a8" style={styles.featureIcon} />
              <Text style={styles.featureText}>
                Captura de fotografias geolocalizadas da fauna
              </Text>
            </View>
            <View style={styles.featureItem}>
              <FontAwesome name="edit" size={16} color="#0996a8" style={styles.featureIcon} />
              <Text style={styles.featureText}>
                Registo de observações e contactos com a rola-comum
              </Text>
            </View>
            <View style={styles.featureItem}>
              <FontAwesome name="cloud" size={16} color="#0996a8" style={styles.featureIcon} />
              <Text style={styles.featureText}>
                Sincronização de dados com plataforma web
              </Text>
            </View>
            <View style={styles.featureItem}>
              <FontAwesome name="users" size={16} color="#0996a8" style={styles.featureIcon} />
              <Text style={styles.featureText}>
                Gestão de utilizadores: técnicos, gestores e colaboradores
              </Text>
            </View>
            <View style={styles.featureItem}>
              <FontAwesome name="bar-chart" size={16} color="#0996a8" style={styles.featureIcon} />
              <Text style={styles.featureText}>
                Relatórios científicos e análise de dados
              </Text>
            </View>
          </View>
        </View>

        {/* Objectives Section */}
        <Text style={styles.subtitle}>Objetivos do Projeto</Text>
        <View style={styles.objectivesContainer}>
          <View style={styles.objectiveItem}>
            <Text style={styles.objectiveText}>
              Aumentar o conhecimento sobre a rola-comum, adaptado às necessidades atuais de gestão adaptativa da espécie
            </Text>
          </View>
          <View style={styles.objectiveItem}>
            <Text style={styles.objectiveText}>
              Promover a qualidade dos habitats
            </Text>
          </View>
          <View style={styles.objectiveItem}>
            <Text style={styles.objectiveText}>
              Desenvolver uma estratégia de caça sustentável e adaptativa
            </Text>
          </View>
          <View style={styles.objectiveItem}>
            <Text style={styles.objectiveText}>
              Promover a cooperação internacional
            </Text>
          </View>
          <View style={styles.objectiveItem}>
            <Text style={styles.objectiveText}>
              Desenvolver uma estratégia de comunicação dirigida a proprietários, agricultores, atores cinegéticos e o público em geral
            </Text>
          </View>
        </View>

        {/* Activities Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Atividades</Text>
          <Text style={styles.description}>
            A monitorização das populações durante as jornadas de caça e fora do período venatório à espécie 
            deverá ser realizada através de uma abordagem científica, implementada por equipas técnicas 
            multidisciplinares, e complementada através do reforço da recolha de dados de exploração e do 
            apoio direto e indireto de caçadores e membros da sociedade civil, numa perspectiva de ciência-cidadã. 
            As ações preconizadas incluem definição de protocolos padronizados, esquemas de emergência de gestão 
            de habitat e de recursos alimentares, monitorização para aferir a origem e condição corporal dos exemplares.
          </Text>
          <Text style={[styles.description, { marginTop: 15 }]}>
            Considerando que o problema da redução destas populações não é apenas nacional, mas de dimensão supra 
            fronteiriça, a redução do número, duração e limites de abate das jornadas de caça deverá ter impacto, 
            no mínimo, à escala da Península Ibérica. As medidas a implementar serão revistas à luz dos resultados 
            obtidos nos estudos de monitorização e concertadas com entidades congéneres espanholas, num esforço 
            Ibérico de recuperação da rola-comum.
          </Text>
        </View>

        {/* Entities Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Entidade Coordenadora</Text>
          <Text style={styles.entityText}>
            Instituto da Conservação da Natureza e das Florestas (ICNF)
          </Text>
          <Text style={styles.entitySubtext}>
            Em nome do Centro de Competências para o Estudo, Gestão e Sustentabilidade das Espécies Cinegéticas e Biodiversidade
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Investigador Responsável</Text>
          <Text style={styles.entityText}>
            Mónica Vieira Cunha
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Entidades Participantes</Text>
          <View style={styles.entitiesList}>
            <View style={styles.entityItem}>
              <Text style={styles.entityItemText}>
                Instituto Nacional de Investigação Agrária e Veterinária, I. P. (INIAV, I. P.)
              </Text>
            </View>
            <View style={styles.entityItem}>
              <Text style={styles.entityItemText}>
                Associação Nacional de Proprietários Rurais, Gestão Cinegética e Biodiversidade (ANPC)
              </Text>
            </View>
            <View style={styles.entityItem}>
              <Text style={styles.entityItemText}>
                Confederação Nacional dos Caçadores Portugueses (CNCP)
              </Text>
            </View>
            <View style={styles.entityItem}>
              <Text style={styles.entityItemText}>
                Federação Portuguesa de Caça (FENCAÇA)
              </Text>
            </View>
            <View style={styles.entityItem}>
              <Text style={styles.entityItemText}>
                Coligação C6
              </Text>
            </View>
            <View style={styles.entityItem}>
              <Text style={styles.entityItemText}>
                Instituto Superior de Agronomia, Centro de Ecologia Aplicada "Prof. Baeta Neves" (ISA/CEABN)
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fcfc',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingTop: 20,
    paddingBottom: 80, // Add extra padding for tab bar
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#0996a8',
    textAlign: 'center',
    lineHeight: 32,
    marginBottom: 30,
  },
  subtitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#0996a8',
    textAlign: 'center',
    marginBottom: 20,
    paddingBottom: 10,
    borderBottomWidth: 3,
    borderBottomColor: '#0996a8',
    alignSelf: 'center',
    paddingHorizontal: 20,
  },
  section: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0996a8',
    textAlign: 'center',
    marginBottom: 15,
  },
  description: {
    fontSize: 15,
    lineHeight: 24,
    color: '#333',
    textAlign: 'justify',
  },
  objectivesContainer: {
    marginBottom: 30,
  },
  objectiveItem: {
    backgroundColor: '#ffffff',
    borderLeftWidth: 4,
    borderLeftColor: '#0996a8',
    padding: 18,
    borderRadius: 8,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  objectiveText: {
    fontSize: 15,
    lineHeight: 22,
    color: '#333',
  },
  entityText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  entitySubtext: {
    fontSize: 13,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  entitiesList: {
    gap: 10,
  },
  entityItem: {
    backgroundColor: '#ffffff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  entityItemText: {
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
  featuresList: {
    gap: 10,
  },
  featureItem: {
    backgroundColor: '#ffffff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  featureIcon: {
    marginRight: 10,
  },
  featureText: {
    fontSize: 14,
    color: '#333',
  },
}); 