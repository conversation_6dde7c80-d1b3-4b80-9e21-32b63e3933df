import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  FlatList,
  Al<PERSON>,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome } from '@expo/vector-icons';
import { useAuth } from '@/contexts/AuthContext';
import { getFirestore, collection, query, where, getDocs, orderBy } from 'firebase/firestore';

interface TrajectoryData {
  id: string;
  name: string;
  description?: string;
  coordinates: Array<{ lat: number; lng: number }>;
  distance?: string;
  pointsCount?: number;
  createdAt: string;
  source: 'kmz' | 'manual';
  originalFileName?: string;
}

interface TechnicianTrajectorySelectionProps {
  visible: boolean;
  onClose: () => void;
  onSelectTrajectory: (trajectory: TrajectoryData | null) => void;
  onContinueWithoutTrajectory: () => void;
}

const pt = {
  title: 'Carregar Trajeto Existente',
  subtitle: 'Deseja carregar um trajeto previamente criado?',
  loadTrajectory: 'Sim, carregar trajeto',
  continueWithout: 'Não, trajeto novo',
  selectTrajectory: 'Selecionar Trajeto',
  noTrajectoriesFound: 'Não foram encontrados trajetos',
  noTrajectoriesMessage: 'Ainda não criou nenhum trajeto. Pode criar trajetos na secção "Trajetos" do painel técnico.',
  loading: 'A carregar trajetos...',
  error: 'Erro ao carregar trajetos',
  retry: 'Tentar novamente',
  cancel: 'Cancelar',
  select: 'Selecionar',
  back: 'Voltar',
  kmzTrajectory: 'Trajeto KMZ',
  manualTrajectory: 'Trajeto Manual',
  createdOn: 'Criado em',
  points: 'pontos',
  distance: 'Distância',
  trajectoryDetails: 'Detalhes do Trajeto',
  confirmSelection: 'Confirmar Seleção',
  confirmSelectionMessage: 'Deseja carregar este trajeto no mapa?',
  confirmSelectionTitle: 'Carregar Trajeto',
  yes: 'Sim',
  no: 'Não',
};

export default function TechnicianTrajectorySelection({
  visible,
  onClose,
  onSelectTrajectory,
  onContinueWithoutTrajectory,
}: TechnicianTrajectorySelectionProps) {
  const { user } = useAuth();
  const [step, setStep] = useState<'choice' | 'list'>('choice');
  const [trajectories, setTrajectories] = useState<TrajectoryData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedTrajectoryForConfirm, setSelectedTrajectoryForConfirm] = useState<TrajectoryData | null>(null);

  // Reset state when modal opens
  useEffect(() => {
    if (visible) {
      setStep('choice');
      setTrajectories([]);
      setError(null);
    }
  }, [visible]);

  const loadTrajectories = async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const db = getFirestore();
      const allTrajectories: TrajectoryData[] = [];

      // Load KMZ trajectories (without orderBy to avoid index requirement)
      const kmzQuery = query(
        collection(db, 'tecnicosTrajetosKmz'),
        where('createdBy', '==', user.uid)
      );
      
      const kmzSnapshot = await getDocs(kmzQuery);
      kmzSnapshot.forEach((doc) => {
        const data = doc.data();
        allTrajectories.push({
          id: doc.id,
          name: data.name || data.originalFileName || 'Trajeto KMZ',
          description: data.description,
          coordinates: data.coordinates || [],
          distance: data.distance,
          pointsCount: data.pointsCount || data.coordinates?.length || 0,
          createdAt: data.createdAt || data.importedAt || '',
          source: 'kmz',
          originalFileName: data.originalFileName,
        });
      });

      // Load Manual trajectories from tecnicosTrajetosManualList only (without orderBy to avoid index requirement)
      const manualQuery = query(
        collection(db, 'tecnicosTrajetosManualList'),
        where('createdBy', '==', user.uid)
      );
      
      // Load from tecnicosTrajetosManualList
      const manualSnapshot = await getDocs(manualQuery);
      manualSnapshot.forEach((doc) => {
        const data = doc.data();
        allTrajectories.push({
          id: doc.id,
          name: data.name || 'Trajeto Manual',
          description: data.description,
          coordinates: data.coordinates || [],
          distance: data.distance,
          pointsCount: data.pointsCount || data.coordinates?.length || 0,
          createdAt: data.createdAt || '',
          source: 'manual',
        });
      });

      // Sort all trajectories by creation date (newest first)
      allTrajectories.sort((a, b) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return dateB - dateA;
      });

      setTrajectories(allTrajectories);
      setStep('list');
    } catch (err) {
      console.error('Error loading trajectories:', err);
      setError('Erro ao carregar trajetos');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectTrajectory = (trajectory: TrajectoryData) => {
    setSelectedTrajectoryForConfirm(trajectory);
    setShowConfirmModal(true);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('pt-PT', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  };

  const renderTrajectoryItem = ({ item }: { item: TrajectoryData }) => (
    <TouchableOpacity
      style={styles.trajectoryItem}
      onPress={() => handleSelectTrajectory(item)}
    >
      <View style={styles.trajectoryHeader}>
        <View style={styles.trajectoryTitleContainer}>
          <FontAwesome
            name={item.source === 'kmz' ? 'file' : 'map-marker'}
            size={16}
            color="#0a7ea4"
            style={styles.trajectoryIcon}
          />
          <Text style={styles.trajectoryName} numberOfLines={1}>
            {item.name}
          </Text>
        </View>
        <View style={styles.trajectoryBadge}>
          <Text style={styles.trajectoryBadgeText}>
            {item.source === 'kmz' ? pt.kmzTrajectory : pt.manualTrajectory}
          </Text>
        </View>
      </View>
      
      {item.description && (
        <Text style={styles.trajectoryDescription} numberOfLines={2}>
          {item.description}
        </Text>
      )}
      
      <View style={styles.trajectoryDetails}>
        <View style={styles.trajectoryDetailItem}>
          <FontAwesome name="calendar" size={12} color="#666" />
          <Text style={styles.trajectoryDetailText}>
            {formatDate(item.createdAt)}
          </Text>
        </View>
        
        <View style={styles.trajectoryDetailItem}>
          <FontAwesome name="map-pin" size={12} color="#666" />
          <Text style={styles.trajectoryDetailText}>
            {item.pointsCount} {pt.points}
          </Text>
        </View>
        
        {item.distance && (
          <View style={styles.trajectoryDetailItem}>
            <FontAwesome name="road" size={12} color="#666" />
            <Text style={styles.trajectoryDetailText}>
              {item.distance}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderChoiceStep = () => (
    <View style={styles.choiceContainer}>
      <View style={styles.iconContainer}>
        <FontAwesome name="map" size={48} color="#0a7ea4" />
      </View>
      <Text style={styles.title}>{pt.title}</Text>
      <Text style={styles.subtitle}>{pt.subtitle}</Text>
      
      <View style={styles.choiceButtons}>
        <TouchableOpacity
          style={[styles.choiceButton, styles.primaryButton]}
          onPress={loadTrajectories}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <View style={styles.buttonIconContainer}>
                <FontAwesome name="download" size={20} color="#fff" />
              </View>
              <Text style={styles.primaryButtonText}>{pt.loadTrajectory}</Text>
            </>
          )}
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.choiceButton, styles.secondaryButton]}
          onPress={onContinueWithoutTrajectory}
        >
          <View style={styles.buttonIconContainer}>
            <FontAwesome name="plus" size={20} color="#0a7ea4" />
          </View>
          <Text style={styles.secondaryButtonText}>{pt.continueWithout}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderListStep = () => (
    <View style={styles.listContainer}>
      <View style={styles.listHeader}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => setStep('choice')}
        >
          <FontAwesome name="arrow-left" size={20} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.listTitle}>{pt.selectTrajectory}</Text>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <FontAwesome name="times" size={20} color="#fff" />
        </TouchableOpacity>
      </View>
      
      {error ? (
        <View style={styles.errorContainer}>
          <FontAwesome name="exclamation-triangle" size={24} color="#ef4444" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadTrajectories}>
            <Text style={styles.retryButtonText}>{pt.retry}</Text>
          </TouchableOpacity>
        </View>
      ) : trajectories.length === 0 ? (
        <View style={styles.emptyContainer}>
          <FontAwesome name="map-o" size={48} color="#ccc" />
          <Text style={styles.emptyTitle}>{pt.noTrajectoriesFound}</Text>
          <Text style={styles.emptyMessage}>{pt.noTrajectoriesMessage}</Text>
        </View>
      ) : (
        <FlatList
          data={trajectories}
          keyExtractor={(item) => `${item.source}-${item.id}`}
          renderItem={renderTrajectoryItem}
          style={styles.trajectoryList}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );

  return (
    <>
      <Modal
        visible={visible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={onClose}
      >
        <SafeAreaView style={styles.container}>
          {step === 'choice' ? renderChoiceStep() : renderListStep()}
        </SafeAreaView>
      </Modal>
      
      {/* Modern Confirmation Modal */}
      <Modal
        visible={showConfirmModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowConfirmModal(false)}
      >
        <View style={styles.confirmModalOverlay}>
          <View style={styles.confirmModalContainer}>
            <View style={styles.confirmModalHeader}>
              <FontAwesome name="map" size={24} color="#0a7ea4" />
              <Text style={styles.confirmModalTitle}>{pt.confirmSelectionTitle}</Text>
            </View>
            
            <View style={styles.confirmModalContent}>
              <Text style={styles.confirmModalTrajectoryName}>
                {selectedTrajectoryForConfirm?.name}
              </Text>
              <Text style={styles.confirmModalMessage}>
                {pt.confirmSelectionMessage}
              </Text>
              
              <View style={styles.confirmModalDetails}>
                <View style={styles.confirmModalDetailItem}>
                  <FontAwesome name="map-pin" size={14} color="#64748b" />
                  <Text style={styles.confirmModalDetailText}>
                    {selectedTrajectoryForConfirm?.pointsCount} pontos
                  </Text>
                </View>
                {selectedTrajectoryForConfirm?.distance && (
                  <View style={styles.confirmModalDetailItem}>
                    <FontAwesome name="road" size={14} color="#64748b" />
                    <Text style={styles.confirmModalDetailText}>
                      {selectedTrajectoryForConfirm.distance}
                    </Text>
                  </View>
                )}
              </View>
            </View>
            
            <View style={styles.confirmModalButtons}>
              <TouchableOpacity
                style={[styles.confirmModalButton, styles.confirmModalCancelButton]}
                onPress={() => setShowConfirmModal(false)}
              >
                <Text style={styles.confirmModalCancelButtonText}>{pt.no}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.confirmModalButton, styles.confirmModalConfirmButton]}
                onPress={() => {
                  setShowConfirmModal(false);
                  if (selectedTrajectoryForConfirm) {
                    onSelectTrajectory(selectedTrajectoryForConfirm);
                  }
                }}
              >
                <Text style={styles.confirmModalConfirmButtonText}>{pt.yes}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  // Choice step styles
  choiceContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    backgroundColor: '#f8fafc',
  },
  iconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    backgroundColor: '#f0f9ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    borderWidth: 3,
    borderColor: '#bfdbfe',
    shadowColor: '#0a7ea4',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  title: {
    fontSize: 32,
    fontWeight: '800',
    color: '#0a7ea4',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: -0.8,
    lineHeight: 38,
  },
  subtitle: {
    fontSize: 18,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 48,
    lineHeight: 28,
    fontWeight: '400',
    paddingHorizontal: 8,
  },
  choiceButtons: {
    width: '100%',
    gap: 16,
  },
  choiceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 32,
    borderRadius: 20,
    gap: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    minHeight: 64,
  },
  buttonIconContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    flexShrink: 0, // Prevent container from shrinking
  },
  primaryButton: {
    backgroundColor: '#0a7ea4',
    borderWidth: 0,
    shadowColor: '#0a7ea4',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 0.2,
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#e2e8f0',
    shadowColor: '#64748b',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 3,
  },
  secondaryButtonText: {
    color: '#0a7ea4',
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  // List step styles
  listContainer: {
    flex: 1,
  },
  listHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#0a7ea4',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backButton: {
    padding: 10,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  listTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
    letterSpacing: -0.3,
  },
  closeButton: {
    padding: 10,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  trajectoryList: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  trajectoryItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  trajectoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  trajectoryTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  trajectoryIcon: {
    marginRight: 8,
  },
  trajectoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    flex: 1,
  },
  trajectoryBadge: {
    backgroundColor: '#f0f9ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#0ea5e9',
  },
  trajectoryBadgeText: {
    fontSize: 12,
    color: '#0ea5e9',
    fontWeight: '500',
  },
  trajectoryDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 12,
    lineHeight: 20,
  },
  trajectoryDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  trajectoryDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  trajectoryDetailText: {
    fontSize: 12,
    color: '#6b7280',
  },
  // Empty and error states
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6b7280',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    marginTop: 12,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#ef4444',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  // Confirmation Modal Styles
  confirmModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  confirmModalContainer: {
    backgroundColor: '#fff',
    borderRadius: 20,
    width: '100%',
    maxWidth: 350,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  confirmModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 24,
    paddingBottom: 16,
    gap: 12,
  },
  confirmModalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#0a7ea4',
    letterSpacing: -0.3,
  },
  confirmModalContent: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  confirmModalTrajectoryName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 8,
  },
  confirmModalMessage: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  confirmModalDetails: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
    marginBottom: 8,
  },
  confirmModalDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  confirmModalDetailText: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '500',
  },
  confirmModalButtons: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#f1f5f9',
  },
  confirmModalButton: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmModalCancelButton: {
    borderRightWidth: 1,
    borderRightColor: '#f1f5f9',
  },
  confirmModalCancelButtonText: {
    fontSize: 17,
    fontWeight: '600',
    color: '#64748b',
  },
  confirmModalConfirmButton: {
    backgroundColor: 'transparent',
  },
  confirmModalConfirmButtonText: {
    fontSize: 17,
    fontWeight: '700',
    color: '#0a7ea4',
  },
}); 