import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, ActivityIndicator, Image, TextInput, Platform, Modal, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TecnicoProtocol } from '@/types/reports';
import { WeatherData, weatherService } from '@/services/weatherService';
import { FontAwesome } from '@expo/vector-icons';
import { useNetwork } from '@/contexts/NetworkContext';
import * as Location from 'expo-location';
import { useAuth } from '@/contexts/AuthContext';
import { getFirestore, collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/config/firebase';
import GestorActiveMonitoring from '../GestorActiveMonitoring';
import ContactPlacementModal from '../ContactPlacementModal';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StatusBar } from 'expo-status-bar';
// Portuguese translations for gestor report creation
const pt = {
  iniciar: 'Iniciar',
  weatherConditions: 'Indique as condições meteorológicas',
  fetchingWeather: 'A obter dados meteorológicos...',
  weatherError: 'Erro ao obter dados meteorológicos',
  currentWeather: 'Condições Atuais',
  manualWeatherSelection: 'Seleção Manual das Condições',
  temperature: 'Temperatura',
  humidity: 'Humidade',
  windSpeed: 'Vento',
  pressure: 'Pressão',
  visibility: 'Visibilidade',
  cloudiness: 'Nebulosidade',
  monitoringMessage: 'Está em condições de iniciar a monitorização e reportar?',
  weatherOptions: {
    ceuLimpo: 'Céu limpo',
    nublado: 'Nublado',
    chuva: 'Chuva',
    ventoModeradoForte: 'Vento moderado a forte',
    outroQual: 'Outro. Qual?',
  },
  createReport: 'Criar Relatório',
  cancel: 'Cancelar',
  reportTitle: 'Novo Relatório',
  reportDescription: 'Crie um relatório de gestão baseado em trajeto',
};

interface GestorReportCreationProps {
  visible: boolean;
  onClose: () => void;
  onReportCreated?: () => void;
}

// Custom GestorActiveMonitoring for reports (saves to gestoresReports collection)
const GestorReportMonitoring: React.FC<{
  protocol: TecnicoProtocol;
  startTime: Date;
  weatherData?: any;
  onTerminate: () => void;
}> = ({ protocol, startTime, weatherData, onTerminate }) => {
  const { user } = useAuth();
  
  // Create a report zone for creation
  const reportZoneId = `report_${Date.now()}`;
  const reportZoneName = `Relatório ${new Date().toLocaleDateString('pt-PT')}`;
  
  const handleTerminate = async () => {
    // Custom termination logic for reports - this will be handled by the 
    // modified GestorActiveMonitoring that saves to gestoresReports
    onTerminate();
  };

  return (
    <GestorActiveMonitoring
      protocol={protocol}
      startTime={startTime}
      weatherData={weatherData}
      zoneId={reportZoneId}
      zoneName={reportZoneName}
      onTerminate={handleTerminate}
      // Add a flag to indicate this is for report creation
      isReportMode={true}
    />
  );
};

const GestorReportCreation: React.FC<GestorReportCreationProps> = ({
  visible,
  onClose,
  onReportCreated
}) => {
  const { user } = useAuth();
  const { hasInternetConnection } = useNetwork();
  const scrollViewRef = useRef<ScrollView>(null);

  // Step management - only weather and monitoring for reports
  const [currentStep, setCurrentStep] = useState<'weather' | 'monitoring'>('weather');
  
  // Protocol is always 'trajeto' for report creation
  const selectedProtocol: TecnicoProtocol = 'trajeto';
  
  // Weather data
  const [currentWeatherData, setCurrentWeatherData] = useState<WeatherData | null>(null);
  const [isLoadingWeather, setIsLoadingWeather] = useState(false);
  const [selectedWeatherOptions, setSelectedWeatherOptions] = useState<Record<string, boolean>>({
    ceuLimpo: false,
    nublado: false,
    chuva: false,
    ventoModeradoForte: false,
    outroQual: false,
  });
  const [weatherOtherText, setWeatherOtherText] = useState('');
  


  // Monitoring data
  const [monitoringStartTime, setMonitoringStartTime] = useState<Date | null>(null);

  // Contact functionality
  const router = useRouter();
  const [showContactModal, setShowContactModal] = useState(false);
  const [currentContactData, setCurrentContactData] = useState<{
    observerLocation: {latitude: number, longitude: number};
    contactLocation: {latitude: number, longitude: number};
    distance: number;
    bearing: number;
  } | null>(null);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [currentMapType, setCurrentMapType] = useState<'standard' | 'satellite'>('standard');
  const [currentZoomLevel, setCurrentZoomLevel] = useState(17);
  const [currentBearing, setCurrentBearing] = useState(0);
  
  // Contact markers state - for displaying contacts on map
  const [contactMarkers, setContactMarkers] = useState<Array<{
    id: string;
    coordinate: { latitude: number; longitude: number };
    observerLocation: { latitude: number; longitude: number };
    contactNumber: number;
    timestamp: string;
  }>>([]);
  const [contactsCount, setContactsCount] = useState(0);
  const [monitoringSessionId] = useState(() => `gestor_report_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  // Reset state when modal opens
  useEffect(() => {
    if (visible && currentStep === 'weather' && !monitoringStartTime) {
      setCurrentWeatherData(null);
      setSelectedWeatherOptions({
        ceuLimpo: false,
        nublado: false,
        chuva: false,
        ventoModeradoForte: false,
        outroQual: false,
      });
      setWeatherOtherText('');

      
      setShowContactModal(false);
      setCurrentContactData(null);
      setUserLocation(null);
      setCurrentMapType('standard');
      setCurrentZoomLevel(17);
      setCurrentBearing(0);
      
      // Fetch weather data if we have internet
      if (hasInternetConnection()) {
        fetchWeatherData();
      }
    }
  }, [visible, hasInternetConnection, currentStep, monitoringStartTime]);

  // Reset to weather step when modal closes
  useEffect(() => {
    if (!visible) {
      setCurrentStep('weather');
      setMonitoringStartTime(null);
    }
  }, [visible]);

  // Track user location
  useEffect(() => {
    let locationSubscription: Location.LocationSubscription | undefined;

    const startLocationTracking = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          console.log('Location permission denied');
          return;
        }

        const initialLocation = await Location.getCurrentPositionAsync({});
        setUserLocation(initialLocation);

        locationSubscription = await Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.High,
            timeInterval: 2000,
            distanceInterval: 5,
          },
          (location) => {
            setUserLocation(location);
          }
        );
      } catch (error) {
        console.error('Error starting location tracking:', error);
      }
    };

    if (visible && currentStep === 'monitoring') {
      startLocationTracking();
    }

    return () => {
      if (locationSubscription) {
        locationSubscription.remove();
      }
    };
  }, [visible, currentStep]);

  const fetchWeatherData = async () => {
    if (!hasInternetConnection()) return;
    
    setIsLoadingWeather(true);
    try {
      // Get current location for weather
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission denied for weather');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const weather = await weatherService.getCurrentWeather(
        location.coords.latitude,
        location.coords.longitude
      );
      
      setCurrentWeatherData(weather);
    } catch (error) {
      console.error('Error fetching weather:', error);
    } finally {
      setIsLoadingWeather(false);
    }
  };

  const handleStartMonitoring = async () => {
    if (!user) return;

    // Validate weather conditions when offline
    if (!hasInternetConnection()) {
      const hasSelectedWeatherOption = Object.values(selectedWeatherOptions).some(value => value === true);
      if (!hasSelectedWeatherOption) {
        Alert.alert('Atenção', 'Por favor, selecione as condições meteorológicas antes de iniciar.');
        return;
      }
      
      if (selectedWeatherOptions.outroQual && (!weatherOtherText || weatherOtherText.trim() === '')) {
        Alert.alert('Atenção', 'Por favor, especifique as condições meteorológicas.');
        return;
      }
    }

    // Just start monitoring - don't create document until completion
    try {
      const startTime = new Date();
      setMonitoringStartTime(startTime);
      setCurrentStep('monitoring');
      
      console.log('🔄 GESTOR_REPORT: Starting monitoring for report creation');
    } catch (error) {
      console.error('Error starting monitoring:', error);
      Alert.alert('Erro', 'Erro ao iniciar monitorização. Tente novamente.');
    }
  };

  const handleWeatherOptionToggle = (option: string) => {
    if (!hasInternetConnection()) {
      // For offline mode, implement single selection
      const wasNothingSelected = !Object.values(selectedWeatherOptions).some(value => value === true);
      const isCurrentlySelected = selectedWeatherOptions[option];
      
      if (wasNothingSelected && !isCurrentlySelected) {
        // Selecting first option - reset all and select this one
        const resetOptions = Object.keys(selectedWeatherOptions).reduce((acc, key) => {
          acc[key] = false;
          return acc;
        }, {} as Record<string, boolean>);
        
        setSelectedWeatherOptions({
          ...resetOptions,
          [option]: true
        });
      } else if (isCurrentlySelected) {
        // Deselecting current option - reset all
        const resetOptions = Object.keys(selectedWeatherOptions).reduce((acc, key) => {
          acc[key] = false;
          return acc;
        }, {} as Record<string, boolean>);
        
        setSelectedWeatherOptions(resetOptions);
        
        // Clear other text if deselecting "outroQual"
        if (option === 'outroQual') {
          setWeatherOtherText('');
        }
      } else {
        // Selecting different option - reset all and select this one
        const resetOptions = Object.keys(selectedWeatherOptions).reduce((acc, key) => {
          acc[key] = false;
          return acc;
        }, {} as Record<string, boolean>);
        
        setSelectedWeatherOptions({
          ...resetOptions,
          [option]: true
        });
        
        // Clear other text if not selecting "outroQual"
        if (option !== 'outroQual') {
          setWeatherOtherText('');
        }
      }
    } else {
      // For online mode, keep the original multi-selection behavior
      setSelectedWeatherOptions(prev => ({
        ...prev,
        [option]: !prev[option]
      }));
      
      // Clear other text if not "outroQual"
      if (option !== 'outroQual') {
        setWeatherOtherText('');
      }
    }
  };

  const handleWeatherOtherTextChange = (text: string) => {
    setWeatherOtherText(text);
  };

  const handleMonitoringTerminate = () => {
    console.log('🔄 GESTOR_REPORT: Monitoring terminated, returning to parent');
    setCurrentStep('weather');
    setMonitoringStartTime(null);
    
    // Notify parent that report was created with a small delay to ensure proper state cleanup
    setTimeout(() => {
      if (onReportCreated) {
        onReportCreated();
      }
      onClose();
    }, 100);
  };

  const handleContactButtonPress = () => {
    setShowContactModal(true);
  };

  const handleContactModalClose = () => {
    setShowContactModal(false);
    setCurrentContactData(null);
  };

  const calculateBearing = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const dLng = lng2 - lng1;
    const y = Math.sin(dLng) * Math.cos(lat2);
    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng);
    const bearing = Math.atan2(y, x);
    return (bearing * 180 / Math.PI + 360) % 360;
  };

  const handleLocationPlacementConfirm = async (observerLocation: {latitude: number, longitude: number}, contactLocation: {latitude: number, longitude: number}, distance: number) => {
    // Handle contact placement for report creation
    console.log('🔄 GESTOR_REPORT: Contact placed for report', { observerLocation, contactLocation, distance });
    
    // Add contact to markers
    const newContact = {
      id: `contact_${Date.now()}`,
      coordinate: contactLocation,
      observerLocation: observerLocation,
      contactNumber: contactsCount + 1,
      timestamp: new Date().toISOString(),
    };
    
    setContactMarkers(prev => [...prev, newContact]);
    setContactsCount(prev => prev + 1);
    
    setShowContactModal(false);
    setCurrentContactData(null);
  };

  const loadContactMarkers = async () => {
    // Load existing contacts if any
    console.log('🔄 GESTOR_REPORT: Loading contact markers for report');
  };

  if (!visible) {
    return null;
  }

  if (currentStep === 'monitoring' && monitoringStartTime) {
    const weatherData = currentWeatherData ? {
      ...currentWeatherData,
      selectedOptions: selectedWeatherOptions,
      otherText: weatherOtherText,
    } : null;

    return (
      <Modal
        visible={visible}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={onClose}
      >
        <GestorReportMonitoring
          protocol={selectedProtocol}
          startTime={monitoringStartTime}
          weatherData={weatherData}
          onTerminate={handleMonitoringTerminate}
        />
      </Modal>
    );
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <View style={styles.container}>
        <StatusBar style="light" />
        {/* Header - new edge-to-edge design */}
        <View style={styles.header}>
          <SafeAreaView edges={['top']} style={styles.headerSafeArea}>
            <View style={styles.headerContent}>
              <Image 
                source={require('../../assets/images/header-logo.png')} 
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.title}>Novo Relatório</Text>
            </View>
          </SafeAreaView>
        </View>

        {/* Content wrapper */}
        <View style={styles.contentWrapper}>
          <ScrollView 
            ref={scrollViewRef}
            style={styles.scrollContent}
            contentContainerStyle={styles.scrollContentContainer}
            showsVerticalScrollIndicator={false}
            bounces={true}
            keyboardShouldPersistTaps="handled"
          >
            <WeatherConditions
              weatherData={currentWeatherData}
              isLoadingWeather={isLoadingWeather}
              hasInternet={hasInternetConnection()}
              selectedWeatherOptions={selectedWeatherOptions}
              onWeatherOptionToggle={handleWeatherOptionToggle}
              weatherOtherText={weatherOtherText}
              onWeatherOtherTextChange={handleWeatherOtherTextChange}
            />
          </ScrollView>
        </View>

        {/* Fixed buttons at bottom */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <FontAwesome name="times" size={16} color="#fff" style={{ marginRight: 8 }} />
            <Text style={styles.cancelButtonText}>{pt.cancel}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.continueButton}
            onPress={handleStartMonitoring}
            activeOpacity={0.7}
          >
            <FontAwesome name="play" size={16} color="#fff" style={{ marginRight: 8 }} />
            <Text style={styles.continueButtonText}>Iniciar</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

// Weather Conditions Component (reused from GestorTrajectoryCreation)
const WeatherConditions: React.FC<{
  weatherData: WeatherData | null;
  isLoadingWeather: boolean;
  hasInternet: boolean;
  selectedWeatherOptions: Record<string, boolean>;
  onWeatherOptionToggle: (option: string) => void;
  weatherOtherText: string;
  onWeatherOtherTextChange: (text: string) => void;
}> = ({ 
  weatherData, 
  isLoadingWeather, 
  hasInternet, 
  selectedWeatherOptions, 
  onWeatherOptionToggle,
  weatherOtherText,
  onWeatherOtherTextChange
}) => {
  const weatherOptions = [
    { key: 'ceuLimpo', label: pt.weatherOptions.ceuLimpo },
    { key: 'nublado', label: pt.weatherOptions.nublado },
    { key: 'chuva', label: pt.weatherOptions.chuva },
    { key: 'ventoModeradoForte', label: pt.weatherOptions.ventoModeradoForte },
    { key: 'outroQual', label: pt.weatherOptions.outroQual },
  ];

  // Helper function to get selected weather option
  const getSelectedWeatherOption = () => {
    const selected = Object.entries(selectedWeatherOptions).find(([key, value]) => 
      key !== 'outroQual' && value === true
    );
    
    if (selected) {
      const [key] = selected;
      return pt.weatherOptions[key as keyof typeof pt.weatherOptions] || key;
    }
    
    if (selectedWeatherOptions.outroQual && weatherOtherText) {
      return `${pt.weatherOptions.outroQual} ${weatherOtherText}`;
    }
    
    return null;
  };

  return (
    <View style={styles.weatherContainer}>
      {/* Title outside the card - matching Tecnicos layout */}
      <Text style={styles.sectionTitle}>
        Condições Meteorológicas
      </Text>

      {hasInternet && isLoadingWeather && (
        <View style={styles.weatherCard}>
          <View style={styles.weatherHeader}>
            <View style={styles.weatherIconContainer}>
              <ActivityIndicator size="small" color="rgba(255, 255, 255, 0.8)" />
            </View>
            <View style={styles.weatherMainInfo}>
              <View style={styles.loadingPlaceholder}>
                <ActivityIndicator size="small" color="rgba(255, 255, 255, 0.8)" />
                <Text style={styles.loadingPlaceholderText}>{pt.fetchingWeather}</Text>
              </View>
            </View>
          </View>

          <View style={styles.weatherDetailsGrid}>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.humidity}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.windSpeed}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.pressure}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
            <View style={styles.weatherDetailItem}>
              <View style={styles.weatherDetailIcon} />
              <Text style={styles.weatherDetailLabel}>{pt.visibility}</Text>
              <Text style={styles.weatherDetailValue}>--</Text>
            </View>
          </View>
        </View>
      )}

      {hasInternet && !isLoadingWeather && weatherData && (
        <View style={styles.weatherCard}>
          <View style={styles.weatherHeader}>
            <View style={styles.weatherIconContainer}>
              <Image
                source={{ uri: weatherService.getWeatherIconUrl(weatherData.icon) }}
                style={styles.weatherIconImage}
                resizeMode="contain"
              />
            </View>
            <View style={styles.weatherMainInfo}>
              <Text style={styles.weatherTemperature}>{weatherData.temperature}°C</Text>
              <Text style={styles.weatherDescription}>{weatherData.description}</Text>
            </View>
          </View>
        </View>
      )}

      {/* Weather details grid outside the main card - matching Tecnicos layout */}
      {hasInternet && !isLoadingWeather && weatherData && (
        <View style={styles.weatherDetailsGrid}>
          <View style={styles.weatherDetailItem}>
            <FontAwesome name="tint" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
            <Text style={styles.weatherDetailLabel}>{pt.humidity}</Text>
            <Text style={styles.weatherDetailValue}>{weatherData.humidity}%</Text>
          </View>
          <View style={styles.weatherDetailItem}>
            <FontAwesome name="flag" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
            <Text style={styles.weatherDetailLabel}>{pt.windSpeed}</Text>
            <Text style={styles.weatherDetailValue}>{weatherData.windSpeed} km/h</Text>
          </View>
          <View style={styles.weatherDetailItem}>
            <FontAwesome name="thermometer-half" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
            <Text style={styles.weatherDetailLabel}>{pt.pressure}</Text>
            <Text style={styles.weatherDetailValue}>{weatherData.pressure} hPa</Text>
          </View>
          <View style={styles.weatherDetailItem}>
            <FontAwesome name="eye" size={12} color="#0996a8" style={styles.weatherDetailIcon} />
            <Text style={styles.weatherDetailLabel}>{pt.visibility}</Text>
            <Text style={styles.weatherDetailValue}>{weatherData.visibility} km</Text>
          </View>
        </View>
      )}

      {/* Manual weather selection when offline */}
      {!hasInternet && (
        <View style={styles.manualWeatherContainer}>
          {weatherOptions.map((option) => (
            <TouchableOpacity
              key={option.key}
              style={[
                styles.weatherOptionRow,
                selectedWeatherOptions[option.key] && styles.weatherOptionSelected
              ]}
              onPress={() => onWeatherOptionToggle(option.key)}
              activeOpacity={0.7}
            >
              <View style={[
                styles.weatherRadio,
                selectedWeatherOptions[option.key] && styles.weatherRadioSelected
              ]}>
                {selectedWeatherOptions[option.key] && (
                  <View style={styles.weatherRadioDot} />
                )}
              </View>
              <Text style={[
                styles.weatherLabel,
                selectedWeatherOptions[option.key] && styles.weatherLabelSelected
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}

          {selectedWeatherOptions.outroQual && (
            <View style={styles.textInputContainer}>
              <TextInput
                style={styles.weatherTextInput}
                placeholder="Especifique as condições meteorológicas..."
                value={weatherOtherText}
                onChangeText={onWeatherOtherTextChange}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
          )}
        </View>
      )}

      {/* Monitoring confirmation message */}
      <View style={styles.monitoringMessageContainer}>
        <Text style={styles.monitoringMessage}>
          Está em condições de iniciar a monitorização e reportar?
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    backgroundColor: '#0996a8',
  },
  headerSafeArea: {
    backgroundColor: '#0996a8',
    paddingBottom: 10,
    paddingHorizontal: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  logo: {
    width: 80,
    height: 80,
    marginRight: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
    paddingHorizontal: 13,
  },
  contentWrapper: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: '#fff',
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingTop: 15,
    paddingBottom: 20,
  },
  content: {
    padding: 20,
  },
  description: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  weatherContainer: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 10,
    fontSize: 14,
    color: '#6b7280',
  },
  weatherCard: {
    backgroundColor: '#0996a8',
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
  },
  weatherHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  weatherIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  weatherIconImage: {
    width: 32,
    height: 32,
  },
  weatherMainInfo: {
    flex: 1,
  },
  weatherTemperature: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 2,
  },
  weatherDescription: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.9,
  },
  weatherDetailsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  weatherDetailItem: {
    flexDirection: 'column',
    alignItems: 'center',
    width: '23%',
    backgroundColor: '#f8fafc',
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
    marginBottom: 8,
  },
  weatherDetailLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
    textAlign: 'center',
  },
  weatherDetailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginTop: 2,
    textAlign: 'center',
  },
  weatherDetailIcon: {
    marginBottom: 4,
  },
  loadingPlaceholder: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingPlaceholderText: {
    marginLeft: 8,
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  manualWeatherContainer: {
    marginBottom: 20,
  },
  manualWeatherTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  weatherOptionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  weatherOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#d1d5db',
    backgroundColor: '#ffffff',
    marginBottom: 8,
  },
  weatherOptionSelected: {
    backgroundColor: '#0996a8',
    borderColor: '#0996a8',
  },
  weatherOptionText: {
    fontSize: 14,
    color: '#6b7280',
  },
  weatherOptionTextSelected: {
    color: '#ffffff',
  },
  weatherOtherInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#1f2937',
    marginTop: 12,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  weatherOptionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  weatherRadio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#d1d5db',
    backgroundColor: '#ffffff',
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  weatherRadioSelected: {
    borderColor: '#0996a8',
    backgroundColor: '#0996a8',
  },
  weatherRadioDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ffffff',
  },
  weatherLabel: {
    fontSize: 14,
    color: '#374151',
    flex: 1,
  },
  weatherLabelSelected: {
    color: '#0996a8',
    fontWeight: '600',
  },
  textInputContainer: {
    marginTop: 12,
  },
  weatherTextInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#1f2937',
    minHeight: 80,
    textAlignVertical: 'top',
    backgroundColor: '#ffffff',
  },
  monitoringMessageContainer: {
    marginTop: 20,
    marginBottom: 20,
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#0996a8',
  },
  actionContainer: {
    marginTop: 30,
  },
  startButton: {
    backgroundColor: '#0996a8',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
  },
  buttonIcon: {
    marginRight: 8,
  },
  startButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  monitoringMessage: {
    marginTop: 20,
    marginBottom: 20,
  },
  monitoringTextWrapper: {
    backgroundColor: '#f0f9ff',
    borderLeftWidth: 4,
    borderLeftColor: '#0996a8',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  monitoringTextContent: {
    fontSize: 16,
    color: '#1f2937',
    lineHeight: 24,
    textAlign: 'center',
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: Platform.OS === 'ios' ? 35 : 20,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 15,
    flexShrink: 0,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#6B7280',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  continueButton: {
    flex: 1,
    backgroundColor: '#0996a8',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default GestorReportCreation; 